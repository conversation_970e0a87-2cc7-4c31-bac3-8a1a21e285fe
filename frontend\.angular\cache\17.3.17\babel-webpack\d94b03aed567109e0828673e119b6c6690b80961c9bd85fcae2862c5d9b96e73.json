{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nexport const authGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  if (authService.isAuthenticated()) {\n    return true;\n  } else {\n    router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthService", "<PERSON>th<PERSON><PERSON>", "route", "state", "authService", "router", "isAuthenticated", "navigate", "queryParams", "returnUrl", "url"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\nimport { CanActivateFn, Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\nexport const authGuard: CanActivateFn = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n\n  if (authService.isAuthenticated()) {\n    return true;\n  } else {\n    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });\n    return false;\n  }\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,OAAO,MAAMC,SAAS,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACvD,MAAMC,WAAW,GAAGN,MAAM,CAACE,WAAW,CAAC;EACvC,MAAMK,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC;EAE7B,IAAIK,WAAW,CAACE,eAAe,EAAE,EAAE;IACjC,OAAO,IAAI;GACZ,MAAM;IACLD,MAAM,CAACE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAAEC,WAAW,EAAE;QAAEC,SAAS,EAAEN,KAAK,CAACO;MAAG;IAAE,CAAE,CAAC;IACtE,OAAO,KAAK;;AAEhB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}