{"ast": null, "code": "import { Subject } from '../Subject';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\nconst DEFAULT_CONFIG = {\n  connector: () => new Subject()\n};\nexport function connect(selector, config = DEFAULT_CONFIG) {\n  const {\n    connector\n  } = config;\n  return operate((source, subscriber) => {\n    const subject = connector();\n    innerFrom(selector(fromSubscribable(subject))).subscribe(subscriber);\n    subscriber.add(source.subscribe(subject));\n  });\n}", "map": {"version": 3, "names": ["Subject", "innerFrom", "operate", "fromSubscribable", "DEFAULT_CONFIG", "connector", "connect", "selector", "config", "source", "subscriber", "subject", "subscribe", "add"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/rxjs/dist/esm/internal/operators/connect.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\nconst DEFAULT_CONFIG = {\n    connector: () => new Subject(),\n};\nexport function connect(selector, config = DEFAULT_CONFIG) {\n    const { connector } = config;\n    return operate((source, subscriber) => {\n        const subject = connector();\n        innerFrom(selector(fromSubscribable(subject))).subscribe(subscriber);\n        subscriber.add(source.subscribe(subject));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,MAAMC,cAAc,GAAG;EACnBC,SAAS,EAAEA,CAAA,KAAM,IAAIL,OAAO,CAAC;AACjC,CAAC;AACD,OAAO,SAASM,OAAOA,CAACC,QAAQ,EAAEC,MAAM,GAAGJ,cAAc,EAAE;EACvD,MAAM;IAAEC;EAAU,CAAC,GAAGG,MAAM;EAC5B,OAAON,OAAO,CAAC,CAACO,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,OAAO,GAAGN,SAAS,CAAC,CAAC;IAC3BJ,SAAS,CAACM,QAAQ,CAACJ,gBAAgB,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS,CAACF,UAAU,CAAC;IACpEA,UAAU,CAACG,GAAG,CAACJ,MAAM,CAACG,SAAS,CAACD,OAAO,CAAC,CAAC;EAC7C,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}