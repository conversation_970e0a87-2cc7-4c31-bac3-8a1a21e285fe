{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { appConfig } from './app/app.config';\nbootstrapApplication(AppComponent, appConfig).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "appConfig", "catch", "err", "console", "error"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { appConfig } from './app/app.config';\n\nbootstrapApplication(AppComponent, appConfig)\n  .catch((err) => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAE5CF,oBAAoB,CAACC,YAAY,EAAEC,SAAS,CAAC,CAC1CC,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}