{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { catchError, throwError } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\nexport const errorInterceptor = (req, next) => {\n  const router = inject(Router);\n  const authService = inject(AuthService);\n  return next(req).pipe(catchError(error => {\n    if (error.status === 401) {\n      // Unauthorized - redirect to login\n      authService.logout();\n      router.navigate(['/login']);\n    } else if (error.status === 403) {\n      // Forbidden - redirect to unauthorized page\n      router.navigate(['/unauthorized']);\n    } else if (error.status === 0) {\n      // Network error\n      console.error('Network error occurred:', error);\n    } else {\n      // Other HTTP errors\n      console.error('HTTP error occurred:', error);\n    }\n    return throwError(() => error);\n  }));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}