{"ast": null, "code": "import { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nexport function scheduleReadableStreamLike(input, scheduler) {\n  return scheduleAsyncIterable(readableStreamLikeToAsyncGenerator(input), scheduler);\n}", "map": {"version": 3, "names": ["scheduleAsyncIterable", "readableStreamLikeToAsyncGenerator", "scheduleReadableStreamLike", "input", "scheduler"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduleReadableStreamLike.js"], "sourcesContent": ["import { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nexport function scheduleReadableStreamLike(input, scheduler) {\n    return scheduleAsyncIterable(readableStreamLikeToAsyncGenerator(input), scheduler);\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,kCAAkC,QAAQ,8BAA8B;AACjF,OAAO,SAASC,0BAA0BA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACzD,OAAOJ,qBAAqB,CAACC,kCAAkC,CAACE,KAAK,CAAC,EAAEC,SAAS,CAAC;AACtF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}