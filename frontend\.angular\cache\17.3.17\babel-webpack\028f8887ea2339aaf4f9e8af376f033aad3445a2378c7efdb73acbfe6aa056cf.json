{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { catchError, throwError } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\nexport const errorInterceptor = (req, next) => {\n  const router = inject(Router);\n  const authService = inject(AuthService);\n  return next(req).pipe(catchError(error => {\n    if (error.status === 401) {\n      // Unauthorized - redirect to login\n      authService.logout();\n      router.navigate(['/login']);\n    } else if (error.status === 403) {\n      // Forbidden - redirect to unauthorized page\n      router.navigate(['/unauthorized']);\n    } else if (error.status === 0) {\n      // Network error\n      console.error('Network error occurred:', error);\n    } else {\n      // Other HTTP errors\n      console.error('HTTP error occurred:', error);\n    }\n    return throwError(() => error);\n  }));\n};", "map": {"version": 3, "names": ["inject", "Router", "catchError", "throwError", "AuthService", "errorInterceptor", "req", "next", "router", "authService", "pipe", "error", "status", "logout", "navigate", "console"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\interceptors\\error.interceptor.ts"], "sourcesContent": ["import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { catchError, throwError } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\nexport const errorInterceptor: HttpInterceptorFn = (req, next) => {\n  const router = inject(Router);\n  const authService = inject(AuthService);\n\n  return next(req).pipe(\n    catchError((error: HttpErrorResponse) => {\n      if (error.status === 401) {\n        // Unauthorized - redirect to login\n        authService.logout();\n        router.navigate(['/login']);\n      } else if (error.status === 403) {\n        // Forbidden - redirect to unauthorized page\n        router.navigate(['/unauthorized']);\n      } else if (error.status === 0) {\n        // Network error\n        console.error('Network error occurred:', error);\n      } else {\n        // Other HTTP errors\n        console.error('HTTP error occurred:', error);\n      }\n\n      return throwError(() => error);\n    })\n  );\n};\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC7C,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,OAAO,MAAMC,gBAAgB,GAAsBA,CAACC,GAAG,EAAEC,IAAI,KAAI;EAC/D,MAAMC,MAAM,GAAGR,MAAM,CAACC,MAAM,CAAC;EAC7B,MAAMQ,WAAW,GAAGT,MAAM,CAACI,WAAW,CAAC;EAEvC,OAAOG,IAAI,CAACD,GAAG,CAAC,CAACI,IAAI,CACnBR,UAAU,CAAES,KAAwB,IAAI;IACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MACxB;MACAH,WAAW,CAACI,MAAM,EAAE;MACpBL,MAAM,CAACM,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KAC5B,MAAM,IAAIH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B;MACAJ,MAAM,CAACM,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;KACnC,MAAM,IAAIH,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7B;MACAG,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;KAChD,MAAM;MACL;MACAI,OAAO,CAACJ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;IAG9C,OAAOR,UAAU,CAAC,MAAMQ,KAAK,CAAC;EAChC,CAAC,CAAC,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}