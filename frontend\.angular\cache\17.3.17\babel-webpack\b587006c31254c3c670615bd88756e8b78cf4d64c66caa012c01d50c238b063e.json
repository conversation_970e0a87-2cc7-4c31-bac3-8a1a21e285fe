{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/client.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/table\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/chips\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nconst _c0 = a0 => [\"/clients\", a0];\nconst _c1 = a0 => [\"/clients\", a0, \"edit\"];\nfunction ClientListComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des clients...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientListComponent_div_37_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" Essayez de modifier vos crit\\u00E8res de recherche ou de filtrage. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_37_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" Commencez par ajouter votre premier client. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"business_center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Aucun client trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ClientListComponent_div_37_p_5_Template, 2, 0, \"p\", 20)(6, ClientListComponent_div_37_p_6_Template, 2, 0, \"p\", 20);\n    i0.ɵɵelementStart(7, \"button\", 13)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Ajouter un Client \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchQuery || ctx_r0.statusFilter !== \"ALL\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.searchQuery && ctx_r0.statusFilter === \"ALL\");\n  }\n}\nfunction ClientListComponent_div_38_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Nom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_4_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\")(1, \"a\", 34)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"link\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const client_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", client_r2.website, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ClientListComponent_div_38_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32)(1, \"div\", 33)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClientListComponent_div_38_td_4_small_4_Template, 4, 1, \"small\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(client_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", client_r2.website);\n  }\n}\nfunction ClientListComponent_div_38_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Secteur\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(client_r3.industry);\n  }\n}\nfunction ClientListComponent_div_38_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Statut\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32)(1, \"mat-chip\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r0.getStatusColor(client_r4.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getStatusLabel(client_r4.status), \" \");\n  }\n}\nfunction ClientListComponent_div_38_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 32)(1, \"div\", 36)(2, \"button\", 37)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 38)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ClientListComponent_div_38_td_13_Template_button_click_8_listener() {\n      const client_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.deleteClient(client_r6));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const client_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c0, client_r6.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, client_r6.id));\n  }\n}\nfunction ClientListComponent_div_38_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 40);\n  }\n}\nfunction ClientListComponent_div_38_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 41);\n  }\n}\nfunction ClientListComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"table\", 22);\n    i0.ɵɵelementContainerStart(2, 23);\n    i0.ɵɵtemplate(3, ClientListComponent_div_38_th_3_Template, 2, 0, \"th\", 24)(4, ClientListComponent_div_38_td_4_Template, 5, 2, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 26);\n    i0.ɵɵtemplate(6, ClientListComponent_div_38_th_6_Template, 2, 0, \"th\", 24)(7, ClientListComponent_div_38_td_7_Template, 2, 1, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 27);\n    i0.ɵɵtemplate(9, ClientListComponent_div_38_th_9_Template, 2, 0, \"th\", 24)(10, ClientListComponent_div_38_td_10_Template, 3, 2, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 28);\n    i0.ɵɵtemplate(12, ClientListComponent_div_38_th_12_Template, 2, 0, \"th\", 24)(13, ClientListComponent_div_38_td_13_Template, 11, 6, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(14, ClientListComponent_div_38_tr_14_Template, 1, 0, \"tr\", 29)(15, ClientListComponent_div_38_tr_15_Template, 1, 0, \"tr\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r0.filteredClients);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r0.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r0.displayedColumns);\n  }\n}\nexport let ClientListComponent = /*#__PURE__*/(() => {\n  class ClientListComponent {\n    constructor(clientService, dialog, snackBar) {\n      this.clientService = clientService;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.clients = [];\n      this.filteredClients = [];\n      this.displayedColumns = ['name', 'industry', 'status', 'actions'];\n      this.searchQuery = '';\n      this.statusFilter = 'ALL';\n      this.isLoading = false;\n      this.destroy$ = new Subject();\n      this.searchSubject = new Subject();\n      // Setup search debouncing\n      this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(query => {\n        this.performSearch(query);\n      });\n    }\n    ngOnInit() {\n      this.loadClients();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadClients() {\n      this.isLoading = true;\n      this.clientService.getClients().pipe(takeUntil(this.destroy$)).subscribe({\n        next: clients => {\n          this.clients = clients;\n          this.applyFilters();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading clients:', error);\n          this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    onSearchChange(query) {\n      this.searchQuery = query;\n      this.searchSubject.next(query);\n    }\n    onStatusFilterChange() {\n      this.applyFilters();\n    }\n    performSearch(query) {\n      if (query.trim()) {\n        this.clientService.searchClients(query).pipe(takeUntil(this.destroy$)).subscribe(clients => {\n          this.filteredClients = this.filterByStatus(clients);\n        });\n      } else {\n        this.applyFilters();\n      }\n    }\n    applyFilters() {\n      this.filteredClients = this.filterByStatus(this.clients);\n    }\n    filterByStatus(clients) {\n      if (this.statusFilter === 'ALL') {\n        return clients;\n      }\n      return clients.filter(client => client.status === this.statusFilter);\n    }\n    deleteClient(client) {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.name}\" ?`)) {\n        this.clientService.deleteClient(client.id).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.loadClients();\n          },\n          error: error => {\n            console.error('Error deleting client:', error);\n            this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n              duration: 3000\n            });\n          }\n        });\n      }\n    }\n    getStatusColor(status) {\n      switch (status) {\n        case 'ACTIVE':\n          return 'primary';\n        case 'INACTIVE':\n          return 'warn';\n        case 'PROSPECT':\n          return 'accent';\n        default:\n          return '';\n      }\n    }\n    getStatusLabel(status) {\n      switch (status) {\n        case 'ACTIVE':\n          return 'Actif';\n        case 'INACTIVE':\n          return 'Inactif';\n        case 'PROSPECT':\n          return 'Prospect';\n        default:\n          return status;\n      }\n    }\n    static {\n      this.ɵfac = function ClientListComponent_Factory(t) {\n        return new (t || ClientListComponent)(i0.ɵɵdirectiveInject(i1.ClientService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ClientListComponent,\n        selectors: [[\"app-client-list\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 39,\n        vars: 5,\n        consts: [[1, \"client-list-container\"], [1, \"header-card\"], [1, \"actions-row\"], [1, \"search-filters\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Nom, email, contact, secteur...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"status-filter\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [\"value\", \"ALL\"], [\"value\", \"ACTIVE\"], [\"value\", \"INACTIVE\"], [\"value\", \"PROSPECT\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/clients/create\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"no-data\"], [4, \"ngIf\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"clients-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"industry\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"client-name\"], [\"target\", \"_blank\", 1, \"website-link\", 3, \"href\"], [\"selected\", \"\", 3, \"color\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Voir les d\\u00E9tails\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Modifier\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Supprimer\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function ClientListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6, \" Gestion des Clients \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n            i0.ɵɵtext(8, \" G\\u00E9rez vos clients et prospects \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 2)(11, \"div\", 3)(12, \"mat-form-field\", 4)(13, \"mat-label\");\n            i0.ɵɵtext(14, \"Rechercher un client\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"input\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ClientListComponent_Template_input_ngModelChange_15_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"input\", function ClientListComponent_Template_input_input_15_listener($event) {\n              return ctx.onSearchChange($event.target.value || \"\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"mat-icon\", 6);\n            i0.ɵɵtext(17, \"search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"mat-form-field\", 7)(19, \"mat-label\");\n            i0.ɵɵtext(20, \"Statut\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-select\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ClientListComponent_Template_mat_select_ngModelChange_21_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.statusFilter, $event) || (ctx.statusFilter = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function ClientListComponent_Template_mat_select_selectionChange_21_listener() {\n              return ctx.onStatusFilterChange();\n            });\n            i0.ɵɵelementStart(22, \"mat-option\", 9);\n            i0.ɵɵtext(23, \"Tous\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-option\", 10);\n            i0.ɵɵtext(25, \"Actifs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"mat-option\", 11);\n            i0.ɵɵtext(27, \"Inactifs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"mat-option\", 12);\n            i0.ɵɵtext(29, \"Prospects\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(30, \"button\", 13)(31, \"mat-icon\");\n            i0.ɵɵtext(32, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(33, \" Nouveau Client \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(34, \"mat-card\", 14)(35, \"mat-card-content\");\n            i0.ɵɵtemplate(36, ClientListComponent_div_36_Template, 4, 0, \"div\", 15)(37, ClientListComponent_div_37_Template, 11, 2, \"div\", 16)(38, ClientListComponent_div_38_Template, 16, 3, \"div\", 17);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(15);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.statusFilter);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredClients.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredClients.length > 0);\n          }\n        },\n        dependencies: [CommonModule, i4.NgIf, RouterModule, i5.RouterLink, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatCardModule, i10.MatCard, i10.MatCardContent, i10.MatCardHeader, i10.MatCardSubtitle, i10.MatCardTitle, MatChipsModule, i11.MatChip, MatInputModule, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, MatFormFieldModule, MatSelectModule, i14.MatSelect, i15.MatOption, MatDialogModule, MatProgressSpinnerModule, i16.MatProgressSpinner],\n        styles: [\".client-list-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{margin-bottom:20px}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:1.5rem}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:1.5rem;height:1.5rem}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-end;gap:20px;margin-top:16px}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]{display:flex;gap:16px;flex:1}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{flex:1;max-width:400px}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .status-filter[_ngcontent-%COMP%]{min-width:150px}@media (max-width: 768px){.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]{flex-direction:column}.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%], .client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .status-filter[_ngcontent-%COMP%]{max-width:none}}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{text-align:center;padding:40px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;color:#0000004d;margin-bottom:16px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:#0009}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 24px;color:#00000080}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]{overflow-x:auto}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{width:100%}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-weight:500}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]{color:#00000080;text-decoration:none}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]:hover{color:var(--mdc-theme-primary, #1976d2)}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .email-link[_ngcontent-%COMP%], .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .phone-link[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2);text-decoration:none}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .email-link[_ngcontent-%COMP%]:hover, .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .phone-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;gap:4px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:40px;width:40px;height:40px}.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.2rem;width:1.2rem;height:1.2rem}@media (max-width: 768px){.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .mat-column-phone[_ngcontent-%COMP%], .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .mat-column-industry[_ngcontent-%COMP%]{display:none}}@media (max-width: 600px){.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .mat-column-contactPerson[_ngcontent-%COMP%]{display:none}}.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%]{font-weight:600;color:#000000de}.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.12)}.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%]:hover{background-color:#0000000a}.mat-mdc-chip[_ngcontent-%COMP%]{font-size:.75rem;min-height:24px}.mat-mdc-chip.mat-primary[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.mat-mdc-chip.mat-accent[_ngcontent-%COMP%]{background-color:#fff3e0;color:#f57c00}.mat-mdc-chip.mat-warn[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}\"]\n      });\n    }\n  }\n  return ClientListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}