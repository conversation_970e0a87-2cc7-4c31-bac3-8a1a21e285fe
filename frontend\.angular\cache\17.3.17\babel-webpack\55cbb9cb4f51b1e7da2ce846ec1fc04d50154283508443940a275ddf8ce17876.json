{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n  if (period < 0) {\n    period = 0;\n  }\n  return timer(period, period, scheduler);\n}", "map": {"version": 3, "names": ["asyncScheduler", "timer", "interval", "period", "scheduler"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/rxjs/dist/esm/internal/observable/interval.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,QAAQA,CAACC,MAAM,GAAG,CAAC,EAAEC,SAAS,GAAGJ,cAAc,EAAE;EAC7D,IAAIG,MAAM,GAAG,CAAC,EAAE;IACZA,MAAM,GAAG,CAAC;EACd;EACA,OAAOF,KAAK,CAACE,MAAM,EAAEA,MAAM,EAAEC,SAAS,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}