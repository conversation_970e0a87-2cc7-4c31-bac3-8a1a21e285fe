{"ast": null, "code": "export const clientRoutes = [{\n  path: '',\n  loadComponent: () => import('./client-list/client-list.component').then(m => m.ClientListComponent)\n}, {\n  path: 'create',\n  loadComponent: () => import('./client-form/client-form.component').then(m => m.ClientFormComponent)\n}, {\n  path: ':id',\n  loadComponent: () => import('./client-detail/client-detail.component').then(m => m.ClientDetailComponent)\n}, {\n  path: ':id/edit',\n  loadComponent: () => import('./client-form/client-form.component').then(m => m.ClientFormComponent)\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}