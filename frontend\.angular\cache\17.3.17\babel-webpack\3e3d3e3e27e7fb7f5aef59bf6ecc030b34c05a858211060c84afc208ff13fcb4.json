{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/client.service\";\nimport * as i2 from \"../../../services/contact.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/chips\";\nimport * as i11 from \"@angular/material/divider\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/table\";\nimport * as i14 from \"@angular/material/tabs\";\nimport * as i15 from \"@angular/material/menu\";\nfunction ClientDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails du client...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientDetailComponent_div_2_mat_divider_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ClientDetailComponent_div_2_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Adresse \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.address, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_mat_divider_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ClientDetailComponent_div_2_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Site web \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_47_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openWebsite());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"open_in_new\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"href\", ctx_r1.client.website, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.website, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_mat_divider_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ClientDetailComponent_div_2_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.notes, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 26)(5, \"div\", 27);\n    i0.ɵɵtext(6, \"Derni\\u00E8re modification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.client.updatedAt));\n  }\n}\nfunction ClientDetailComponent_div_2_mat_card_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Notes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 37);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.notes, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des contacts...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientDetailComponent_div_2_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Aucun contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Ce client n'a pas encore de contacts associ\\u00E9s.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_93_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddContact());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Ajouter le premier contact \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51);\n    i0.ɵɵtext(1, \"Nom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 52)(1, \"div\", 53)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r5 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", contact_r5.firstName, \" \", contact_r5.lastName, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 52)(1, \"a\", 54);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_7_Template_a_click_1_listener() {\n      const contact_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendContactEmail(contact_r7));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"mailto:\" + contact_r7.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", contact_r7.email, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51);\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 52)(1, \"a\", 54);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_10_Template_a_click_1_listener() {\n      const contact_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.callContactPhone(contact_r9));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"tel:\" + contact_r9.phone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", contact_r9.phone, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51);\n    i0.ɵɵtext(1, \"Poste\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(contact_r10.position);\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51);\n    i0.ɵɵtext(1, \"Statut\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 52)(1, \"mat-chip\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.getContactStatusColor(contact_r11.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getContactStatusLabel(contact_r11.status), \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_19_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_19_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const contact_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendContactEmail(contact_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Envoyer email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_19_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_19_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const contact_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.callContactPhone(contact_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Appeler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 52)(1, \"button\", 55)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 0)(6, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_19_Template_button_click_6_listener() {\n      const contact_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onViewContact(contact_r13));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Voir d\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_19_Template_button_click_10_listener() {\n      const contact_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onEditContact(contact_r13));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ClientDetailComponent_div_2_div_94_td_19_button_14_Template, 4, 0, \"button\", 57)(15, ClientDetailComponent_div_2_div_94_td_19_button_15_Template, 4, 0, \"button\", 57);\n    i0.ɵɵelement(16, \"mat-divider\");\n    i0.ɵɵelementStart(17, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_94_td_19_Template_button_click_17_listener() {\n      const contact_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDeleteContact(contact_r13));\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r13 = ctx.$implicit;\n    const contactMenu_r16 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", contactMenu_r16);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", contact_r13.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", contact_r13.phone);\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 59);\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 60);\n  }\n}\nfunction ClientDetailComponent_div_2_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40);\n    i0.ɵɵelementContainerStart(2, 41);\n    i0.ɵɵtemplate(3, ClientDetailComponent_div_2_div_94_th_3_Template, 2, 0, \"th\", 42)(4, ClientDetailComponent_div_2_div_94_td_4_Template, 5, 2, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 44);\n    i0.ɵɵtemplate(6, ClientDetailComponent_div_2_div_94_th_6_Template, 2, 0, \"th\", 42)(7, ClientDetailComponent_div_2_div_94_td_7_Template, 3, 2, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 45);\n    i0.ɵɵtemplate(9, ClientDetailComponent_div_2_div_94_th_9_Template, 2, 0, \"th\", 42)(10, ClientDetailComponent_div_2_div_94_td_10_Template, 3, 2, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 46);\n    i0.ɵɵtemplate(12, ClientDetailComponent_div_2_div_94_th_12_Template, 2, 0, \"th\", 42)(13, ClientDetailComponent_div_2_div_94_td_13_Template, 2, 1, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 47);\n    i0.ɵɵtemplate(15, ClientDetailComponent_div_2_div_94_th_15_Template, 2, 0, \"th\", 42)(16, ClientDetailComponent_div_2_div_94_td_16_Template, 3, 2, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 48);\n    i0.ɵɵtemplate(18, ClientDetailComponent_div_2_div_94_th_18_Template, 2, 0, \"th\", 42)(19, ClientDetailComponent_div_2_div_94_td_19_Template, 21, 3, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, ClientDetailComponent_div_2_div_94_tr_20_Template, 1, 0, \"tr\", 49)(21, ClientDetailComponent_div_2_div_94_tr_21_Template, 1, 0, \"tr\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.contacts);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.contactDisplayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.contactDisplayedColumns);\n  }\n}\nfunction ClientDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-card\", 6)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"div\", 7)(5, \"div\", 8)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip\", 9);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-subtitle\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-card-actions\")(14, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBack());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onEdit());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDelete());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"mat-tab-group\", 13);\n    i0.ɵɵtwoWayListener(\"selectedIndexChange\", function ClientDetailComponent_div_2_Template_mat_tab_group_selectedIndexChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedTabIndex, $event) || (ctx_r1.selectedTabIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(27, \"mat-tab\", 14)(28, \"div\", 15)(29, \"div\", 16)(30, \"mat-card\", 17)(31, \"mat-card-header\")(32, \"mat-card-title\")(33, \"mat-icon\");\n    i0.ɵɵtext(34, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(35, \" Informations entreprise \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-card-content\")(37, \"div\", 18)(38, \"div\", 19)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Secteur d'activit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 20);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, ClientDetailComponent_div_2_mat_divider_44_Template, 1, 0, \"mat-divider\", 21)(45, ClientDetailComponent_div_2_div_45_Template, 7, 1, \"div\", 22)(46, ClientDetailComponent_div_2_mat_divider_46_Template, 1, 0, \"mat-divider\", 21)(47, ClientDetailComponent_div_2_div_47_Template, 10, 2, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"mat-card\", 17)(49, \"mat-card-header\")(50, \"mat-card-title\")(51, \"mat-icon\");\n    i0.ɵɵtext(52, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(53, \" Statut et informations \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"mat-card-content\")(55, \"div\", 18)(56, \"div\", 19)(57, \"mat-icon\");\n    i0.ɵɵtext(58, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(59, \" Statut \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 20)(61, \"mat-chip\", 9);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(63, ClientDetailComponent_div_2_mat_divider_63_Template, 1, 0, \"mat-divider\", 21)(64, ClientDetailComponent_div_2_div_64_Template, 7, 1, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"mat-card\", 23)(66, \"mat-card-header\")(67, \"mat-card-title\")(68, \"mat-icon\");\n    i0.ɵɵtext(69, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(70, \" Historique \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"mat-card-content\")(72, \"div\", 24)(73, \"div\", 25)(74, \"mat-icon\");\n    i0.ɵɵtext(75, \"add_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(76, \"div\", 26)(77, \"div\", 27);\n    i0.ɵɵtext(78, \"Client cr\\u00E9\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"div\", 28);\n    i0.ɵɵtext(80);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(81, ClientDetailComponent_div_2_div_81_Template, 9, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(82, ClientDetailComponent_div_2_mat_card_82_Template, 9, 1, \"mat-card\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(83, \"mat-tab\", 31)(84, \"div\", 15)(85, \"div\", 32)(86, \"h3\");\n    i0.ɵɵtext(87, \"Contacts associ\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_88_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddContact());\n    });\n    i0.ɵɵelementStart(89, \"mat-icon\");\n    i0.ɵɵtext(90, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(91, \" Ajouter un contact \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(92, ClientDetailComponent_div_2_div_92_Template, 4, 0, \"div\", 2)(93, ClientDetailComponent_div_2_div_93_Template, 11, 0, \"div\", 33)(94, ClientDetailComponent_div_2_div_94_Template, 22, 3, \"div\", 34);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.getStatusColor(ctx_r1.client.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusLabel(ctx_r1.client.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.industry, \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"selectedIndex\", ctx_r1.selectedTabIndex);\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.industry, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.address);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.address);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.website);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.website);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"color\", ctx_r1.getStatusColor(ctx_r1.client.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusLabel(ctx_r1.client.status), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.notes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.notes);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.client.createdAt));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.updatedAt !== ctx_r1.client.createdAt);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.notes);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingContacts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingContacts && ctx_r1.contacts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingContacts && ctx_r1.contacts.length > 0);\n  }\n}\nexport let ClientDetailComponent = /*#__PURE__*/(() => {\n  class ClientDetailComponent {\n    constructor(clientService, contactService, router, route, snackBar, dialog) {\n      this.clientService = clientService;\n      this.contactService = contactService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.contacts = [];\n      this.isLoading = false;\n      this.isLoadingContacts = false;\n      this.selectedTabIndex = 0;\n      // Contact table columns\n      this.contactDisplayedColumns = ['name', 'email', 'phone', 'position', 'status', 'actions'];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        if (params['id']) {\n          this.clientId = +params['id'];\n          this.loadClientAndContacts();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadClientAndContacts() {\n      if (!this.clientId) return;\n      this.isLoading = true;\n      this.isLoadingContacts = true;\n      // Load both client and contacts in parallel\n      forkJoin({\n        client: this.clientService.getClient(this.clientId),\n        contacts: this.contactService.getContactsByClient(this.clientId)\n      }).pipe(takeUntil(this.destroy$)).subscribe({\n        next: ({\n          client,\n          contacts\n        }) => {\n          if (client) {\n            this.client = client;\n            this.contacts = contacts;\n          } else {\n            this.snackBar.open('Client non trouvé', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/clients']);\n          }\n          this.isLoading = false;\n          this.isLoadingContacts = false;\n        },\n        error: error => {\n          console.error('Error loading client and contacts:', error);\n          this.snackBar.open('Erreur lors du chargement des données', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n          this.isLoadingContacts = false;\n        }\n      });\n    }\n    loadContacts() {\n      if (!this.clientId) return;\n      this.isLoadingContacts = true;\n      this.contactService.getContactsByClient(this.clientId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: contacts => {\n          this.contacts = contacts;\n          this.isLoadingContacts = false;\n        },\n        error: error => {\n          console.error('Error loading contacts:', error);\n          this.snackBar.open('Erreur lors du chargement des contacts', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoadingContacts = false;\n        }\n      });\n    }\n    onEdit() {\n      if (this.client) {\n        this.router.navigate(['/clients', this.client.id, 'edit']);\n      }\n    }\n    onDelete() {\n      if (!this.client) return;\n      if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${this.client.name}\" ?`)) {\n        this.clientService.deleteClient(this.client.id).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/clients']);\n          },\n          error: error => {\n            console.error('Error deleting client:', error);\n            this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n              duration: 3000\n            });\n          }\n        });\n      }\n    }\n    onBack() {\n      this.router.navigate(['/clients']);\n    }\n    getStatusColor(status) {\n      switch (status) {\n        case 'ACTIVE':\n          return 'primary';\n        case 'INACTIVE':\n          return 'warn';\n        case 'PROSPECT':\n          return 'accent';\n        default:\n          return '';\n      }\n    }\n    getStatusLabel(status) {\n      switch (status) {\n        case 'ACTIVE':\n          return 'Actif';\n        case 'INACTIVE':\n          return 'Inactif';\n        case 'PROSPECT':\n          return 'Prospect';\n        default:\n          return status;\n      }\n    }\n    formatDate(date) {\n      return new Date(date).toLocaleDateString('fr-FR', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    }\n    openWebsite() {\n      if (this.client?.website) {\n        window.open(this.client.website, '_blank');\n      }\n    }\n    // Contact management methods\n    onAddContact() {\n      if (!this.clientId) return;\n      this.router.navigate(['/contacts/create'], {\n        queryParams: {\n          clientId: this.clientId\n        }\n      });\n    }\n    onEditContact(contact) {\n      this.router.navigate(['/contacts', contact.id, 'edit']);\n    }\n    onViewContact(contact) {\n      this.router.navigate(['/contacts', contact.id]);\n    }\n    onDeleteContact(contact) {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer le contact \"${contact.firstName} ${contact.lastName}\" ?`)) {\n        this.contactService.deleteContact(contact.id).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.snackBar.open('Contact supprimé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.loadContacts(); // Reload contacts\n          },\n          error: error => {\n            console.error('Error deleting contact:', error);\n            this.snackBar.open('Erreur lors de la suppression du contact', 'Fermer', {\n              duration: 3000\n            });\n          }\n        });\n      }\n    }\n    getContactStatusColor(status) {\n      switch (status) {\n        case 'ACTIVE':\n          return 'primary';\n        case 'INACTIVE':\n          return 'warn';\n        default:\n          return '';\n      }\n    }\n    getContactStatusLabel(status) {\n      switch (status) {\n        case 'ACTIVE':\n          return 'Actif';\n        case 'INACTIVE':\n          return 'Inactif';\n        default:\n          return status;\n      }\n    }\n    sendContactEmail(contact) {\n      if (contact.email) {\n        window.location.href = `mailto:${contact.email}`;\n      }\n    }\n    callContactPhone(contact) {\n      if (contact.phone) {\n        window.location.href = `tel:${contact.phone}`;\n      }\n    }\n    static {\n      this.ɵfac = function ClientDetailComponent_Factory(t) {\n        return new (t || ClientDetailComponent)(i0.ɵɵdirectiveInject(i1.ClientService), i0.ɵɵdirectiveInject(i2.ContactService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ClientDetailComponent,\n        selectors: [[\"app-client-detail\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 2,\n        consts: [[\"contactMenu\", \"matMenu\"], [1, \"client-detail-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"client-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"client-content\"], [1, \"header-card\"], [1, \"title-row\"], [1, \"client-name\"], [\"selected\", \"\", 3, \"color\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"client-tabs\", 3, \"selectedIndexChange\", \"selectedIndex\"], [\"label\", \"D\\u00E9tails du client\"], [1, \"tab-content\"], [1, \"details-grid\"], [1, \"info-card\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [4, \"ngIf\"], [\"class\", \"info-item\", 4, \"ngIf\"], [1, \"info-card\", \"timeline-card\"], [1, \"timeline-item\"], [1, \"timeline-icon\"], [1, \"timeline-content\"], [1, \"timeline-title\"], [1, \"timeline-date\"], [\"class\", \"timeline-item\", 4, \"ngIf\"], [\"class\", \"info-card notes-card\", 4, \"ngIf\"], [\"label\", \"Contacts\"], [1, \"contacts-header\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"contacts-table-container\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"contact-link\", 3, \"click\", \"href\"], [1, \"info-card\", \"notes-card\"], [1, \"notes-content\"], [1, \"empty-state\"], [1, \"contacts-table-container\"], [\"mat-table\", \"\", 1, \"contacts-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"email\"], [\"matColumnDef\", \"phone\"], [\"matColumnDef\", \"position\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"contact-name\"], [1, \"contact-link\", 3, \"click\", \"href\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 1, \"delete-action\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function ClientDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1);\n            i0.ɵɵtemplate(1, ClientDetailComponent_div_1_Template, 4, 0, \"div\", 2)(2, ClientDetailComponent_div_2_Template, 95, 20, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.client);\n          }\n        },\n        dependencies: [CommonModule, i6.NgIf, RouterModule, MatCardModule, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatChipsModule, i10.MatChip, MatDividerModule, i11.MatDivider, MatProgressSpinnerModule, i12.MatProgressSpinner, MatTableModule, i13.MatTable, i13.MatHeaderCellDef, i13.MatHeaderRowDef, i13.MatColumnDef, i13.MatCellDef, i13.MatRowDef, i13.MatHeaderCell, i13.MatCell, i13.MatHeaderRow, i13.MatRow, MatTabsModule, i14.MatTab, i14.MatTabGroup, MatMenuModule, i15.MatMenu, i15.MatMenuItem, i15.MatMenuTrigger, MatDialogModule],\n        styles: [\".client-detail-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.client-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.client-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{margin-bottom:24px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:1.5rem;font-weight:500}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:1.5rem;height:1.5rem;color:var(--mdc-theme-primary, #1976d2)}@media (max-width: 600px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:12px}}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{margin-top:8px;color:#0009;font-size:.9rem}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:16px 24px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.1rem;width:1.1rem;height:1.1rem}@media (max-width: 600px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]{flex-wrap:wrap}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{flex:1;min-width:120px}}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .client-tabs[_ngcontent-%COMP%]{margin-top:24px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .client-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-group[_ngcontent-%COMP%]   .mat-mdc-tab-header[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.12)}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .client-tabs[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]{padding:24px 0}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:24px}@media (max-width: 768px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:1.1rem}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.2rem;width:1.2rem;height:1.2rem;color:var(--mdc-theme-primary, #1976d2)}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding-top:16px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:12px 0}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:500;color:#000000b3;min-width:120px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem;color:#00000080}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{flex:1;text-align:right}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2);text-decoration:none;display:flex;align-items:center;justify-content:flex-end;gap:4px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.9rem;width:.9rem;height:.9rem}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 600px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:4px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{text-align:left}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]{justify-content:flex-start}}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%]{margin:0}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:12px 0}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:not(:last-child){border-bottom:1px solid rgba(0,0,0,.12)}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2);font-size:1.2rem;width:1.2rem;height:1.2rem}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]{flex:1}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]{font-weight:500;color:#000000de;margin-bottom:4px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-date[_ngcontent-%COMP%]{font-size:.85rem;color:#0009}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%]{grid-column:1/-1}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%]   .notes-content[_ngcontent-%COMP%]{background-color:#00000005;border-left:4px solid var(--mdc-theme-primary, #1976d2);padding:16px;border-radius:4px;line-height:1.6;white-space:pre-wrap}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.2rem;font-weight:500;color:#000000de}@media (max-width: 600px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#0009}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:16px;color:#0000004d}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;font-size:1.2rem;font-weight:500;color:#000000b3}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 24px;font-size:.9rem}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]{width:100%}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.1rem;width:1.1rem;height:1.1rem;color:#00000080}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2);text-decoration:none}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-menu-item.delete-action[_ngcontent-%COMP%]{color:#d32f2f}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-menu-item.delete-action[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#d32f2f}@media (max-width: 768px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%]:nth-child(3), .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%]:nth-child(4), .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]:nth-child(3), .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]:nth-child(4){display:none}}@media (max-width: 600px){.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%]:nth-child(2), .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .contacts-table-container[_ngcontent-%COMP%]   .contacts-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]:nth-child(2){display:none}}.mat-mdc-chip[_ngcontent-%COMP%]{font-size:.75rem;min-height:28px}.mat-mdc-chip.mat-primary[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.mat-mdc-chip.mat-accent[_ngcontent-%COMP%]{background-color:#fff3e0;color:#f57c00}.mat-mdc-chip.mat-warn[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]{background-color:var(--mdc-theme-primary, #1976d2);color:#fff}.mat-mdc-button.mat-warn[_ngcontent-%COMP%]{color:#d32f2f}.mat-mdc-button.mat-warn[_ngcontent-%COMP%]:hover{background-color:#d32f2f0a}@media (max-width: 768px){.client-detail-container[_ngcontent-%COMP%]{padding:16px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{margin-bottom:16px}.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]{gap:16px}}\"]\n      });\n    }\n  }\n  return ClientDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}