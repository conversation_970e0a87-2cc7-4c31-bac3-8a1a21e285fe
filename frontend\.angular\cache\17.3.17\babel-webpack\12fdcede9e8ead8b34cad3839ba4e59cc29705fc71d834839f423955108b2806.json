{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/freelance.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nconst _c0 = a0 => [\"/projects\", a0];\nfunction DashboardComponent_div_59_mat_card_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, project_r1.workMode));\n  }\n}\nfunction DashboardComponent_div_59_mat_card_1_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r1.techStack, \" \");\n  }\n}\nfunction DashboardComponent_div_59_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 21)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 22)(8, \"div\", 23)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_59_mat_card_1_div_13_Template, 6, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, DashboardComponent_div_59_mat_card_1_p_14_Template, 2, 1, \"p\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-card-actions\")(16, \"button\", 26);\n    i0.ɵɵtext(17, \" Voir d\\u00E9tails \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r1.clientName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", project_r1.dailyRate, \"\\u20AC/jour\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r1.workMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r1.techStack);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(6, _c0, project_r1.id));\n  }\n}\nfunction DashboardComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, DashboardComponent_div_59_mat_card_1_Template, 18, 8, \"mat-card\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentProjects);\n  }\n}\nfunction DashboardComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 29)(1, \"mat-card-content\")(2, \"div\", 30)(3, \"mat-icon\", 31);\n    i0.ɵɵtext(4, \"work_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Aucun projet pour le moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Commencez par ajouter votre premier projet !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵtext(10, \" Ajouter un projet \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(authService, projectService, freelanceService) {\n      this.authService = authService;\n      this.projectService = projectService;\n      this.freelanceService = freelanceService;\n      this.freelanceProfile = null;\n      this.recentProjects = [];\n      this.stats = {\n        totalProjects: 0,\n        averageDailyRate: 0,\n        totalRevenue: 0,\n        activeProjects: 0\n      };\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getUser();\n      this.loadDashboardData();\n    }\n    loadDashboardData() {\n      if (this.currentUser?.id) {\n        // Load freelance profile\n        this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n          next: profile => {\n            this.freelanceProfile = profile;\n            this.updateStats(profile);\n          },\n          error: error => console.error('Error loading profile:', error)\n        });\n        // Load recent projects\n        this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n          next: projects => {\n            this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n          },\n          error: error => console.error('Error loading projects:', error)\n        });\n      }\n    }\n    updateStats(profile) {\n      this.stats = {\n        totalProjects: profile.totalProjects || 0,\n        averageDailyRate: profile.averageDailyRate || 0,\n        totalRevenue: this.calculateTotalRevenue(),\n        activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n      };\n    }\n    calculateTotalRevenue() {\n      return this.recentProjects.reduce((total, project) => {\n        if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n          const monthlyDays = project.daysPerYear / 12;\n          return total + project.dailyRate * monthlyDays * project.durationInMonths;\n        }\n        return total;\n      }, 0);\n    }\n    logout() {\n      this.authService.logout();\n    }\n    getGreeting() {\n      const hour = new Date().getHours();\n      if (hour < 12) return 'Bonjour';\n      if (hour < 18) return 'Bon après-midi';\n      return 'Bonsoir';\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.FreelanceService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 62,\n        vars: 14,\n        consts: [[\"noProjects\", \"\"], [1, \"dashboard-container\"], [1, \"welcome-section\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-icon\", \"projects\"], [1, \"stat-info\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-icon\", \"rate\"], [1, \"stat-icon\", \"revenue\"], [1, \"stat-icon\", \"active\"], [1, \"recent-projects-section\"], [1, \"section-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects\"], [\"class\", \"projects-grid\", 4, \"ngIf\", \"ngIfElse\"], [1, \"projects-grid\"], [\"class\", \"project-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"project-card\"], [1, \"project-info\"], [1, \"project-rate\"], [\"class\", \"project-mode\", 4, \"ngIf\"], [\"class\", \"project-tech\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"routerLink\"], [1, \"project-mode\"], [1, \"project-tech\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\", 3);\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\", 4);\n            i0.ɵɵtext(5, \" Voici un aper\\u00E7u de vos missions freelance \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"mat-card\", 6)(8, \"mat-card-content\")(9, \"div\", 7)(10, \"div\", 8)(11, \"mat-icon\");\n            i0.ɵɵtext(12, \"work\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10);\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 11);\n            i0.ɵɵtext(17, \"Projets totaux\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(18, \"mat-card\", 6)(19, \"mat-card-content\")(20, \"div\", 7)(21, \"div\", 12)(22, \"mat-icon\");\n            i0.ɵɵtext(23, \"euro\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 9)(25, \"div\", 10);\n            i0.ɵɵtext(26);\n            i0.ɵɵpipe(27, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 11);\n            i0.ɵɵtext(29, \"TJM moyen\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(30, \"mat-card\", 6)(31, \"mat-card-content\")(32, \"div\", 7)(33, \"div\", 13)(34, \"mat-icon\");\n            i0.ɵɵtext(35, \"trending_up\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10);\n            i0.ɵɵtext(38);\n            i0.ɵɵpipe(39, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 11);\n            i0.ɵɵtext(41, \"Revenus estim\\u00E9s\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(42, \"mat-card\", 6)(43, \"mat-card-content\")(44, \"div\", 7)(45, \"div\", 14)(46, \"mat-icon\");\n            i0.ɵɵtext(47, \"play_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10);\n            i0.ɵɵtext(50);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 11);\n            i0.ɵɵtext(52, \"Projets actifs\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(53, \"div\", 15)(54, \"div\", 16)(55, \"h2\");\n            i0.ɵɵtext(56, \"Projets r\\u00E9cents\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"button\", 17);\n            i0.ɵɵtext(58, \" Voir tous les projets \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(59, DashboardComponent_div_59_Template, 2, 1, \"div\", 18)(60, DashboardComponent_ng_template_60_Template, 11, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const noProjects_r3 = i0.ɵɵreference(61);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate2(\" \", ctx.getGreeting(), \", \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" ! \\uD83D\\uDC4B \");\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate(ctx.stats.totalProjects);\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(27, 8, ctx.stats.averageDailyRate, \"1.0-0\"), \"\\u20AC\");\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(39, 11, ctx.stats.totalRevenue, \"1.0-0\"), \"\\u20AC\");\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate(ctx.stats.activeProjects);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.recentProjects.length > 0)(\"ngIfElse\", noProjects_r3);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, i4.TitleCasePipe, RouterModule, i5.RouterLink, MatIconModule, i6.MatIcon, MatButtonModule, i7.MatButton, MatCardModule, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.welcome-section[_ngcontent-%COMP%]{margin-bottom:32px}.welcome-title[_ngcontent-%COMP%]{font-size:32px;font-weight:600;margin:0 0 8px;color:#333}.welcome-subtitle[_ngcontent-%COMP%]{font-size:16px;color:#666;margin:0}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:32px}.stat-card[_ngcontent-%COMP%]{border-radius:12px;box-shadow:0 2px 8px #0000001a}.stat-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center}.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.stat-icon.projects[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.stat-icon.rate[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.stat-icon.revenue[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe)}.stat-icon.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.stat-info[_ngcontent-%COMP%]{flex:1}.stat-number[_ngcontent-%COMP%]{font-size:24px;font-weight:600;color:#333;line-height:1}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#666;margin-top:4px}.recent-projects-section[_ngcontent-%COMP%]{margin-bottom:32px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px;font-weight:600;margin:0;color:#333}.projects-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px}.project-card[_ngcontent-%COMP%]{border-radius:12px;box-shadow:0 2px 8px #0000001a;transition:transform .2s ease,box-shadow .2s ease}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #00000026}.project-info[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:12px}.project-info[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-size:14px;color:#666}.project-info[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.project-tech[_ngcontent-%COMP%]{font-size:14px;color:#888;margin:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.empty-state[_ngcontent-%COMP%]{border-radius:12px;box-shadow:0 2px 8px #0000001a}.empty-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:#ccc;margin-bottom:16px}.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:500;margin:0 0 8px;color:#333}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin:0 0 24px}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{padding:16px}.welcome-title[_ngcontent-%COMP%]{font-size:24px}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px}.projects-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.section-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:12px}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}