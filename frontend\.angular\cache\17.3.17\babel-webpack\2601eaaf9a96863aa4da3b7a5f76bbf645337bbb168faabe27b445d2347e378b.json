{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/client.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction ClientFormComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientFormComponent_form_11_mat_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const industry_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", industry_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", industry_r3, \" \");\n  }\n}\nfunction ClientFormComponent_form_11_mat_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r4.label, \" \");\n  }\n}\nfunction ClientFormComponent_form_11_mat_spinner_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction ClientFormComponent_form_11_mat_icon_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"save\" : \"add\");\n  }\n}\nfunction ClientFormComponent_form_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function ClientFormComponent_form_11_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7)(3, \"h3\");\n    i0.ɵɵtext(4, \"Informations g\\u00E9n\\u00E9rales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 8)(6, \"mat-form-field\", 9)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Nom de l'entreprise *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 10);\n    i0.ɵɵelementStart(10, \"mat-error\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 8)(13, \"mat-form-field\", 9)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Secteur d'activit\\u00E9 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-select\", 11);\n    i0.ɵɵtemplate(17, ClientFormComponent_form_11_mat_option_17_Template, 2, 2, \"mat-option\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"mat-error\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 8)(21, \"mat-form-field\", 9)(22, \"mat-label\");\n    i0.ɵɵtext(23, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"textarea\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 8)(26, \"mat-form-field\", 14)(27, \"mat-label\");\n    i0.ɵɵtext(28, \"Site web\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 15);\n    i0.ɵɵelementStart(30, \"mat-icon\", 16);\n    i0.ɵɵtext(31, \"link\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-form-field\", 14)(33, \"mat-label\");\n    i0.ɵɵtext(34, \"Statut *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"mat-select\", 17);\n    i0.ɵɵtemplate(36, ClientFormComponent_form_11_mat_option_36_Template, 2, 2, \"mat-option\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-error\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(39, \"div\", 7)(40, \"h3\");\n    i0.ɵɵtext(41, \"Informations compl\\u00E9mentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 8)(43, \"mat-form-field\", 9)(44, \"mat-label\");\n    i0.ɵɵtext(45, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"textarea\", 18);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 19)(48, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ClientFormComponent_form_11_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(49, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"button\", 21);\n    i0.ɵɵtemplate(51, ClientFormComponent_form_11_mat_spinner_51_Template, 1, 0, \"mat-spinner\", 22)(52, ClientFormComponent_form_11_mat_icon_52_Template, 2, 1, \"mat-icon\", 23);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.clientForm);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"name\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.industries);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"industry\"));\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.statuses);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"status\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSaving);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSaving);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSaving);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSaving);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isSaving ? \"Enregistrement...\" : ctx_r1.isEditMode ? \"Modifier\" : \"Cr\\u00E9er\", \" \");\n  }\n}\nexport class ClientFormComponent {\n  constructor(fb, clientService, router, route, snackBar) {\n    this.fb = fb;\n    this.clientService = clientService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isEditMode = false;\n    this.isLoading = false;\n    this.isSaving = false;\n    this.industries = ['Technology', 'Finance', 'Healthcare', 'Education', 'E-commerce', 'Manufacturing', 'Consulting', 'Marketing', 'Real Estate', 'Retail', 'Transportation', 'Energy', 'Media', 'Government', 'Non-profit', 'Other'];\n    this.statuses = [{\n      value: 'ACTIVE',\n      label: 'Actif'\n    }, {\n      value: 'INACTIVE',\n      label: 'Inactif'\n    }, {\n      value: 'PROSPECT',\n      label: 'Prospect'\n    }];\n    this.destroy$ = new Subject();\n    this.clientForm = this.createForm();\n  }\n  ngOnInit() {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.clientId = +params['id'];\n        this.isEditMode = true;\n        this.loadClient();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createForm() {\n    return this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      industry: ['', [Validators.required]],\n      address: [''],\n      website: [''],\n      notes: [''],\n      status: ['ACTIVE', [Validators.required]]\n    });\n  }\n  loadClient() {\n    if (!this.clientId) return;\n    this.isLoading = true;\n    this.clientService.getClient(this.clientId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: client => {\n        if (client) {\n          this.clientForm.patchValue(client);\n        } else {\n          this.snackBar.open('Client non trouvé', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/clients']);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading client:', error);\n        this.snackBar.open('Erreur lors du chargement du client', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onSubmit() {\n    if (this.clientForm.valid) {\n      this.isSaving = true;\n      const formValue = this.clientForm.value;\n      if (this.isEditMode && this.clientId) {\n        const updateData = {\n          id: this.clientId,\n          ...formValue\n        };\n        this.clientService.updateClient(updateData).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.snackBar.open('Client modifié avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/clients']);\n          },\n          error: error => {\n            console.error('Error updating client:', error);\n            this.snackBar.open('Erreur lors de la modification du client', 'Fermer', {\n              duration: 3000\n            });\n            this.isSaving = false;\n          }\n        });\n      } else {\n        const createData = formValue;\n        this.clientService.createClient(createData).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.snackBar.open('Client créé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/clients']);\n          },\n          error: error => {\n            console.error('Error creating client:', error);\n            this.snackBar.open('Erreur lors de la création du client', 'Fermer', {\n              duration: 3000\n            });\n            this.isSaving = false;\n          }\n        });\n      }\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  onCancel() {\n    this.router.navigate(['/clients']);\n  }\n  markFormGroupTouched() {\n    Object.keys(this.clientForm.controls).forEach(key => {\n      const control = this.clientForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const control = this.clientForm.get(fieldName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return 'Ce champ est requis';\n      }\n      if (control.errors['email']) {\n        return 'Veuillez saisir une adresse email valide';\n      }\n      if (control.errors['minlength']) {\n        return `Minimum ${control.errors['minlength'].requiredLength} caractères`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function ClientFormComponent_Factory(t) {\n      return new (t || ClientFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ClientService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClientFormComponent,\n      selectors: [[\"app-client-form\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 5,\n      consts: [[1, \"client-form-container\"], [1, \"form-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"loading-container\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-grid\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Ex: TechCorp Solutions\"], [\"formControlName\", \"industry\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"rows\", \"2\", \"placeholder\", \"123 Avenue des Champs-\\u00C9lys\\u00E9es, 75008 Paris\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"https://www.entreprise.com\"], [\"matSuffix\", \"\"], [\"formControlName\", \"status\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"4\", \"placeholder\", \"Notes sur le client, historique, pr\\u00E9f\\u00E9rences...\"], [1, \"form-actions\"], [\"type\", \"button\", \"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", \"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"value\"], [\"diameter\", \"20\"]],\n      template: function ClientFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\");\n          i0.ɵɵtemplate(10, ClientFormComponent_div_10_Template, 4, 0, \"div\", 2)(11, ClientFormComponent_form_11_Template, 54, 11, \"form\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier le Client\" : \"Nouveau Client\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations du client\" : \"Ajoutez un nouveau client \\u00E0 votre portefeuille\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, RouterModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatSelectModule, i9.MatSelect, i10.MatOption, MatButtonModule, i11.MatButton, MatIconModule, i12.MatIcon, MatProgressSpinnerModule, i13.MatProgressSpinner],\n      styles: [\".client-form-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 1.5rem;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding-top: 24px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 32px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.87);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n  padding-bottom: 8px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n@media (max-width: 600px) {\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 12px;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n@media (max-width: 600px) {\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n  }\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n.mat-mdc-form-field.full-width[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%], .mat-mdc-form-field.half-width[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%]   .mat-mdc-form-field-error-wrapper[_ngcontent-%COMP%]   .mat-mdc-form-field-error[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #f44336;\\n}\\n\\ntextarea.mat-mdc-input-element[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 60px;\\n}\\n\\n.mat-mdc-select[_ngcontent-%COMP%]   .mat-mdc-select-trigger[_ngcontent-%COMP%]   .mat-mdc-select-value[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.87);\\n}\\n\\n.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%] {\\n  background-color: var(--mdc-theme-primary, #1976d2);\\n  color: white;\\n}\\n.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]:disabled {\\n  background-color: rgba(0, 0, 0, 0.12);\\n  color: rgba(0, 0, 0, 0.26);\\n}\\n\\n.mat-mdc-button[_ngcontent-%COMP%]:not(.mat-mdc-raised-button) {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.mat-mdc-button[_ngcontent-%COMP%]:not(.mat-mdc-raised-button):hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n\\n@media (max-width: 768px) {\\n  .client-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n    gap: 24px;\\n  }\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n    margin-top: 24px;\\n    padding-top: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "industry_r3", "ɵɵadvance", "ɵɵtextInterpolate1", "status_r4", "value", "label", "ɵɵtextInterpolate", "ctx_r1", "isEditMode", "ɵɵlistener", "ClientFormComponent_form_11_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵtemplate", "ClientFormComponent_form_11_mat_option_17_Template", "ClientFormComponent_form_11_mat_option_36_Template", "ClientFormComponent_form_11_Template_button_click_48_listener", "onCancel", "ClientFormComponent_form_11_mat_spinner_51_Template", "ClientFormComponent_form_11_mat_icon_52_Template", "clientForm", "getFieldError", "industries", "statuses", "isSaving", "ClientFormComponent", "constructor", "fb", "clientService", "router", "route", "snackBar", "isLoading", "destroy$", "createForm", "ngOnInit", "params", "pipe", "subscribe", "clientId", "loadClient", "ngOnDestroy", "next", "complete", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "industry", "address", "website", "notes", "status", "getClient", "client", "patchValue", "open", "duration", "navigate", "error", "console", "valid", "formValue", "updateData", "id", "updateClient", "createData", "createClient", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ClientService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ClientFormComponent_Template", "rf", "ctx", "ClientFormComponent_div_10_Template", "ClientFormComponent_form_11_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatSelect", "i10", "MatOption", "i11", "MatButton", "i12", "MatIcon", "i13", "MatProgressSpinner", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-form\\client-form.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-form\\client-form.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { ClientService, ClientDto, CreateClientDto, UpdateClientDto } from '../../../services/client.service';\n\n@Component({\n  selector: 'app-client-form',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatButtonModule,\n    MatIconModule,\n\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './client-form.component.html',\n  styleUrls: ['./client-form.component.scss']\n})\nexport class ClientFormComponent implements OnInit, OnDestroy {\n  clientForm: FormGroup;\n  isEditMode = false;\n  isLoading = false;\n  isSaving = false;\n  clientId?: number;\n  \n  industries = [\n    'Technology',\n    'Finance',\n    'Healthcare',\n    'Education',\n    'E-commerce',\n    'Manufacturing',\n    'Consulting',\n    'Marketing',\n    'Real Estate',\n    'Retail',\n    'Transportation',\n    'Energy',\n    'Media',\n    'Government',\n    'Non-profit',\n    'Other'\n  ];\n  \n  statuses = [\n    { value: 'ACTIVE', label: 'Actif' },\n    { value: 'INACTIVE', label: 'Inactif' },\n    { value: 'PROSPECT', label: 'Prospect' }\n  ];\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private fb: FormBuilder,\n    private clientService: ClientService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.clientForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.clientId = +params['id'];\n        this.isEditMode = true;\n        this.loadClient();\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private createForm(): FormGroup {\n    return this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      industry: ['', [Validators.required]],\n      address: [''],\n      website: [''],\n      notes: [''],\n      status: ['ACTIVE', [Validators.required]]\n    });\n  }\n\n  private loadClient(): void {\n    if (!this.clientId) return;\n    \n    this.isLoading = true;\n    this.clientService.getClient(this.clientId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (client) => {\n          if (client) {\n            this.clientForm.patchValue(client);\n          } else {\n            this.snackBar.open('Client non trouvé', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/clients']);\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading client:', error);\n          this.snackBar.open('Erreur lors du chargement du client', 'Fermer', { duration: 3000 });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onSubmit(): void {\n    if (this.clientForm.valid) {\n      this.isSaving = true;\n      const formValue = this.clientForm.value;\n      \n      if (this.isEditMode && this.clientId) {\n        const updateData: UpdateClientDto = {\n          id: this.clientId,\n          ...formValue\n        };\n        \n        this.clientService.updateClient(updateData)\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: () => {\n              this.snackBar.open('Client modifié avec succès', 'Fermer', { duration: 3000 });\n              this.router.navigate(['/clients']);\n            },\n            error: (error) => {\n              console.error('Error updating client:', error);\n              this.snackBar.open('Erreur lors de la modification du client', 'Fermer', { duration: 3000 });\n              this.isSaving = false;\n            }\n          });\n      } else {\n        const createData: CreateClientDto = formValue;\n        \n        this.clientService.createClient(createData)\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: () => {\n              this.snackBar.open('Client créé avec succès', 'Fermer', { duration: 3000 });\n              this.router.navigate(['/clients']);\n            },\n            error: (error) => {\n              console.error('Error creating client:', error);\n              this.snackBar.open('Erreur lors de la création du client', 'Fermer', { duration: 3000 });\n              this.isSaving = false;\n            }\n          });\n      }\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  onCancel(): void {\n    this.router.navigate(['/clients']);\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.clientForm.controls).forEach(key => {\n      const control = this.clientForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const control = this.clientForm.get(fieldName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return 'Ce champ est requis';\n      }\n      if (control.errors['email']) {\n        return 'Veuillez saisir une adresse email valide';\n      }\n      if (control.errors['minlength']) {\n        return `Minimum ${control.errors['minlength'].requiredLength} caractères`;\n      }\n    }\n    return '';\n  }\n}\n", "<div class=\"client-form-container\">\n  <mat-card class=\"form-card\">\n    <mat-card-header>\n      <mat-card-title>\n        <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>\n        {{ isEditMode ? 'Modifier le Client' : 'Nouveau Client' }}\n      </mat-card-title>\n      <mat-card-subtitle>\n        {{ isEditMode ? 'Modifiez les informations du client' : 'Ajoutez un nouveau client à votre portefeuille' }}\n      </mat-card-subtitle>\n    </mat-card-header>\n\n    <mat-card-content>\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner></mat-spinner>\n        <p>Chargement des données...</p>\n      </div>\n\n      <form [formGroup]=\"clientForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"!isLoading\">\n        <div class=\"form-grid\">\n          <!-- Basic Information Section -->\n          <div class=\"form-section\">\n            <h3>Informations générales</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Nom de l'entreprise *</mat-label>\n                <input matInput formControlName=\"name\" placeholder=\"Ex: TechCorp Solutions\">\n                <mat-error>{{ getFieldError('name') }}</mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Secteur d'activité *</mat-label>\n                <mat-select formControlName=\"industry\">\n                  <mat-option *ngFor=\"let industry of industries\" [value]=\"industry\">\n                    {{ industry }}\n                  </mat-option>\n                </mat-select>\n                <mat-error>{{ getFieldError('industry') }}</mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Adresse</mat-label>\n                <textarea matInput formControlName=\"address\" rows=\"2\"\n                         placeholder=\"123 Avenue des Champs-Élysées, 75008 Paris\"></textarea>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Site web</mat-label>\n                <input matInput formControlName=\"website\" placeholder=\"https://www.entreprise.com\">\n                <mat-icon matSuffix>link</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Statut *</mat-label>\n                <mat-select formControlName=\"status\">\n                  <mat-option *ngFor=\"let status of statuses\" [value]=\"status.value\">\n                    {{ status.label }}\n                  </mat-option>\n                </mat-select>\n                <mat-error>{{ getFieldError('status') }}</mat-error>\n              </mat-form-field>\n            </div>\n          </div>\n\n          <!-- Additional Information Section -->\n          <div class=\"form-section\">\n            <h3>Informations complémentaires</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Notes</mat-label>\n                <textarea matInput formControlName=\"notes\" rows=\"4\" \n                         placeholder=\"Notes sur le client, historique, préférences...\"></textarea>\n              </mat-form-field>\n            </div>\n          </div>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"form-actions\">\n          <button type=\"button\" mat-button (click)=\"onCancel()\" [disabled]=\"isSaving\">\n            Annuler\n          </button>\n          <button type=\"submit\" mat-raised-button color=\"primary\" [disabled]=\"isSaving\">\n            <mat-spinner *ngIf=\"isSaving\" diameter=\"20\"></mat-spinner>\n            <mat-icon *ngIf=\"!isSaving\">{{ isEditMode ? 'save' : 'add' }}</mat-icon>\n            {{ isSaving ? 'Enregistrement...' : (isEditMode ? 'Modifier' : 'Créer') }}\n          </button>\n        </div>\n      </form>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgC,iBAAiB;AACtE,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;ICCnCC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;IAoBMJ,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAa;;;;IAFmCJ,EAAA,CAAAK,UAAA,UAAAC,WAAA,CAAkB;IAChEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAF,WAAA,MACF;;;;;IAwBAN,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAa;;;;IAF+BJ,EAAA,CAAAK,UAAA,UAAAI,SAAA,CAAAC,KAAA,CAAsB;IAChEV,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,SAAA,CAAAE,KAAA,MACF;;;;;IA2BNX,EAAA,CAAAE,SAAA,sBAA0D;;;;;IAC1DF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IAA5CJ,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAC,UAAA,kBAAiC;;;;;;IA1EnEd,EAAA,CAAAC,cAAA,cAA0E;IAA3CD,EAAA,CAAAe,UAAA,sBAAAC,8DAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAb,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYP,MAAA,CAAAQ,QAAA,EAAU;IAAA,EAAC;IAIhDrB,EAHJ,CAAAC,cAAA,aAAuB,aAEK,SACpB;IAAAD,EAAA,CAAAG,MAAA,uCAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI3BJ,EAFJ,CAAAC,cAAA,aAAsB,wBACoC,gBAC3C;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5CJ,EAAA,CAAAE,SAAA,gBAA4E;IAC5EF,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAE1CH,EAF0C,CAAAI,YAAA,EAAY,EACnC,EACb;IAIFJ,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,iCAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC3CJ,EAAA,CAAAC,cAAA,sBAAuC;IACrCD,EAAA,CAAAsB,UAAA,KAAAC,kDAAA,yBAAmE;IAGrEvB,EAAA,CAAAI,YAAA,EAAa;IACbJ,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IAE9CH,EAF8C,CAAAI,YAAA,EAAY,EACvC,EACb;IAIFJ,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC9BJ,EAAA,CAAAE,SAAA,oBAC6E;IAEjFF,EADE,CAAAI,YAAA,EAAiB,EACb;IAIFJ,EAFJ,CAAAC,cAAA,cAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC/BJ,EAAA,CAAAE,SAAA,iBAAmF;IACnFF,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAC1BH,EAD0B,CAAAI,YAAA,EAAW,EACpB;IAGfJ,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC/BJ,EAAA,CAAAC,cAAA,sBAAqC;IACnCD,EAAA,CAAAsB,UAAA,KAAAE,kDAAA,yBAAmE;IAGrExB,EAAA,CAAAI,YAAA,EAAa;IACbJ,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAA6B;IAG9CH,EAH8C,CAAAI,YAAA,EAAY,EACrC,EACb,EACF;IAIJJ,EADF,CAAAC,cAAA,cAA0B,UACpB;IAAAD,EAAA,CAAAG,MAAA,yCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIjCJ,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5BJ,EAAA,CAAAE,SAAA,oBACkF;IAI1FF,EAHM,CAAAI,YAAA,EAAiB,EACb,EACF,EACF;IAIJJ,EADF,CAAAC,cAAA,eAA0B,kBACoD;IAA3CD,EAAA,CAAAe,UAAA,mBAAAU,8DAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAb,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASP,MAAA,CAAAa,QAAA,EAAU;IAAA,EAAC;IACnD1B,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8E;IAE5ED,EADA,CAAAsB,UAAA,KAAAK,mDAAA,0BAA4C,KAAAC,gDAAA,uBAChB;IAC5B5B,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACD;;;;IA9EDJ,EAAA,CAAAK,UAAA,cAAAQ,MAAA,CAAAgB,UAAA,CAAwB;IAUT7B,EAAA,CAAAO,SAAA,IAA2B;IAA3BP,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAiB,aAAA,SAA2B;IAQH9B,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAK,UAAA,YAAAQ,MAAA,CAAAkB,UAAA,CAAa;IAIrC/B,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAiB,aAAA,aAA+B;IAsBT9B,EAAA,CAAAO,SAAA,IAAW;IAAXP,EAAA,CAAAK,UAAA,YAAAQ,MAAA,CAAAmB,QAAA,CAAW;IAIjChC,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAiB,aAAA,WAA6B;IAqBQ9B,EAAA,CAAAO,SAAA,IAAqB;IAArBP,EAAA,CAAAK,UAAA,aAAAQ,MAAA,CAAAoB,QAAA,CAAqB;IAGnBjC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAK,UAAA,aAAAQ,MAAA,CAAAoB,QAAA,CAAqB;IAC7DjC,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAK,UAAA,SAAAQ,MAAA,CAAAoB,QAAA,CAAc;IACjBjC,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAK,UAAA,UAAAQ,MAAA,CAAAoB,QAAA,CAAe;IAC1BjC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAK,MAAA,CAAAoB,QAAA,yBAAApB,MAAA,CAAAC,UAAA,kCACF;;;AD3DV,OAAM,MAAOoB,mBAAmB;EAkC9BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IArClB,KAAA1B,UAAU,GAAG,KAAK;IAClB,KAAA2B,SAAS,GAAG,KAAK;IACjB,KAAAR,QAAQ,GAAG,KAAK;IAGhB,KAAAF,UAAU,GAAG,CACX,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,WAAW,EACX,aAAa,EACb,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,CACR;IAED,KAAAC,QAAQ,GAAG,CACT;MAAEtB,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EACnC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAS,CAAE,EACvC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACzC;IAEO,KAAA+B,QAAQ,GAAG,IAAI5C,OAAO,EAAQ;IASpC,IAAI,CAAC+B,UAAU,GAAG,IAAI,CAACc,UAAU,EAAE;EACrC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACL,KAAK,CAACM,MAAM,CAACC,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAACK,SAAS,CAACF,MAAM,IAAG;MAClE,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACG,QAAQ,GAAG,CAACH,MAAM,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC/B,UAAU,GAAG,IAAI;QACtB,IAAI,CAACmC,UAAU,EAAE;;IAErB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEQT,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACP,EAAE,CAACiB,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAChE,UAAU,CAACiE,QAAQ,EAAEjE,UAAU,CAACkE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAACiE,QAAQ,CAAC,CAAC;MACrCG,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAACvE,UAAU,CAACiE,QAAQ,CAAC;KACzC,CAAC;EACJ;EAEQN,UAAUA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;IAEpB,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,aAAa,CAACyB,SAAS,CAAC,IAAI,CAACd,QAAQ,CAAC,CACxCF,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAAC;MACTI,IAAI,EAAGY,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAAClC,UAAU,CAACmC,UAAU,CAACD,MAAM,CAAC;SACnC,MAAM;UACL,IAAI,CAACvB,QAAQ,CAACyB,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACrE,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;QAEpC,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACxB,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAAC5B,QAAQ,CAACyB,IAAI,CAAC,qCAAqC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAACzB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEApB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACQ,UAAU,CAACyC,KAAK,EAAE;MACzB,IAAI,CAACrC,QAAQ,GAAG,IAAI;MACpB,MAAMsC,SAAS,GAAG,IAAI,CAAC1C,UAAU,CAACnB,KAAK;MAEvC,IAAI,IAAI,CAACI,UAAU,IAAI,IAAI,CAACkC,QAAQ,EAAE;QACpC,MAAMwB,UAAU,GAAoB;UAClCC,EAAE,EAAE,IAAI,CAACzB,QAAQ;UACjB,GAAGuB;SACJ;QAED,IAAI,CAAClC,aAAa,CAACqC,YAAY,CAACF,UAAU,CAAC,CACxC1B,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAAC;UACTI,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACX,QAAQ,CAACyB,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC9E,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;UACpC,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;YAC9C,IAAI,CAAC5B,QAAQ,CAACyB,IAAI,CAAC,0CAA0C,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC5F,IAAI,CAACjC,QAAQ,GAAG,KAAK;UACvB;SACD,CAAC;OACL,MAAM;QACL,MAAM0C,UAAU,GAAoBJ,SAAS;QAE7C,IAAI,CAAClC,aAAa,CAACuC,YAAY,CAACD,UAAU,CAAC,CACxC7B,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAAC;UACTI,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACX,QAAQ,CAACyB,IAAI,CAAC,yBAAyB,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC3E,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;UACpC,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;YAC9C,IAAI,CAAC5B,QAAQ,CAACyB,IAAI,CAAC,sCAAsC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YACxF,IAAI,CAACjC,QAAQ,GAAG,KAAK;UACvB;SACD,CAAC;;KAEP,MAAM;MACL,IAAI,CAAC4C,oBAAoB,EAAE;;EAE/B;EAEAnD,QAAQA,CAAA;IACN,IAAI,CAACY,MAAM,CAAC6B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEQU,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClD,UAAU,CAACmD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAACtD,UAAU,CAACuD,GAAG,CAACF,GAAG,CAAC;MACxCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAvD,aAAaA,CAACwD,SAAiB;IAC7B,MAAMH,OAAO,GAAG,IAAI,CAACtD,UAAU,CAACuD,GAAG,CAACE,SAAS,CAAC;IAC9C,IAAIH,OAAO,EAAEI,MAAM,IAAIJ,OAAO,CAACK,OAAO,EAAE;MACtC,IAAIL,OAAO,CAACI,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,qBAAqB;;MAE9B,IAAIJ,OAAO,CAACI,MAAM,CAAC,OAAO,CAAC,EAAE;QAC3B,OAAO,0CAA0C;;MAEnD,IAAIJ,OAAO,CAACI,MAAM,CAAC,WAAW,CAAC,EAAE;QAC/B,OAAO,WAAWJ,OAAO,CAACI,MAAM,CAAC,WAAW,CAAC,CAACE,cAAc,aAAa;;;IAG7E,OAAO,EAAE;EACX;;;uBArKWvD,mBAAmB,EAAAlC,EAAA,CAAA0F,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5F,EAAA,CAAA0F,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA9F,EAAA,CAAA0F,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAhG,EAAA,CAAA0F,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAjG,EAAA,CAAA0F,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBjE,mBAAmB;MAAAkE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtG,EAAA,CAAAuG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/BxB7G,EAJR,CAAAC,cAAA,aAAmC,kBACL,sBACT,qBACC,eACJ;UAAAD,EAAA,CAAAG,MAAA,GAAiC;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACtDJ,EAAA,CAAAG,MAAA,GACF;UAAAH,EAAA,CAAAI,YAAA,EAAiB;UACjBJ,EAAA,CAAAC,cAAA,wBAAmB;UACjBD,EAAA,CAAAG,MAAA,GACF;UACFH,EADE,CAAAI,YAAA,EAAoB,EACJ;UAElBJ,EAAA,CAAAC,cAAA,uBAAkB;UAMhBD,EALA,CAAAsB,UAAA,KAAAyF,mCAAA,iBAAiD,KAAAC,oCAAA,oBAKyB;UAiFhFhH,EAFI,CAAAI,YAAA,EAAmB,EACV,EACP;;;UA/FYJ,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAY,iBAAA,CAAAkG,GAAA,CAAAhG,UAAA,kBAAiC;UAC3Cd,EAAA,CAAAO,SAAA,EACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAsG,GAAA,CAAAhG,UAAA,gDACF;UAEEd,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAsG,GAAA,CAAAhG,UAAA,sGACF;UAIMd,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAK,UAAA,SAAAyG,GAAA,CAAArE,SAAA,CAAe;UAKmCzC,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAK,UAAA,UAAAyG,GAAA,CAAArE,SAAA,CAAgB;;;qBDE1EtD,YAAY,EAAA8H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/H,YAAY,EACZC,mBAAmB,EAAAsG,EAAA,CAAAyB,aAAA,EAAAzB,EAAA,CAAA0B,oBAAA,EAAA1B,EAAA,CAAA2B,eAAA,EAAA3B,EAAA,CAAA4B,oBAAA,EAAA5B,EAAA,CAAA6B,kBAAA,EAAA7B,EAAA,CAAA8B,eAAA,EACnBlI,aAAa,EAAAmI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,YAAA,EACbvI,kBAAkB,EAAAwI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB3I,cAAc,EAAA4I,EAAA,CAAAC,QAAA,EACd5I,eAAe,EAAA6I,EAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf/I,eAAe,EAAAgJ,GAAA,CAAAC,SAAA,EACfhJ,aAAa,EAAAiJ,GAAA,CAAAC,OAAA,EAEbjJ,wBAAwB,EAAAkJ,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}