{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProjectService = /*#__PURE__*/(() => {\n  class ProjectService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = `${environment.apiUrl}/projects`;\n    }\n    getAll() {\n      return this.http.get(this.API_URL);\n    }\n    getById(id) {\n      // Mock data for development - replace with real API call when backend is ready\n      return new Observable(observer => {\n        setTimeout(() => {\n          const mockProjects = [{\n            id: 1,\n            role: 'Développeur Full Stack',\n            clientName: 'TechCorp',\n            clientId: 1,\n            dailyRate: 600,\n            workMode: 'HYBRID',\n            remoteDaysPerMonth: 15,\n            onsiteDaysPerMonth: 5,\n            techStack: 'React, Node.js, PostgreSQL',\n            description: 'Développement d\\'une application web moderne pour la gestion des ressources humaines. Le projet inclut la création d\\'un système complet de gestion des employés, des congés, et des évaluations de performance.',\n            advantages: 'Équipe dynamique, technologies modernes, possibilité de télétravail',\n            startDate: '2024-01-15',\n            durationInMonths: 6,\n            orderRenewalInMonths: 3,\n            daysPerYear: 220,\n            documents: ['Contrat signé', 'Cahier des charges', 'Spécifications techniques'],\n            link: 'https://techcorp.com/project-portal',\n            personalRating: 4,\n            notes: 'Excellent projet avec une équipe très professionnelle. Technologies intéressantes et défis techniques stimulants.',\n            freelanceId: 1,\n            sourceName: 'LinkedIn',\n            sourceId: 1,\n            totalRevenue: 72000,\n            totalSteps: 5,\n            completedSteps: 3,\n            failedSteps: 0\n          }, {\n            id: 2,\n            role: 'Consultant Angular',\n            clientName: 'StartupInnovante',\n            clientId: 2,\n            dailyRate: 550,\n            workMode: 'REMOTE',\n            remoteDaysPerMonth: 20,\n            onsiteDaysPerMonth: 0,\n            techStack: 'Angular, TypeScript, Firebase',\n            description: 'Refonte complète de l\\'interface utilisateur d\\'une plateforme e-commerce. Migration d\\'une ancienne application vers Angular 17 avec une nouvelle architecture moderne.',\n            advantages: 'Flexibilité totale, startup innovante, stack technique moderne',\n            startDate: '2024-03-01',\n            durationInMonths: 4,\n            orderRenewalInMonths: 2,\n            daysPerYear: 200,\n            documents: ['Contrat freelance', 'NDA', 'Guide de style'],\n            link: 'https://startup-innovante.com/dev-portal',\n            personalRating: 5,\n            notes: 'Projet très enrichissant avec beaucoup d\\'autonomie. Équipe jeune et dynamique.',\n            freelanceId: 1,\n            sourceName: 'Recommandation',\n            sourceId: 2,\n            totalRevenue: 44000,\n            totalSteps: 4,\n            completedSteps: 4,\n            failedSteps: 0\n          }, {\n            id: 3,\n            role: 'Architecte Solution',\n            clientName: 'GrandGroupe',\n            clientId: 3,\n            dailyRate: 750,\n            workMode: 'ONSITE',\n            remoteDaysPerMonth: 0,\n            onsiteDaysPerMonth: 20,\n            techStack: 'Java, Spring Boot, Microservices',\n            description: 'Architecture et mise en place d\\'une solution microservices pour un système bancaire. Conception de l\\'architecture globale et accompagnement des équipes de développement.',\n            advantages: 'Projet d\\'envergure, équipe expérimentée, secteur bancaire stable',\n            startDate: '2024-06-01',\n            durationInMonths: 12,\n            orderRenewalInMonths: 6,\n            daysPerYear: 230,\n            documents: ['Contrat cadre', 'Habilitation bancaire', 'Architecture document'],\n            link: 'https://grandgroupe.com/contractor-portal',\n            personalRating: 3,\n            notes: 'Projet complexe avec beaucoup de contraintes réglementaires. Environnement très structuré.',\n            freelanceId: 1,\n            sourceName: 'Cabinet de recrutement',\n            sourceId: 3,\n            totalRevenue: 180000,\n            totalSteps: 6,\n            completedSteps: 2,\n            failedSteps: 1\n          }];\n          const project = mockProjects.find(p => p.id === id);\n          if (project) {\n            observer.next(project);\n          } else {\n            observer.error(new Error('Project not found'));\n          }\n          observer.complete();\n        }, 300);\n      });\n    }\n    getByIdWithSteps(id) {\n      return this.http.get(`${this.API_URL}/${id}/with-steps`);\n    }\n    getByFreelanceId(freelanceId) {\n      // Mock data for development - replace with real API call when backend is ready\n      return new Observable(observer => {\n        setTimeout(() => {\n          const mockProjects = [{\n            id: 1,\n            role: 'Développeur Full Stack',\n            clientName: 'TechCorp',\n            dailyRate: 600,\n            workMode: 'HYBRID',\n            techStack: 'React, Node.js, PostgreSQL',\n            description: 'Développement d\\'une application web moderne pour la gestion des ressources humaines.',\n            startDate: '2024-01-15',\n            durationInMonths: 6,\n            daysPerYear: 220\n          }, {\n            id: 2,\n            role: 'Consultant Angular',\n            clientName: 'StartupInnovante',\n            dailyRate: 550,\n            workMode: 'REMOTE',\n            techStack: 'Angular, TypeScript, Firebase',\n            description: 'Refonte complète de l\\'interface utilisateur d\\'une plateforme e-commerce.',\n            startDate: '2024-03-01',\n            durationInMonths: 4,\n            daysPerYear: 200\n          }, {\n            id: 3,\n            role: 'Architecte Solution',\n            clientName: 'GrandGroupe',\n            dailyRate: 750,\n            workMode: 'ONSITE',\n            techStack: 'Java, Spring Boot, Microservices',\n            description: 'Architecture et mise en place d\\'une solution microservices pour un système bancaire.',\n            startDate: '2024-06-01',\n            durationInMonths: 12,\n            daysPerYear: 230\n          }];\n          observer.next(mockProjects);\n          observer.complete();\n        }, 500);\n      });\n    }\n    getByClientId(clientId) {\n      return this.http.get(`${this.API_URL}/by-client/${clientId}`);\n    }\n    getByFreelanceIdWithFilters(freelanceId, filters) {\n      let params = new HttpParams();\n      if (filters.minRate !== undefined) {\n        params = params.set('minRate', filters.minRate.toString());\n      }\n      if (filters.maxRate !== undefined) {\n        params = params.set('maxRate', filters.maxRate.toString());\n      }\n      if (filters.workMode) {\n        params = params.set('workMode', filters.workMode);\n      }\n      if (filters.startDateAfter) {\n        params = params.set('startDateAfter', filters.startDateAfter);\n      }\n      if (filters.techStack) {\n        params = params.set('techStack', filters.techStack);\n      }\n      return this.http.get(`${this.API_URL}/by-freelance/${freelanceId}/filtered`, {\n        params\n      });\n    }\n    create(project) {\n      return this.http.post(this.API_URL, project);\n    }\n    update(id, project) {\n      return this.http.put(`${this.API_URL}/${id}`, project);\n    }\n    delete(id) {\n      return this.http.delete(`${this.API_URL}/${id}`);\n    }\n    getAverageDailyRate(freelanceId) {\n      return this.http.get(`${this.API_URL}/stats/average-rate/${freelanceId}`);\n    }\n    getProjectCount(freelanceId) {\n      return this.http.get(`${this.API_URL}/stats/count/${freelanceId}`);\n    }\n    static {\n      this.ɵfac = function ProjectService_Factory(t) {\n        return new (t || ProjectService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProjectService,\n        factory: ProjectService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProjectService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}