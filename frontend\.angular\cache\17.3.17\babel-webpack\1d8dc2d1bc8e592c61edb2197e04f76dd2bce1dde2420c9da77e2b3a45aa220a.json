{"ast": null, "code": "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isReadableStreamLike } from '../util/isReadableStreamLike';\nimport { scheduleReadableStreamLike } from './scheduleReadableStreamLike';\nexport function scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    }\n    if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    }\n    if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    }\n    if (isAsyncIterable(input)) {\n      return scheduleAsyncIterable(input, scheduler);\n    }\n    if (isIterable(input)) {\n      return scheduleIterable(input, scheduler);\n    }\n    if (isReadableStreamLike(input)) {\n      return scheduleReadableStreamLike(input, scheduler);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}", "map": {"version": 3, "names": ["scheduleObservable", "schedulePromise", "scheduleArray", "scheduleIterable", "scheduleAsyncIterable", "isInteropObservable", "isPromise", "isArrayLike", "isIterable", "isAsyncIterable", "createInvalidObservableTypeError", "isReadableStreamLike", "scheduleReadableStreamLike", "scheduled", "input", "scheduler"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduled.js"], "sourcesContent": ["import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isReadableStreamLike } from '../util/isReadableStreamLike';\nimport { scheduleReadableStreamLike } from './scheduleReadableStreamLike';\nexport function scheduled(input, scheduler) {\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return scheduleObservable(input, scheduler);\n        }\n        if (isArrayLike(input)) {\n            return scheduleArray(input, scheduler);\n        }\n        if (isPromise(input)) {\n            return schedulePromise(input, scheduler);\n        }\n        if (isAsyncIterable(input)) {\n            return scheduleAsyncIterable(input, scheduler);\n        }\n        if (isIterable(input)) {\n            return scheduleIterable(input, scheduler);\n        }\n        if (isReadableStreamLike(input)) {\n            return scheduleReadableStreamLike(input, scheduler);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,gCAAgC,QAAQ,gCAAgC;AACjF,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxC,IAAID,KAAK,IAAI,IAAI,EAAE;IACf,IAAIT,mBAAmB,CAACS,KAAK,CAAC,EAAE;MAC5B,OAAOd,kBAAkB,CAACc,KAAK,EAAEC,SAAS,CAAC;IAC/C;IACA,IAAIR,WAAW,CAACO,KAAK,CAAC,EAAE;MACpB,OAAOZ,aAAa,CAACY,KAAK,EAAEC,SAAS,CAAC;IAC1C;IACA,IAAIT,SAAS,CAACQ,KAAK,CAAC,EAAE;MAClB,OAAOb,eAAe,CAACa,KAAK,EAAEC,SAAS,CAAC;IAC5C;IACA,IAAIN,eAAe,CAACK,KAAK,CAAC,EAAE;MACxB,OAAOV,qBAAqB,CAACU,KAAK,EAAEC,SAAS,CAAC;IAClD;IACA,IAAIP,UAAU,CAACM,KAAK,CAAC,EAAE;MACnB,OAAOX,gBAAgB,CAACW,KAAK,EAAEC,SAAS,CAAC;IAC7C;IACA,IAAIJ,oBAAoB,CAACG,KAAK,CAAC,EAAE;MAC7B,OAAOF,0BAA0B,CAACE,KAAK,EAAEC,SAAS,CAAC;IACvD;EACJ;EACA,MAAML,gCAAgC,CAACI,KAAK,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}