{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/card\";\nimport * as i2 from \"@angular/material/icon\";\nexport class ProfileComponent {\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"profile-container\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Profil utilisateur \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\");\n          i0.ɵɵtext(9, \"Cette fonctionnalit\\u00E9 sera bient\\u00F4t disponible.\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [CommonModule, MatCardModule, i1.MatCard, i1.MatCardContent, i1.MatCardHeader, i1.MatCardTitle, MatButtonModule, MatIconModule, i2.MatIcon],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nmat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9wcm9maWxlL3Byb2ZpbGUuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsYUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQUFOIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnByb2ZpbGUtY29udGFpbmVyIHtcbiAgICAgIHBhZGRpbmc6IDI0cHg7XG4gICAgICBtYXgtd2lkdGg6IDgwMHB4O1xuICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgfVxuICAgIFxuICAgIG1hdC1jYXJkLXRpdGxlIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "ProfileComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "i1", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i2", "MatIcon", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\profile\\profile.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\n\n@Component({\n  selector: 'app-profile',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule\n  ],\n  template: `\n    <div class=\"profile-container\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>person</mat-icon>\n            Profil utilisateur\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <p>Cette fonctionnalité sera bientôt disponible.</p>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .profile-container {\n      padding: 24px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n    \n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n  `]\n})\nexport class ProfileComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;AAwCtD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxBjBP,EAJR,CAAAS,cAAA,aAA+B,eACnB,sBACS,qBACC,eACJ;UAAAT,EAAA,CAAAU,MAAA,aAAM;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAC3BX,EAAA,CAAAU,MAAA,2BACF;UACFV,EADE,CAAAW,YAAA,EAAiB,EACD;UAEhBX,EADF,CAAAS,cAAA,uBAAkB,QACb;UAAAT,EAAA,CAAAU,MAAA,8DAA6C;UAGtDV,EAHsD,CAAAW,YAAA,EAAI,EACnC,EACV,EACP;;;qBAlBNnB,YAAY,EACZC,aAAa,EAAAmB,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbtB,eAAe,EACfC,aAAa,EAAAsB,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}