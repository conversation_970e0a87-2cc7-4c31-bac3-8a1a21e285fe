{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/client.service\";\nimport * as i2 from \"../../../services/contact.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/chips\";\nimport * as i11 from \"@angular/material/divider\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/table\";\nimport * as i14 from \"@angular/material/tabs\";\nimport * as i15 from \"@angular/material/menu\";\nfunction ClientDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails du client...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientDetailComponent_div_2_mat_divider_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ClientDetailComponent_div_2_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Site web \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"a\", 36);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_63_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openWebsite());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"open_in_new\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"href\", ctx_r1.client.website, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.website, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 27)(5, \"div\", 28);\n    i0.ɵɵtext(6, \"Derni\\u00E8re modification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.client.updatedAt));\n  }\n}\nfunction ClientDetailComponent_div_2_mat_card_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 37)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Notes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.notes, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des contacts...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientDetailComponent_div_2_div_123_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Aucun contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Ce client n'a pas encore de contacts associ\\u00E9s.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_123_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddContact());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Ajouter le premier contact \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Nom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 53)(1, \"div\", 54)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r5 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", contact_r5.firstName, \" \", contact_r5.lastName, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 53)(1, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_7_Template_a_click_1_listener() {\n      const contact_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendContactEmail(contact_r7));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"mailto:\" + contact_r7.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", contact_r7.email, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 53)(1, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_10_Template_a_click_1_listener() {\n      const contact_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.callContactPhone(contact_r9));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"tel:\" + contact_r9.phone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", contact_r9.phone, \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Poste\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(contact_r10.position);\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Statut\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 53)(1, \"mat-chip\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.getContactStatusColor(contact_r11.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getContactStatusLabel(contact_r11.status), \" \");\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_19_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_19_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const contact_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendContactEmail(contact_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Envoyer email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_19_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_19_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const contact_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.callContactPhone(contact_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Appeler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 53)(1, \"button\", 55)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 0)(6, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_19_Template_button_click_6_listener() {\n      const contact_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onViewContact(contact_r13));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Voir d\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_19_Template_button_click_10_listener() {\n      const contact_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onEditContact(contact_r13));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ClientDetailComponent_div_2_div_124_td_19_button_14_Template, 4, 0, \"button\", 57)(15, ClientDetailComponent_div_2_div_124_td_19_button_15_Template, 4, 0, \"button\", 57);\n    i0.ɵɵelement(16, \"mat-divider\");\n    i0.ɵɵelementStart(17, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_div_124_td_19_Template_button_click_17_listener() {\n      const contact_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDeleteContact(contact_r13));\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r13 = ctx.$implicit;\n    const contactMenu_r16 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", contactMenu_r16);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", contact_r13.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", contact_r13.phone);\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 59);\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 60);\n  }\n}\nfunction ClientDetailComponent_div_2_div_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"table\", 41);\n    i0.ɵɵelementContainerStart(2, 42);\n    i0.ɵɵtemplate(3, ClientDetailComponent_div_2_div_124_th_3_Template, 2, 0, \"th\", 43)(4, ClientDetailComponent_div_2_div_124_td_4_Template, 5, 2, \"td\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 45);\n    i0.ɵɵtemplate(6, ClientDetailComponent_div_2_div_124_th_6_Template, 2, 0, \"th\", 43)(7, ClientDetailComponent_div_2_div_124_td_7_Template, 3, 2, \"td\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 46);\n    i0.ɵɵtemplate(9, ClientDetailComponent_div_2_div_124_th_9_Template, 2, 0, \"th\", 43)(10, ClientDetailComponent_div_2_div_124_td_10_Template, 3, 2, \"td\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 47);\n    i0.ɵɵtemplate(12, ClientDetailComponent_div_2_div_124_th_12_Template, 2, 0, \"th\", 43)(13, ClientDetailComponent_div_2_div_124_td_13_Template, 2, 1, \"td\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 48);\n    i0.ɵɵtemplate(15, ClientDetailComponent_div_2_div_124_th_15_Template, 2, 0, \"th\", 43)(16, ClientDetailComponent_div_2_div_124_td_16_Template, 3, 2, \"td\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 49);\n    i0.ɵɵtemplate(18, ClientDetailComponent_div_2_div_124_th_18_Template, 2, 0, \"th\", 43)(19, ClientDetailComponent_div_2_div_124_td_19_Template, 21, 3, \"td\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, ClientDetailComponent_div_2_div_124_tr_20_Template, 1, 0, \"tr\", 50)(21, ClientDetailComponent_div_2_div_124_tr_21_Template, 1, 0, \"tr\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.contacts);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.contactDisplayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.contactDisplayedColumns);\n  }\n}\nfunction ClientDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-card\", 6)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"div\", 7)(5, \"div\", 8)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip\", 9);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-subtitle\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-card-actions\")(14, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBack());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onEdit());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDelete());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"mat-tab-group\", 13);\n    i0.ɵɵtwoWayListener(\"selectedIndexChange\", function ClientDetailComponent_div_2_Template_mat_tab_group_selectedIndexChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedTabIndex, $event) || (ctx_r1.selectedTabIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(27, \"mat-tab\", 14)(28, \"div\", 15)(29, \"div\", 16)(30, \"mat-card\", 17)(31, \"mat-card-header\")(32, \"mat-card-title\")(33, \"mat-icon\");\n    i0.ɵɵtext(34, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(35, \" Informations de contact \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-card-content\")(37, \"div\", 18)(38, \"div\", 19)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 20)(43, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_a_click_43_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendEmail());\n    });\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(45, \"mat-divider\");\n    i0.ɵɵelementStart(46, \"div\", 18)(47, \"div\", 19)(48, \"mat-icon\");\n    i0.ɵɵtext(49, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(50, \" T\\u00E9l\\u00E9phone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 20)(52, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_a_click_52_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.callPhone());\n    });\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(54, \"mat-divider\");\n    i0.ɵɵelementStart(55, \"div\", 18)(56, \"div\", 19)(57, \"mat-icon\");\n    i0.ɵɵtext(58, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(59, \" Adresse \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 20);\n    i0.ɵɵtext(61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(62, ClientDetailComponent_div_2_mat_divider_62_Template, 1, 0, \"mat-divider\", 22)(63, ClientDetailComponent_div_2_div_63_Template, 10, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"mat-card\", 17)(65, \"mat-card-header\")(66, \"mat-card-title\")(67, \"mat-icon\");\n    i0.ɵɵtext(68, \"business_center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(69, \" Informations entreprise \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"mat-card-content\")(71, \"div\", 18)(72, \"div\", 19)(73, \"mat-icon\");\n    i0.ɵɵtext(74, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(75, \" Personne de contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"div\", 20);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(78, \"mat-divider\");\n    i0.ɵɵelementStart(79, \"div\", 18)(80, \"div\", 19)(81, \"mat-icon\");\n    i0.ɵɵtext(82, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(83, \" Secteur d'activit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"div\", 20);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(86, \"mat-divider\");\n    i0.ɵɵelementStart(87, \"div\", 18)(88, \"div\", 19)(89, \"mat-icon\");\n    i0.ɵɵtext(90, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(91, \" Statut \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(92, \"div\", 20)(93, \"mat-chip\", 9);\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(95, \"mat-card\", 24)(96, \"mat-card-header\")(97, \"mat-card-title\")(98, \"mat-icon\");\n    i0.ɵɵtext(99, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \" Historique \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(101, \"mat-card-content\")(102, \"div\", 25)(103, \"div\", 26)(104, \"mat-icon\");\n    i0.ɵɵtext(105, \"add_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(106, \"div\", 27)(107, \"div\", 28);\n    i0.ɵɵtext(108, \"Client cr\\u00E9\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"div\", 29);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(111, ClientDetailComponent_div_2_div_111_Template, 9, 1, \"div\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(112, ClientDetailComponent_div_2_mat_card_112_Template, 9, 1, \"mat-card\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(113, \"mat-tab\", 32)(114, \"div\", 15)(115, \"div\", 33)(116, \"h3\");\n    i0.ɵɵtext(117, \"Contacts associ\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(118, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ClientDetailComponent_div_2_Template_button_click_118_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddContact());\n    });\n    i0.ɵɵelementStart(119, \"mat-icon\");\n    i0.ɵɵtext(120, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Ajouter un contact \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(122, ClientDetailComponent_div_2_div_122_Template, 4, 0, \"div\", 2)(123, ClientDetailComponent_div_2_div_123_Template, 11, 0, \"div\", 34)(124, ClientDetailComponent_div_2_div_124_Template, 22, 3, \"div\", 35);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.getStatusColor(ctx_r1.client.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusLabel(ctx_r1.client.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Contact: \", ctx_r1.client.contactPerson, \" \\u2022 \", ctx_r1.client.industry, \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"selectedIndex\", ctx_r1.selectedTabIndex);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"href\", \"mailto:\" + ctx_r1.client.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.email, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"tel:\" + ctx_r1.client.phone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.phone, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.address, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.website);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.website);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.contactPerson, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.industry, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"color\", ctx_r1.getStatusColor(ctx_r1.client.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusLabel(ctx_r1.client.status), \" \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.client.createdAt));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.updatedAt !== ctx_r1.client.createdAt);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.notes);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingContacts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingContacts && ctx_r1.contacts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingContacts && ctx_r1.contacts.length > 0);\n  }\n}\nexport class ClientDetailComponent {\n  constructor(clientService, contactService, router, route, snackBar, dialog) {\n    this.clientService = clientService;\n    this.contactService = contactService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.contacts = [];\n    this.isLoading = false;\n    this.isLoadingContacts = false;\n    this.selectedTabIndex = 0;\n    // Contact table columns\n    this.contactDisplayedColumns = ['name', 'email', 'phone', 'position', 'status', 'actions'];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.clientId = +params['id'];\n        this.loadClientAndContacts();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadClientAndContacts() {\n    if (!this.clientId) return;\n    this.isLoading = true;\n    this.isLoadingContacts = true;\n    // Load both client and contacts in parallel\n    forkJoin({\n      client: this.clientService.getClient(this.clientId),\n      contacts: this.contactService.getContactsByClient(this.clientId)\n    }).pipe(takeUntil(this.destroy$)).subscribe({\n      next: ({\n        client,\n        contacts\n      }) => {\n        if (client) {\n          this.client = client;\n          this.contacts = contacts;\n        } else {\n          this.snackBar.open('Client non trouvé', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/clients']);\n        }\n        this.isLoading = false;\n        this.isLoadingContacts = false;\n      },\n      error: error => {\n        console.error('Error loading client and contacts:', error);\n        this.snackBar.open('Erreur lors du chargement des données', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n        this.isLoadingContacts = false;\n      }\n    });\n  }\n  loadContacts() {\n    if (!this.clientId) return;\n    this.isLoadingContacts = true;\n    this.contactService.getContactsByClient(this.clientId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: contacts => {\n        this.contacts = contacts;\n        this.isLoadingContacts = false;\n      },\n      error: error => {\n        console.error('Error loading contacts:', error);\n        this.snackBar.open('Erreur lors du chargement des contacts', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoadingContacts = false;\n      }\n    });\n  }\n  onEdit() {\n    if (this.client) {\n      this.router.navigate(['/clients', this.client.id, 'edit']);\n    }\n  }\n  onDelete() {\n    if (!this.client) return;\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${this.client.name}\" ?`)) {\n      this.clientService.deleteClient(this.client.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/clients']);\n        },\n        error: error => {\n          console.error('Error deleting client:', error);\n          this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  onBack() {\n    this.router.navigate(['/clients']);\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n  getStatusLabel(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  openWebsite() {\n    if (this.client?.website) {\n      window.open(this.client.website, '_blank');\n    }\n  }\n  sendEmail() {\n    if (this.client?.email) {\n      window.location.href = `mailto:${this.client.email}`;\n    }\n  }\n  callPhone() {\n    if (this.client?.phone) {\n      window.location.href = `tel:${this.client.phone}`;\n    }\n  }\n  // Contact management methods\n  onAddContact() {\n    if (!this.clientId) return;\n    this.router.navigate(['/contacts/create'], {\n      queryParams: {\n        clientId: this.clientId\n      }\n    });\n  }\n  onEditContact(contact) {\n    this.router.navigate(['/contacts', contact.id, 'edit']);\n  }\n  onViewContact(contact) {\n    this.router.navigate(['/contacts', contact.id]);\n  }\n  onDeleteContact(contact) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le contact \"${contact.firstName} ${contact.lastName}\" ?`)) {\n      this.contactService.deleteContact(contact.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Contact supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.loadContacts(); // Reload contacts\n        },\n        error: error => {\n          console.error('Error deleting contact:', error);\n          this.snackBar.open('Erreur lors de la suppression du contact', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  getContactStatusColor(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      default:\n        return '';\n    }\n  }\n  getContactStatusLabel(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      default:\n        return status;\n    }\n  }\n  sendContactEmail(contact) {\n    if (contact.email) {\n      window.location.href = `mailto:${contact.email}`;\n    }\n  }\n  callContactPhone(contact) {\n    if (contact.phone) {\n      window.location.href = `tel:${contact.phone}`;\n    }\n  }\n  static {\n    this.ɵfac = function ClientDetailComponent_Factory(t) {\n      return new (t || ClientDetailComponent)(i0.ɵɵdirectiveInject(i1.ClientService), i0.ɵɵdirectiveInject(i2.ContactService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClientDetailComponent,\n      selectors: [[\"app-client-detail\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"contactMenu\", \"matMenu\"], [1, \"client-detail-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"client-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"client-content\"], [1, \"header-card\"], [1, \"title-row\"], [1, \"client-name\"], [\"selected\", \"\", 3, \"color\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"client-tabs\", 3, \"selectedIndexChange\", \"selectedIndex\"], [\"label\", \"D\\u00E9tails du client\"], [1, \"tab-content\"], [1, \"details-grid\"], [1, \"info-card\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [1, \"contact-link\", 3, \"click\", \"href\"], [4, \"ngIf\"], [\"class\", \"info-item\", 4, \"ngIf\"], [1, \"info-card\", \"timeline-card\"], [1, \"timeline-item\"], [1, \"timeline-icon\"], [1, \"timeline-content\"], [1, \"timeline-title\"], [1, \"timeline-date\"], [\"class\", \"timeline-item\", 4, \"ngIf\"], [\"class\", \"info-card notes-card\", 4, \"ngIf\"], [\"label\", \"Contacts\"], [1, \"contacts-header\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"contacts-table-container\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"contact-link\", 3, \"click\", \"href\"], [1, \"info-card\", \"notes-card\"], [1, \"notes-content\"], [1, \"empty-state\"], [1, \"contacts-table-container\"], [\"mat-table\", \"\", 1, \"contacts-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"email\"], [\"matColumnDef\", \"phone\"], [\"matColumnDef\", \"position\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"contact-name\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 1, \"delete-action\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function ClientDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, ClientDetailComponent_div_1_Template, 4, 0, \"div\", 2)(2, ClientDetailComponent_div_2_Template, 125, 23, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.client);\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, RouterModule, MatCardModule, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatChipsModule, i10.MatChip, MatDividerModule, i11.MatDivider, MatProgressSpinnerModule, i12.MatProgressSpinner, MatTableModule, i13.MatTable, i13.MatHeaderCellDef, i13.MatHeaderRowDef, i13.MatColumnDef, i13.MatCellDef, i13.MatRowDef, i13.MatHeaderCell, i13.MatCell, i13.MatHeaderRow, i13.MatRow, MatTabsModule, i14.MatTab, i14.MatTabGroup, MatMenuModule, i15.MatMenu, i15.MatMenuItem, i15.MatMenuTrigger, MatDialogModule],\n      styles: [\".client-detail-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n@media (max-width: 600px) {\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 12px;\\n  }\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 0.9rem;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 16px 24px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n}\\n@media (max-width: 600px) {\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 120px;\\n  }\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n@media (max-width: 768px) {\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 1.1rem;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding-top: 16px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 12px 0;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n  min-width: 120px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: right;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%] {\\n  color: var(--mdc-theme-primary, #1976d2);\\n  text-decoration: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  gap: 4px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 0.9rem;\\n  height: 0.9rem;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n@media (max-width: 600px) {\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 4px;\\n  }\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%] {\\n    justify-content: flex-start;\\n  }\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  padding: 12px 0;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:not(:last-child) {\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--mdc-theme-primary, #1976d2);\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.87);\\n  margin-bottom: 4px;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.timeline-card[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-date[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%]   .notes-content[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-left: 4px solid var(--mdc-theme-primary, #1976d2);\\n  padding: 16px;\\n  border-radius: 4px;\\n  line-height: 1.6;\\n  white-space: pre-wrap;\\n}\\n\\n.mat-mdc-chip[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  min-height: 28px;\\n}\\n.mat-mdc-chip.mat-primary[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n.mat-mdc-chip.mat-accent[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #f57c00;\\n}\\n.mat-mdc-chip.mat-warn[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n}\\n\\n.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%] {\\n  background-color: var(--mdc-theme-primary, #1976d2);\\n  color: white;\\n}\\n\\n.mat-mdc-button.mat-warn[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.mat-mdc-button.mat-warn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(211, 47, 47, 0.04);\\n}\\n\\n@media (max-width: 768px) {\\n  .client-detail-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .client-detail-container[_ngcontent-%COMP%]   .client-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatDividerModule", "MatProgressSpinnerModule", "MatTableModule", "MatTabsModule", "MatMenuModule", "MatDialogModule", "Subject", "takeUntil", "fork<PERSON><PERSON>n", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ClientDetailComponent_div_2_div_63_Template_a_click_6_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "openWebsite", "ɵɵadvance", "ɵɵproperty", "client", "website", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "formatDate", "updatedAt", "notes", "ClientDetailComponent_div_2_div_123_Template_button_click_7_listener", "_r4", "onAddContact", "ɵɵtextInterpolate2", "contact_r5", "firstName", "lastName", "ClientDetailComponent_div_2_div_124_td_7_Template_a_click_1_listener", "contact_r7", "_r6", "$implicit", "sendContactEmail", "email", "ClientDetailComponent_div_2_div_124_td_10_Template_a_click_1_listener", "contact_r9", "_r8", "callContactPhone", "phone", "contact_r10", "position", "getContactStatusColor", "contact_r11", "status", "getContactStatusLabel", "ClientDetailComponent_div_2_div_124_td_19_button_14_Template_button_click_0_listener", "_r14", "contact_r13", "ClientDetailComponent_div_2_div_124_td_19_button_15_Template_button_click_0_listener", "_r15", "ClientDetailComponent_div_2_div_124_td_19_Template_button_click_6_listener", "_r12", "onViewContact", "ClientDetailComponent_div_2_div_124_td_19_Template_button_click_10_listener", "onEditContact", "ɵɵtemplate", "ClientDetailComponent_div_2_div_124_td_19_button_14_Template", "ClientDetailComponent_div_2_div_124_td_19_button_15_Template", "ClientDetailComponent_div_2_div_124_td_19_Template_button_click_17_listener", "onDeleteContact", "contactMenu_r16", "ɵɵelementContainerStart", "ClientDetailComponent_div_2_div_124_th_3_Template", "ClientDetailComponent_div_2_div_124_td_4_Template", "ClientDetailComponent_div_2_div_124_th_6_Template", "ClientDetailComponent_div_2_div_124_td_7_Template", "ClientDetailComponent_div_2_div_124_th_9_Template", "ClientDetailComponent_div_2_div_124_td_10_Template", "ClientDetailComponent_div_2_div_124_th_12_Template", "ClientDetailComponent_div_2_div_124_td_13_Template", "ClientDetailComponent_div_2_div_124_th_15_Template", "ClientDetailComponent_div_2_div_124_td_16_Template", "ClientDetailComponent_div_2_div_124_th_18_Template", "ClientDetailComponent_div_2_div_124_td_19_Template", "ClientDetailComponent_div_2_div_124_tr_20_Template", "ClientDetailComponent_div_2_div_124_tr_21_Template", "contacts", "contactDisplayedColumns", "ClientDetailComponent_div_2_Template_button_click_14_listener", "_r1", "onBack", "ClientDetailComponent_div_2_Template_button_click_18_listener", "onEdit", "ClientDetailComponent_div_2_Template_button_click_22_listener", "onDelete", "ɵɵtwoWayListener", "ClientDetailComponent_div_2_Template_mat_tab_group_selectedIndexChange_26_listener", "$event", "ɵɵtwoWayBindingSet", "selectedTabIndex", "ClientDetailComponent_div_2_Template_a_click_43_listener", "sendEmail", "ClientDetailComponent_div_2_Template_a_click_52_listener", "callPhone", "ClientDetailComponent_div_2_mat_divider_62_Template", "ClientDetailComponent_div_2_div_63_Template", "ClientDetailComponent_div_2_div_111_Template", "ClientDetailComponent_div_2_mat_card_112_Template", "ClientDetailComponent_div_2_Template_button_click_118_listener", "ClientDetailComponent_div_2_div_122_Template", "ClientDetailComponent_div_2_div_123_Template", "ClientDetailComponent_div_2_div_124_Template", "name", "getStatusColor", "getStatusLabel", "<PERSON><PERSON><PERSON>", "industry", "ɵɵtwoWayProperty", "address", "createdAt", "isLoadingContacts", "length", "ClientDetailComponent", "constructor", "clientService", "contactService", "router", "route", "snackBar", "dialog", "isLoading", "destroy$", "ngOnInit", "params", "pipe", "subscribe", "clientId", "loadClientAndContacts", "ngOnDestroy", "next", "complete", "getClient", "getContactsByClient", "open", "duration", "navigate", "error", "console", "loadContacts", "id", "confirm", "deleteClient", "date", "Date", "toLocaleDateString", "year", "month", "day", "window", "location", "href", "queryParams", "contact", "deleteContact", "ɵɵdirectiveInject", "i1", "ClientService", "i2", "ContactService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "i5", "MatDialog", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ClientDetailComponent_Template", "rf", "ctx", "ClientDetailComponent_div_1_Template", "ClientDetailComponent_div_2_Template", "i6", "NgIf", "i7", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatChip", "i11", "<PERSON><PERSON><PERSON><PERSON>", "i12", "MatProgressSpinner", "i13", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i14", "Mat<PERSON><PERSON>", "MatTabGroup", "i15", "MatMenu", "MatMenuItem", "MatMenuTrigger", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-detail\\client-detail.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-detail\\client-detail.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\n\nimport { ClientService, ClientDto } from '../../../services/client.service';\nimport { ContactService, ContactDto } from '../../../services/contact.service';\n\n@Component({\n  selector: 'app-client-detail',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    MatTableModule,\n    MatTabsModule,\n    MatMenuModule,\n    MatDialogModule\n  ],\n  templateUrl: './client-detail.component.html',\n  styleUrls: ['./client-detail.component.scss']\n})\nexport class ClientDetailComponent implements OnInit, OnDestroy {\n  client?: ClientDto;\n  contacts: ContactDto[] = [];\n  isLoading = false;\n  isLoadingContacts = false;\n  clientId?: number;\n  selectedTabIndex = 0;\n\n  // Contact table columns\n  contactDisplayedColumns: string[] = ['name', 'email', 'phone', 'position', 'status', 'actions'];\n\n  private readonly destroy$ = new Subject<void>();\n\n  constructor(\n    private readonly clientService: ClientService,\n    private readonly contactService: ContactService,\n    private readonly router: Router,\n    private readonly route: ActivatedRoute,\n    private readonly snackBar: MatSnackBar,\n    private readonly dialog: MatDialog\n  ) {}\n\n  ngOnInit(): void {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.clientId = +params['id'];\n        this.loadClientAndContacts();\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private loadClientAndContacts(): void {\n    if (!this.clientId) return;\n\n    this.isLoading = true;\n    this.isLoadingContacts = true;\n\n    // Load both client and contacts in parallel\n    forkJoin({\n      client: this.clientService.getClient(this.clientId),\n      contacts: this.contactService.getContactsByClient(this.clientId)\n    }).pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: ({ client, contacts }) => {\n          if (client) {\n            this.client = client;\n            this.contacts = contacts;\n          } else {\n            this.snackBar.open('Client non trouvé', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/clients']);\n          }\n          this.isLoading = false;\n          this.isLoadingContacts = false;\n        },\n        error: (error) => {\n          console.error('Error loading client and contacts:', error);\n          this.snackBar.open('Erreur lors du chargement des données', 'Fermer', { duration: 3000 });\n          this.isLoading = false;\n          this.isLoadingContacts = false;\n        }\n      });\n  }\n\n  private loadContacts(): void {\n    if (!this.clientId) return;\n\n    this.isLoadingContacts = true;\n    this.contactService.getContactsByClient(this.clientId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (contacts) => {\n          this.contacts = contacts;\n          this.isLoadingContacts = false;\n        },\n        error: (error) => {\n          console.error('Error loading contacts:', error);\n          this.snackBar.open('Erreur lors du chargement des contacts', 'Fermer', { duration: 3000 });\n          this.isLoadingContacts = false;\n        }\n      });\n  }\n\n  onEdit(): void {\n    if (this.client) {\n      this.router.navigate(['/clients', this.client.id, 'edit']);\n    }\n  }\n\n  onDelete(): void {\n    if (!this.client) return;\n    \n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${this.client.name}\" ?`)) {\n      this.clientService.deleteClient(this.client.id)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Client supprimé avec succès', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/clients']);\n          },\n          error: (error) => {\n            console.error('Error deleting client:', error);\n            this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', { duration: 3000 });\n          }\n        });\n    }\n  }\n\n  onBack(): void {\n    this.router.navigate(['/clients']);\n  }\n\n  getStatusColor(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n\n  getStatusLabel(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  openWebsite(): void {\n    if (this.client?.website) {\n      window.open(this.client.website, '_blank');\n    }\n  }\n\n  sendEmail(): void {\n    if (this.client?.email) {\n      window.location.href = `mailto:${this.client.email}`;\n    }\n  }\n\n  callPhone(): void {\n    if (this.client?.phone) {\n      window.location.href = `tel:${this.client.phone}`;\n    }\n  }\n\n  // Contact management methods\n  onAddContact(): void {\n    if (!this.clientId) return;\n\n    this.router.navigate(['/contacts/create'], {\n      queryParams: { clientId: this.clientId }\n    });\n  }\n\n  onEditContact(contact: ContactDto): void {\n    this.router.navigate(['/contacts', contact.id, 'edit']);\n  }\n\n  onViewContact(contact: ContactDto): void {\n    this.router.navigate(['/contacts', contact.id]);\n  }\n\n  onDeleteContact(contact: ContactDto): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le contact \"${contact.firstName} ${contact.lastName}\" ?`)) {\n      this.contactService.deleteContact(contact.id)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Contact supprimé avec succès', 'Fermer', { duration: 3000 });\n            this.loadContacts(); // Reload contacts\n          },\n          error: (error) => {\n            console.error('Error deleting contact:', error);\n            this.snackBar.open('Erreur lors de la suppression du contact', 'Fermer', { duration: 3000 });\n          }\n        });\n    }\n  }\n\n  getContactStatusColor(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      default:\n        return '';\n    }\n  }\n\n  getContactStatusLabel(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      default:\n        return status;\n    }\n  }\n\n  sendContactEmail(contact: ContactDto): void {\n    if (contact.email) {\n      window.location.href = `mailto:${contact.email}`;\n    }\n  }\n\n  callContactPhone(contact: ContactDto): void {\n    if (contact.phone) {\n      window.location.href = `tel:${contact.phone}`;\n    }\n  }\n}\n", "<div class=\"client-detail-container\">\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Chargement des détails du client...</p>\n  </div>\n\n  <div *ngIf=\"!isLoading && client\" class=\"client-content\">\n    <!-- Header Card -->\n    <mat-card class=\"header-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <div class=\"title-row\">\n            <div class=\"client-name\">\n              <mat-icon>business</mat-icon>\n              {{ client.name }}\n            </div>\n            <mat-chip [color]=\"getStatusColor(client.status)\" selected>\n              {{ getStatusLabel(client.status) }}\n            </mat-chip>\n          </div>\n        </mat-card-title>\n        <mat-card-subtitle>\n          Contact: {{ client.contactPerson }} • {{ client.industry }}\n        </mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-actions>\n        <button mat-button (click)=\"onBack()\">\n          <mat-icon>arrow_back</mat-icon>\n          Retour\n        </button>\n        <button mat-raised-button color=\"primary\" (click)=\"onEdit()\">\n          <mat-icon>edit</mat-icon>\n          Modifier\n        </button>\n        <button mat-button color=\"warn\" (click)=\"onDelete()\">\n          <mat-icon>delete</mat-icon>\n          Supprimer\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <!-- Tabbed Content -->\n    <mat-tab-group [(selectedIndex)]=\"selectedTabIndex\" class=\"client-tabs\">\n      <!-- Client Details Tab -->\n      <mat-tab label=\"Détails du client\">\n        <div class=\"tab-content\">\n\n          <div class=\"details-grid\">\n            <!-- Contact Information -->\n            <mat-card class=\"info-card\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>contact_mail</mat-icon>\n                  Informations de contact\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-item\">\n                  <div class=\"info-label\">\n                    <mat-icon>email</mat-icon>\n                    Email\n                  </div>\n                  <div class=\"info-value\">\n                    <a [href]=\"'mailto:' + client.email\" class=\"contact-link\" (click)=\"sendEmail()\">\n                      {{ client.email }}\n                    </a>\n                  </div>\n                </div>\n\n                <mat-divider></mat-divider>\n\n                <div class=\"info-item\">\n                  <div class=\"info-label\">\n                    <mat-icon>phone</mat-icon>\n                    Téléphone\n                  </div>\n                  <div class=\"info-value\">\n                    <a [href]=\"'tel:' + client.phone\" class=\"contact-link\" (click)=\"callPhone()\">\n                      {{ client.phone }}\n                    </a>\n                  </div>\n                </div>\n\n                <mat-divider></mat-divider>\n\n                <div class=\"info-item\">\n                  <div class=\"info-label\">\n                    <mat-icon>location_on</mat-icon>\n                    Adresse\n                  </div>\n                  <div class=\"info-value\">\n                    {{ client.address }}\n                  </div>\n                </div>\n\n                <mat-divider *ngIf=\"client.website\"></mat-divider>\n\n                <div class=\"info-item\" *ngIf=\"client.website\">\n                  <div class=\"info-label\">\n                    <mat-icon>link</mat-icon>\n                    Site web\n                  </div>\n                  <div class=\"info-value\">\n                    <a [href]=\"client.website\" target=\"_blank\" class=\"contact-link\" (click)=\"openWebsite()\">\n                      {{ client.website }}\n                      <mat-icon>open_in_new</mat-icon>\n                    </a>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Business Information -->\n            <mat-card class=\"info-card\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>business_center</mat-icon>\n                  Informations entreprise\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-item\">\n                  <div class=\"info-label\">\n                    <mat-icon>person</mat-icon>\n                    Personne de contact\n                  </div>\n                  <div class=\"info-value\">\n                    {{ client.contactPerson }}\n                  </div>\n                </div>\n\n                <mat-divider></mat-divider>\n\n                <div class=\"info-item\">\n                  <div class=\"info-label\">\n                    <mat-icon>category</mat-icon>\n                    Secteur d'activité\n                  </div>\n                  <div class=\"info-value\">\n                    {{ client.industry }}\n                  </div>\n                </div>\n\n                <mat-divider></mat-divider>\n\n                <div class=\"info-item\">\n                  <div class=\"info-label\">\n                    <mat-icon>info</mat-icon>\n                    Statut\n                  </div>\n                  <div class=\"info-value\">\n                    <mat-chip [color]=\"getStatusColor(client.status)\" selected>\n                      {{ getStatusLabel(client.status) }}\n                    </mat-chip>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Timeline Information -->\n            <mat-card class=\"info-card timeline-card\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>schedule</mat-icon>\n                  Historique\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"timeline-item\">\n                  <div class=\"timeline-icon\">\n                    <mat-icon>add_circle</mat-icon>\n                  </div>\n                  <div class=\"timeline-content\">\n                    <div class=\"timeline-title\">Client créé</div>\n                    <div class=\"timeline-date\">{{ formatDate(client.createdAt) }}</div>\n                  </div>\n                </div>\n\n                <div class=\"timeline-item\" *ngIf=\"client.updatedAt !== client.createdAt\">\n                  <div class=\"timeline-icon\">\n                    <mat-icon>edit</mat-icon>\n                  </div>\n                  <div class=\"timeline-content\">\n                    <div class=\"timeline-title\">Dernière modification</div>\n                    <div class=\"timeline-date\">{{ formatDate(client.updatedAt) }}</div>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Notes -->\n            <mat-card class=\"info-card notes-card\" *ngIf=\"client.notes\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>note</mat-icon>\n                  Notes\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"notes-content\">\n                  {{ client.notes }}\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n      </mat-tab>\n\n      <!-- Contacts Tab -->\n      <mat-tab label=\"Contacts\">\n        <div class=\"tab-content\">\n          <div class=\"contacts-header\">\n            <h3>Contacts associés</h3>\n            <button mat-raised-button color=\"primary\" (click)=\"onAddContact()\">\n              <mat-icon>add</mat-icon>\n              Ajouter un contact\n            </button>\n          </div>\n\n          <div *ngIf=\"isLoadingContacts\" class=\"loading-container\">\n            <mat-spinner></mat-spinner>\n            <p>Chargement des contacts...</p>\n          </div>\n\n          <div *ngIf=\"!isLoadingContacts && contacts.length === 0\" class=\"empty-state\">\n            <mat-icon>person_off</mat-icon>\n            <h4>Aucun contact</h4>\n            <p>Ce client n'a pas encore de contacts associés.</p>\n            <button mat-raised-button color=\"primary\" (click)=\"onAddContact()\">\n              <mat-icon>add</mat-icon>\n              Ajouter le premier contact\n            </button>\n          </div>\n\n          <div *ngIf=\"!isLoadingContacts && contacts.length > 0\" class=\"contacts-table-container\">\n            <table mat-table [dataSource]=\"contacts\" class=\"contacts-table\">\n              <!-- Name Column -->\n              <ng-container matColumnDef=\"name\">\n                <th mat-header-cell *matHeaderCellDef>Nom</th>\n                <td mat-cell *matCellDef=\"let contact\">\n                  <div class=\"contact-name\">\n                    <mat-icon>person</mat-icon>\n                    {{ contact.firstName }} {{ contact.lastName }}\n                  </div>\n                </td>\n              </ng-container>\n\n              <!-- Email Column -->\n              <ng-container matColumnDef=\"email\">\n                <th mat-header-cell *matHeaderCellDef>Email</th>\n                <td mat-cell *matCellDef=\"let contact\">\n                  <a [href]=\"'mailto:' + contact.email\" class=\"contact-link\" (click)=\"sendContactEmail(contact)\">\n                    {{ contact.email }}\n                  </a>\n                </td>\n              </ng-container>\n\n              <!-- Phone Column -->\n              <ng-container matColumnDef=\"phone\">\n                <th mat-header-cell *matHeaderCellDef>Téléphone</th>\n                <td mat-cell *matCellDef=\"let contact\">\n                  <a [href]=\"'tel:' + contact.phone\" class=\"contact-link\" (click)=\"callContactPhone(contact)\">\n                    {{ contact.phone }}\n                  </a>\n                </td>\n              </ng-container>\n\n              <!-- Position Column -->\n              <ng-container matColumnDef=\"position\">\n                <th mat-header-cell *matHeaderCellDef>Poste</th>\n                <td mat-cell *matCellDef=\"let contact\">{{ contact.position }}</td>\n              </ng-container>\n\n              <!-- Status Column -->\n              <ng-container matColumnDef=\"status\">\n                <th mat-header-cell *matHeaderCellDef>Statut</th>\n                <td mat-cell *matCellDef=\"let contact\">\n                  <mat-chip [color]=\"getContactStatusColor(contact.status)\" selected>\n                    {{ getContactStatusLabel(contact.status) }}\n                  </mat-chip>\n                </td>\n              </ng-container>\n\n              <!-- Actions Column -->\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let contact\">\n                  <button mat-icon-button [matMenuTriggerFor]=\"contactMenu\">\n                    <mat-icon>more_vert</mat-icon>\n                  </button>\n                  <mat-menu #contactMenu=\"matMenu\">\n                    <button mat-menu-item (click)=\"onViewContact(contact)\">\n                      <mat-icon>visibility</mat-icon>\n                      Voir détails\n                    </button>\n                    <button mat-menu-item (click)=\"onEditContact(contact)\">\n                      <mat-icon>edit</mat-icon>\n                      Modifier\n                    </button>\n                    <button mat-menu-item (click)=\"sendContactEmail(contact)\" *ngIf=\"contact.email\">\n                      <mat-icon>email</mat-icon>\n                      Envoyer email\n                    </button>\n                    <button mat-menu-item (click)=\"callContactPhone(contact)\" *ngIf=\"contact.phone\">\n                      <mat-icon>phone</mat-icon>\n                      Appeler\n                    </button>\n                    <mat-divider></mat-divider>\n                    <button mat-menu-item (click)=\"onDeleteContact(contact)\" class=\"delete-action\">\n                      <mat-icon>delete</mat-icon>\n                      Supprimer\n                    </button>\n                  </mat-menu>\n                </td>\n              </ng-container>\n\n              <tr mat-header-row *matHeaderRowDef=\"contactDisplayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: contactDisplayedColumns;\"></tr>\n            </table>\n          </div>\n        </div>\n      </mat-tab>\n    </mat-tab-group>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgC,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;ICbjDC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,+CAAmC;IACxCH,EADwC,CAAAI,YAAA,EAAI,EACtC;;;;;IA4FQJ,EAAA,CAAAE,SAAA,kBAAkD;;;;;;IAI9CF,EAFJ,CAAAC,cAAA,cAA8C,cACpB,eACZ;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAAwB,YACkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACrFZ,EAAA,CAAAG,MAAA,GACA;IAAAH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAG3BH,EAH2B,CAAAI,YAAA,EAAW,EAC9B,EACA,EACF;;;;IALCJ,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,MAAA,CAAAC,OAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAAuB;IACxBjB,EAAA,CAAAa,SAAA,EACA;IADAb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAAC,OAAA,MACA;;;;;IA2EFhB,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,eACf;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAChBH,EADgB,CAAAI,YAAA,EAAW,EACrB;IAEJJ,EADF,CAAAC,cAAA,cAA8B,cACA;IAAAD,EAAA,CAAAG,MAAA,iCAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACvDJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAEjEH,EAFiE,CAAAI,YAAA,EAAM,EAC/D,EACF;;;;IAFyBJ,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAmB,iBAAA,CAAAV,MAAA,CAAAW,UAAA,CAAAX,MAAA,CAAAM,MAAA,CAAAM,SAAA,EAAkC;;;;;IAU/DrB,EAHN,CAAAC,cAAA,mBAA4D,sBACzC,qBACC,eACJ;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,cACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAEhBJ,EADF,CAAAC,cAAA,uBAAkB,cACW;IACzBD,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAM,EACW,EACV;;;;IAHLJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAAO,KAAA,MACF;;;;;IAkBNtB,EAAA,CAAAC,cAAA,aAAyD;IACvDD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;;;;;;IAGJJ,EADF,CAAAC,cAAA,cAA6E,eACjE;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0DAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrDJ,EAAA,CAAAC,cAAA,iBAAmE;IAAzBD,EAAA,CAAAK,UAAA,mBAAAkB,qEAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgB,YAAA,EAAc;IAAA,EAAC;IAChEzB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACxBJ,EAAA,CAAAG,MAAA,oCACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;;IAMAJ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAG1CJ,EAFJ,CAAAC,cAAA,aAAuC,cACX,eACd;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACH;;;;IAFDJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,MAAAC,UAAA,CAAAC,SAAA,OAAAD,UAAA,CAAAE,QAAA,MACF;;;;;IAMF7B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAE9CJ,EADF,CAAAC,cAAA,aAAuC,YAC0D;IAApCD,EAAA,CAAAK,UAAA,mBAAAyB,qEAAA;MAAA,MAAAC,UAAA,GAAA/B,EAAA,CAAAO,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,gBAAA,CAAAH,UAAA,CAAyB;IAAA,EAAC;IAC5F/B,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAHAJ,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAAc,UAAA,qBAAAiB,UAAA,CAAAI,KAAA,EAAAnC,EAAA,CAAAiB,aAAA,CAAkC;IACnCjB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAa,UAAA,CAAAI,KAAA,MACF;;;;;IAMFnC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,0BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAElDJ,EADF,CAAAC,cAAA,aAAuC,YACuD;IAApCD,EAAA,CAAAK,UAAA,mBAAA+B,sEAAA;MAAA,MAAAC,UAAA,GAAArC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAAL,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8B,gBAAA,CAAAF,UAAA,CAAyB;IAAA,EAAC;IACzFrC,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAHAJ,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAc,UAAA,kBAAAuB,UAAA,CAAAG,KAAA,EAAAxC,EAAA,CAAAiB,aAAA,CAA+B;IAChCjB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAmB,UAAA,CAAAG,KAAA,MACF;;;;;IAMFxC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAChDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA3BJ,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAmB,iBAAA,CAAAsB,WAAA,CAAAC,QAAA,CAAsB;;;;;IAK7D1C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAE/CJ,EADF,CAAAC,cAAA,aAAuC,kBAC8B;IACjED,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAW,EACR;;;;;IAHOJ,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAkC,qBAAA,CAAAC,WAAA,CAAAC,MAAA,EAA+C;IACvD7C,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAqC,qBAAA,CAAAF,WAAA,CAAAC,MAAA,OACF;;;;;IAMF7C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAc9CJ,EAAA,CAAAC,cAAA,iBAAgF;IAA1DD,EAAA,CAAAK,UAAA,mBAAA0C,qFAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,WAAA,GAAAjD,EAAA,CAAAU,aAAA,GAAAuB,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,gBAAA,CAAAe,WAAA,CAAyB;IAAA,EAAC;IACvDjD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IACTJ,EAAA,CAAAC,cAAA,iBAAgF;IAA1DD,EAAA,CAAAK,UAAA,mBAAA6C,qFAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,IAAA;MAAA,MAAAF,WAAA,GAAAjD,EAAA,CAAAU,aAAA,GAAAuB,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8B,gBAAA,CAAAU,WAAA,CAAyB;IAAA,EAAC;IACvDjD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAlBTJ,EAFJ,CAAAC,cAAA,aAAuC,iBACqB,eAC9C;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IACrBH,EADqB,CAAAI,YAAA,EAAW,EACvB;IAEPJ,EADF,CAAAC,cAAA,wBAAiC,iBACwB;IAAjCD,EAAA,CAAAK,UAAA,mBAAA+C,2EAAA;MAAA,MAAAH,WAAA,GAAAjD,EAAA,CAAAO,aAAA,CAAA8C,IAAA,EAAApB,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6C,aAAA,CAAAL,WAAA,CAAsB;IAAA,EAAC;IACpDjD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAuD;IAAjCD,EAAA,CAAAK,UAAA,mBAAAkD,4EAAA;MAAA,MAAAN,WAAA,GAAAjD,EAAA,CAAAO,aAAA,CAAA8C,IAAA,EAAApB,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+C,aAAA,CAAAP,WAAA,CAAsB;IAAA,EAAC;IACpDjD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAKTJ,EAJA,CAAAyD,UAAA,KAAAC,4DAAA,qBAAgF,KAAAC,4DAAA,qBAIA;IAIhF3D,EAAA,CAAAE,SAAA,mBAA2B;IAC3BF,EAAA,CAAAC,cAAA,kBAA+E;IAAzDD,EAAA,CAAAK,UAAA,mBAAAuD,4EAAA;MAAA,MAAAX,WAAA,GAAAjD,EAAA,CAAAO,aAAA,CAAA8C,IAAA,EAAApB,SAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoD,eAAA,CAAAZ,WAAA,CAAwB;IAAA,EAAC;IACtDjD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,mBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACA,EACR;;;;;IA1BqBJ,EAAA,CAAAa,SAAA,EAAiC;IAAjCb,EAAA,CAAAc,UAAA,sBAAAgD,eAAA,CAAiC;IAYI9D,EAAA,CAAAa,SAAA,IAAmB;IAAnBb,EAAA,CAAAc,UAAA,SAAAmC,WAAA,CAAAd,KAAA,CAAmB;IAInBnC,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAc,UAAA,SAAAmC,WAAA,CAAAT,KAAA,CAAmB;;;;;IAapFxC,EAAA,CAAAE,SAAA,aAAmE;;;;;IACnEF,EAAA,CAAAE,SAAA,aAAyE;;;;;IAlF3EF,EADF,CAAAC,cAAA,cAAwF,gBACtB;IAE9DD,EAAA,CAAA+D,uBAAA,OAAkC;IAEhC/D,EADA,CAAAyD,UAAA,IAAAO,iDAAA,iBAAsC,IAAAC,iDAAA,iBACC;;IASzCjE,EAAA,CAAA+D,uBAAA,OAAmC;IAEjC/D,EADA,CAAAyD,UAAA,IAAAS,iDAAA,iBAAsC,IAAAC,iDAAA,iBACC;;IAQzCnE,EAAA,CAAA+D,uBAAA,OAAmC;IAEjC/D,EADA,CAAAyD,UAAA,IAAAW,iDAAA,iBAAsC,KAAAC,kDAAA,iBACC;;IAQzCrE,EAAA,CAAA+D,uBAAA,QAAsC;IAEpC/D,EADA,CAAAyD,UAAA,KAAAa,kDAAA,iBAAsC,KAAAC,kDAAA,iBACC;;IAIzCvE,EAAA,CAAA+D,uBAAA,QAAoC;IAElC/D,EADA,CAAAyD,UAAA,KAAAe,kDAAA,iBAAsC,KAAAC,kDAAA,iBACC;;IAQzCzE,EAAA,CAAA+D,uBAAA,QAAqC;IAEnC/D,EADA,CAAAyD,UAAA,KAAAiB,kDAAA,iBAAsC,KAAAC,kDAAA,kBACC;;IA+BzC3E,EADA,CAAAyD,UAAA,KAAAmB,kDAAA,iBAA8D,KAAAC,kDAAA,iBACM;IAExE7E,EADE,CAAAI,YAAA,EAAQ,EACJ;;;;IApFaJ,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAc,UAAA,eAAAL,MAAA,CAAAqE,QAAA,CAAuB;IAiFlB9E,EAAA,CAAAa,SAAA,IAAwC;IAAxCb,EAAA,CAAAc,UAAA,oBAAAL,MAAA,CAAAsE,uBAAA,CAAwC;IAC3B/E,EAAA,CAAAa,SAAA,EAAiC;IAAjCb,EAAA,CAAAc,UAAA,qBAAAL,MAAA,CAAAsE,uBAAA,CAAiC;;;;;;IAjTlE/E,EAPZ,CAAAC,cAAA,aAAyD,kBAEzB,sBACX,qBACC,aACS,aACI,eACb;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,kBAA2D;IACzDD,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAW,EACP,EACS;IACjBJ,EAAA,CAAAC,cAAA,yBAAmB;IACjBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAoB,EACJ;IAGhBJ,EADF,CAAAC,cAAA,wBAAkB,kBACsB;IAAnBD,EAAA,CAAAK,UAAA,mBAAA2E,8DAAA;MAAAhF,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyE,MAAA,EAAQ;IAAA,EAAC;IACnClF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA6D;IAAnBD,EAAA,CAAAK,UAAA,mBAAA8E,8DAAA;MAAAnF,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2E,MAAA,EAAQ;IAAA,EAAC;IAC1DpF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAqD;IAArBD,EAAA,CAAAK,UAAA,mBAAAgF,8DAAA;MAAArF,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6E,QAAA,EAAU;IAAA,EAAC;IAClDtF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,mBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACQ,EACV;IAGXJ,EAAA,CAAAC,cAAA,yBAAwE;IAAzDD,EAAA,CAAAuF,gBAAA,iCAAAC,mFAAAC,MAAA;MAAAzF,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAA0F,kBAAA,CAAAjF,MAAA,CAAAkF,gBAAA,EAAAF,MAAA,MAAAhF,MAAA,CAAAkF,gBAAA,GAAAF,MAAA;MAAA,OAAAzF,EAAA,CAAAW,WAAA,CAAA8E,MAAA;IAAA,EAAoC;IAUrCzF,EARZ,CAAAC,cAAA,mBAAmC,eACR,eAEG,oBAEI,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjCJ,EAAA,CAAAG,MAAA,iCACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACO,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAAwB,aAC0D;IAAtBD,EAAA,CAAAK,UAAA,mBAAAuF,yDAAA;MAAA5F,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoF,SAAA,EAAW;IAAA,EAAC;IAC7E7F,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAI,EACA,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAAwB,aACuD;IAAtBD,EAAA,CAAAK,UAAA,mBAAAyF,yDAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsF,SAAA,EAAW;IAAA,EAAC;IAC1E/F,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAI,EACA,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAINJ,EAFA,CAAAyD,UAAA,KAAAuC,mDAAA,0BAAoC,KAAAC,2CAAA,mBAEU;IAalDjG,EADE,CAAAI,YAAA,EAAmB,EACV;IAMLJ,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAG,MAAA,iCACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACO,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAAwB,mBACqC;IACzDD,EAAA,CAAAG,MAAA,IACF;IAIRH,EAJQ,CAAAI,YAAA,EAAW,EACP,EACF,EACW,EACV;IAMLJ,EAHN,CAAAC,cAAA,oBAA0C,uBACvB,sBACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,qBACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAIZJ,EAHN,CAAAC,cAAA,yBAAkB,gBACW,gBACE,iBACf;IAAAD,EAAA,CAAAG,MAAA,mBAAU;IACtBH,EADsB,CAAAI,YAAA,EAAW,EAC3B;IAEJJ,EADF,CAAAC,cAAA,gBAA8B,gBACA;IAAAD,EAAA,CAAAG,MAAA,8BAAW;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC7CJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,KAAkC;IAEjEH,EAFiE,CAAAI,YAAA,EAAM,EAC/D,EACF;IAENJ,EAAA,CAAAyD,UAAA,MAAAyC,4CAAA,kBAAyE;IAU7ElG,EADE,CAAAI,YAAA,EAAmB,EACV;IAGXJ,EAAA,CAAAyD,UAAA,MAAA0C,iDAAA,uBAA4D;IAelEnG,EAFI,CAAAI,YAAA,EAAM,EACF,EACE;IAMJJ,EAHN,CAAAC,cAAA,oBAA0B,gBACC,gBACM,WACvB;IAAAD,EAAA,CAAAG,MAAA,+BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,mBAAmE;IAAzBD,EAAA,CAAAK,UAAA,mBAAA+F,+DAAA;MAAApG,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgB,YAAA,EAAc;IAAA,EAAC;IAChEzB,EAAA,CAAAC,cAAA,iBAAU;IAAAD,EAAA,CAAAG,MAAA,YAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACxBJ,EAAA,CAAAG,MAAA,6BACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAiBNJ,EAfA,CAAAyD,UAAA,MAAA4C,4CAAA,iBAAyD,MAAAC,4CAAA,mBAKoB,MAAAC,4CAAA,mBAUW;IAyFhGvG,EAHM,CAAAI,YAAA,EAAM,EACE,EACI,EACZ;;;;IAtTMJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAAyF,IAAA,MACF;IACUxG,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAgG,cAAA,CAAAhG,MAAA,CAAAM,MAAA,CAAA8B,MAAA,EAAuC;IAC/C7C,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAiG,cAAA,CAAAjG,MAAA,CAAAM,MAAA,CAAA8B,MAAA,OACF;IAIF7C,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,eAAAjB,MAAA,CAAAM,MAAA,CAAA4F,aAAA,cAAAlG,MAAA,CAAAM,MAAA,CAAA6F,QAAA,MACF;IAoBW5G,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAA6G,gBAAA,kBAAApG,MAAA,CAAAkF,gBAAA,CAAoC;IAqBhC3F,EAAA,CAAAa,SAAA,IAAiC;IAAjCb,EAAA,CAAAc,UAAA,qBAAAL,MAAA,CAAAM,MAAA,CAAAoB,KAAA,EAAAnC,EAAA,CAAAiB,aAAA,CAAiC;IAClCjB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAAoB,KAAA,MACF;IAYGnC,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,kBAAAL,MAAA,CAAAM,MAAA,CAAAyB,KAAA,EAAAxC,EAAA,CAAAiB,aAAA,CAA8B;IAC/BjB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAAyB,KAAA,MACF;IAYAxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAA+F,OAAA,MACF;IAGY9G,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,MAAA,CAAAC,OAAA,CAAoB;IAEVhB,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,MAAA,CAAAC,OAAA,CAAoB;IA8BxChB,EAAA,CAAAa,SAAA,IACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAA4F,aAAA,MACF;IAWE3G,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAM,MAAA,CAAA6F,QAAA,MACF;IAWY5G,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAgG,cAAA,CAAAhG,MAAA,CAAAM,MAAA,CAAA8B,MAAA,EAAuC;IAC/C7C,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAiG,cAAA,CAAAjG,MAAA,CAAAM,MAAA,CAAA8B,MAAA,OACF;IAqB2B7C,EAAA,CAAAa,SAAA,IAAkC;IAAlCb,EAAA,CAAAmB,iBAAA,CAAAV,MAAA,CAAAW,UAAA,CAAAX,MAAA,CAAAM,MAAA,CAAAgG,SAAA,EAAkC;IAIrC/G,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,MAAA,CAAAM,SAAA,KAAAZ,MAAA,CAAAM,MAAA,CAAAgG,SAAA,CAA2C;IAanC/G,EAAA,CAAAa,SAAA,EAAkB;IAAlBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,MAAA,CAAAO,KAAA,CAAkB;IA4BtDtB,EAAA,CAAAa,SAAA,IAAuB;IAAvBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAuG,iBAAA,CAAuB;IAKvBhH,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAuG,iBAAA,IAAAvG,MAAA,CAAAqE,QAAA,CAAAmC,MAAA,OAAiD;IAUjDjH,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAuG,iBAAA,IAAAvG,MAAA,CAAAqE,QAAA,CAAAmC,MAAA,KAA+C;;;ADpM/D,OAAM,MAAOC,qBAAqB;EAahCC,YACmBC,aAA4B,EAC5BC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB,EACrBC,MAAiB;IALjB,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAjBzB,KAAA3C,QAAQ,GAAiB,EAAE;IAC3B,KAAA4C,SAAS,GAAG,KAAK;IACjB,KAAAV,iBAAiB,GAAG,KAAK;IAEzB,KAAArB,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAZ,uBAAuB,GAAa,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC;IAE9E,KAAA4C,QAAQ,GAAG,IAAI9H,OAAO,EAAQ;EAS5C;EAEH+H,QAAQA,CAAA;IACN,IAAI,CAACL,KAAK,CAACM,MAAM,CAACC,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,QAAQ,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAG;MAClE,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACG,QAAQ,GAAG,CAACH,MAAM,CAAC,IAAI,CAAC;QAC7B,IAAI,CAACI,qBAAqB,EAAE;;IAEhC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACpB,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE;EAC1B;EAEQH,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;IAEpB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACV,iBAAiB,GAAG,IAAI;IAE7B;IACAjH,QAAQ,CAAC;MACPgB,MAAM,EAAE,IAAI,CAACqG,aAAa,CAACiB,SAAS,CAAC,IAAI,CAACL,QAAQ,CAAC;MACnDlD,QAAQ,EAAE,IAAI,CAACuC,cAAc,CAACiB,mBAAmB,CAAC,IAAI,CAACN,QAAQ;KAChE,CAAC,CAACF,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTI,IAAI,EAAEA,CAAC;QAAEpH,MAAM;QAAE+D;MAAQ,CAAE,KAAI;QAC7B,IAAI/D,MAAM,EAAE;UACV,IAAI,CAACA,MAAM,GAAGA,MAAM;UACpB,IAAI,CAAC+D,QAAQ,GAAGA,QAAQ;SACzB,MAAM;UACL,IAAI,CAAC0C,QAAQ,CAACe,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACrE,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;QAEpC,IAAI,CAACf,SAAS,GAAG,KAAK;QACtB,IAAI,CAACV,iBAAiB,GAAG,KAAK;MAChC,CAAC;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAClB,QAAQ,CAACe,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzF,IAAI,CAACd,SAAS,GAAG,KAAK;QACtB,IAAI,CAACV,iBAAiB,GAAG,KAAK;MAChC;KACD,CAAC;EACN;EAEQ4B,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACZ,QAAQ,EAAE;IAEpB,IAAI,CAAChB,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACK,cAAc,CAACiB,mBAAmB,CAAC,IAAI,CAACN,QAAQ,CAAC,CACnDF,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTI,IAAI,EAAGrD,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACkC,iBAAiB,GAAG,KAAK;MAChC,CAAC;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClB,QAAQ,CAACe,IAAI,CAAC,wCAAwC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC1F,IAAI,CAACxB,iBAAiB,GAAG,KAAK;MAChC;KACD,CAAC;EACN;EAEA5B,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACrE,MAAM,EAAE;MACf,IAAI,CAACuG,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC1H,MAAM,CAAC8H,EAAE,EAAE,MAAM,CAAC,CAAC;;EAE9D;EAEAvD,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACvE,MAAM,EAAE;IAElB,IAAI+H,OAAO,CAAC,iDAAiD,IAAI,CAAC/H,MAAM,CAACyF,IAAI,KAAK,CAAC,EAAE;MACnF,IAAI,CAACY,aAAa,CAAC2B,YAAY,CAAC,IAAI,CAAChI,MAAM,CAAC8H,EAAE,CAAC,CAC5Cf,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;QACTI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACX,QAAQ,CAACe,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAC/E,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAAClB,QAAQ,CAACe,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC7F;OACD,CAAC;;EAER;EAEAtD,MAAMA,CAAA;IACJ,IAAI,CAACoC,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAhC,cAAcA,CAAC5D,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,UAAU;QACb,OAAO,QAAQ;MACjB;QACE,OAAO,EAAE;;EAEf;EAEA6D,cAAcA,CAAC7D,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB;QACE,OAAOA,MAAM;;EAEnB;EAEAzB,UAAUA,CAAC4H,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAzI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACG,MAAM,EAAEC,OAAO,EAAE;MACxBsI,MAAM,CAACf,IAAI,CAAC,IAAI,CAACxH,MAAM,CAACC,OAAO,EAAE,QAAQ,CAAC;;EAE9C;EAEA6E,SAASA,CAAA;IACP,IAAI,IAAI,CAAC9E,MAAM,EAAEoB,KAAK,EAAE;MACtBmH,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU,IAAI,CAACzI,MAAM,CAACoB,KAAK,EAAE;;EAExD;EAEA4D,SAASA,CAAA;IACP,IAAI,IAAI,CAAChF,MAAM,EAAEyB,KAAK,EAAE;MACtB8G,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO,IAAI,CAACzI,MAAM,CAACyB,KAAK,EAAE;;EAErD;EAEA;EACAf,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACuG,QAAQ,EAAE;IAEpB,IAAI,CAACV,MAAM,CAACmB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;MACzCgB,WAAW,EAAE;QAAEzB,QAAQ,EAAE,IAAI,CAACA;MAAQ;KACvC,CAAC;EACJ;EAEAxE,aAAaA,CAACkG,OAAmB;IAC/B,IAAI,CAACpC,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,EAAEiB,OAAO,CAACb,EAAE,EAAE,MAAM,CAAC,CAAC;EACzD;EAEAvF,aAAaA,CAACoG,OAAmB;IAC/B,IAAI,CAACpC,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,EAAEiB,OAAO,CAACb,EAAE,CAAC,CAAC;EACjD;EAEAhF,eAAeA,CAAC6F,OAAmB;IACjC,IAAIZ,OAAO,CAAC,kDAAkDY,OAAO,CAAC9H,SAAS,IAAI8H,OAAO,CAAC7H,QAAQ,KAAK,CAAC,EAAE;MACzG,IAAI,CAACwF,cAAc,CAACsC,aAAa,CAACD,OAAO,CAACb,EAAE,CAAC,CAC1Cf,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;QACTI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACX,QAAQ,CAACe,IAAI,CAAC,8BAA8B,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAChF,IAAI,CAACI,YAAY,EAAE,CAAC,CAAC;QACvB,CAAC;QACDF,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAAClB,QAAQ,CAACe,IAAI,CAAC,0CAA0C,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9F;OACD,CAAC;;EAER;EAEA7F,qBAAqBA,CAACE,MAAc;IAClC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,MAAM;MACf;QACE,OAAO,EAAE;;EAEf;EAEAC,qBAAqBA,CAACD,MAAc;IAClC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAOA,MAAM;;EAEnB;EAEAX,gBAAgBA,CAACwH,OAAmB;IAClC,IAAIA,OAAO,CAACvH,KAAK,EAAE;MACjBmH,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAUE,OAAO,CAACvH,KAAK,EAAE;;EAEpD;EAEAI,gBAAgBA,CAACmH,OAAmB;IAClC,IAAIA,OAAO,CAAClH,KAAK,EAAE;MACjB8G,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAOE,OAAO,CAAClH,KAAK,EAAE;;EAEjD;;;uBA1OW0E,qBAAqB,EAAAlH,EAAA,CAAA4J,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA9J,EAAA,CAAA4J,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhK,EAAA,CAAA4J,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAlK,EAAA,CAAA4J,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAnK,EAAA,CAAA4J,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAArK,EAAA,CAAA4J,iBAAA,CAAAU,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAArBrD,qBAAqB;MAAAsD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1K,EAAA,CAAA2K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvClCjL,EAAA,CAAAC,cAAA,aAAqC;UAMnCD,EALA,CAAAyD,UAAA,IAAA0H,oCAAA,iBAAiD,IAAAC,oCAAA,oBAKQ;UA+T3DpL,EAAA,CAAAI,YAAA,EAAM;;;UApUEJ,EAAA,CAAAa,SAAA,EAAe;UAAfb,EAAA,CAAAc,UAAA,SAAAoK,GAAA,CAAAxD,SAAA,CAAe;UAKf1H,EAAA,CAAAa,SAAA,EAA0B;UAA1Bb,EAAA,CAAAc,UAAA,UAAAoK,GAAA,CAAAxD,SAAA,IAAAwD,GAAA,CAAAnK,MAAA,CAA0B;;;qBDiB9B9B,YAAY,EAAAoM,EAAA,CAAAC,IAAA,EACZpM,YAAY,EACZC,aAAa,EAAAoM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbzM,eAAe,EAAA0M,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3M,aAAa,EAAA4M,EAAA,CAAAC,OAAA,EACb5M,cAAc,EAAA6M,GAAA,CAAAC,OAAA,EACd7M,gBAAgB,EAAA8M,GAAA,CAAAC,UAAA,EAChB9M,wBAAwB,EAAA+M,GAAA,CAAAC,kBAAA,EACxB/M,cAAc,EAAAgN,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACdzN,aAAa,EAAA0N,GAAA,CAAAC,MAAA,EAAAD,GAAA,CAAAE,WAAA,EACb3N,aAAa,EAAA4N,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACb9N,eAAe;MAAA+N,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}