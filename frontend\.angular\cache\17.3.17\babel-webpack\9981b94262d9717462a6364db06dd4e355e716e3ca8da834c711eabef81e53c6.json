{"ast": null, "code": "import { of, BehaviorSubject } from 'rxjs';\nimport { delay, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class ClientService {\n  constructor() {\n    this.clients$ = new BehaviorSubject([]);\n    this.nextId = 1;\n    // Initialize with mock data\n    this.initializeMockData();\n  }\n  initializeMockData() {\n    const mockClients = [{\n      id: 1,\n      name: 'TechCorp Solutions',\n      email: '<EMAIL>',\n      phone: '+33 1 23 45 67 89',\n      address: '123 Avenue des Champs-Élysées, 75008 Paris',\n      contactPerson: '<PERSON>',\n      industry: 'Technology',\n      website: 'https://techcorp.fr',\n      notes: 'Client principal, projets récurrents',\n      status: 'ACTIVE',\n      createdAt: new Date('2024-01-15'),\n      updatedAt: new Date('2024-06-20')\n    }, {\n      id: 2,\n      name: 'StartupInnovate',\n      email: '<EMAIL>',\n      phone: '+33 1 98 76 54 32',\n      address: '45 Rue de Rivoli, 75001 Paris',\n      contactPerson: '<PERSON>',\n      industry: 'Fintech',\n      website: 'https://startupinnovate.com',\n      notes: 'Startup prometteuse, budget limité',\n      status: 'ACTIVE',\n      createdAt: new Date('2024-02-10'),\n      updatedAt: new Date('2024-06-15')\n    }, {\n      id: 3,\n      name: 'E-Commerce Plus',\n      email: '<EMAIL>',\n      phone: '+33 1 11 22 33 44',\n      address: '78 Boulevard Saint-Germain, 75006 Paris',\n      contactPerson: 'Sophie Laurent',\n      industry: 'E-commerce',\n      status: 'PROSPECT',\n      createdAt: new Date('2024-06-01'),\n      updatedAt: new Date('2024-06-01')\n    }, {\n      id: 4,\n      name: 'ConsultingPro',\n      email: '<EMAIL>',\n      phone: '+33 1 55 66 77 88',\n      address: '12 Place Vendôme, 75001 Paris',\n      contactPerson: 'Pierre Moreau',\n      industry: 'Consulting',\n      notes: 'Client exigeant mais bien payeur',\n      status: 'INACTIVE',\n      createdAt: new Date('2023-12-01'),\n      updatedAt: new Date('2024-03-15')\n    }];\n    this.clients$.next(mockClients);\n    this.nextId = Math.max(...mockClients.map(c => c.id)) + 1;\n  }\n  getClients() {\n    return this.clients$.asObservable();\n  }\n  getClient(id) {\n    return this.clients$.pipe(map(clients => clients.find(client => client.id === id)), delay(300) // Simulate API delay\n    );\n  }\n  createClient(clientData) {\n    const newClient = {\n      ...clientData,\n      id: this.nextId++,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n    const currentClients = this.clients$.value;\n    this.clients$.next([...currentClients, newClient]);\n    return of(newClient).pipe(delay(500));\n  }\n  updateClient(clientData) {\n    const currentClients = this.clients$.value;\n    const index = currentClients.findIndex(client => client.id === clientData.id);\n    if (index === -1) {\n      throw new Error('Client not found');\n    }\n    const updatedClient = {\n      ...currentClients[index],\n      ...clientData,\n      updatedAt: new Date()\n    };\n    const updatedClients = [...currentClients];\n    updatedClients[index] = updatedClient;\n    this.clients$.next(updatedClients);\n    return of(updatedClient).pipe(delay(500));\n  }\n  deleteClient(id) {\n    const currentClients = this.clients$.value;\n    const filteredClients = currentClients.filter(client => client.id !== id);\n    if (filteredClients.length === currentClients.length) {\n      throw new Error('Client not found');\n    }\n    this.clients$.next(filteredClients);\n    return of(true).pipe(delay(300));\n  }\n  searchClients(query) {\n    return this.clients$.pipe(map(clients => clients.filter(client => client.name.toLowerCase().includes(query.toLowerCase()) || client.email.toLowerCase().includes(query.toLowerCase()) || client.contactPerson.toLowerCase().includes(query.toLowerCase()) || client.industry.toLowerCase().includes(query.toLowerCase()))), delay(300));\n  }\n  getClientsByStatus(status) {\n    return this.clients$.pipe(map(clients => clients.filter(client => client.status === status)), delay(300));\n  }\n  getClientStats() {\n    return this.clients$.pipe(map(clients => ({\n      total: clients.length,\n      active: clients.filter(c => c.status === 'ACTIVE').length,\n      inactive: clients.filter(c => c.status === 'INACTIVE').length,\n      prospects: clients.filter(c => c.status === 'PROSPECT').length\n    })), delay(200));\n  }\n  static {\n    this.ɵfac = function ClientService_Factory(t) {\n      return new (t || ClientService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ClientService,\n      factory: ClientService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "BehaviorSubject", "delay", "map", "ClientService", "constructor", "clients$", "nextId", "initializeMockData", "mockClients", "id", "name", "email", "phone", "address", "<PERSON><PERSON><PERSON>", "industry", "website", "notes", "status", "createdAt", "Date", "updatedAt", "next", "Math", "max", "c", "getClients", "asObservable", "getClient", "pipe", "clients", "find", "client", "createClient", "clientData", "newClient", "currentClients", "value", "updateClient", "index", "findIndex", "Error", "updatedClient", "updatedClients", "deleteClient", "filteredClients", "filter", "length", "searchClients", "query", "toLowerCase", "includes", "getClientsByStatus", "getClientStats", "total", "active", "inactive", "prospects", "factory", "ɵfac", "providedIn"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { delay, map } from 'rxjs/operators';\n\nexport interface ClientDto {\n  id: number;\n  name: string;\n  industry: string;\n  address?: string;\n  website?: string;\n  notes?: string;\n  status: 'ACTIVE' | 'INACTIVE' | 'PROSPECT';\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface CreateClientDto {\n  name: string;\n  industry: string;\n  address?: string;\n  website?: string;\n  notes?: string;\n  status: 'ACTIVE' | 'INACTIVE' | 'PROSPECT';\n}\n\nexport interface UpdateClientDto extends Partial<CreateClientDto> {\n  id: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private clients$ = new BehaviorSubject<ClientDto[]>([]);\n  private nextId = 1;\n\n  constructor() {\n    // Initialize with mock data\n    this.initializeMockData();\n  }\n\n  private initializeMockData(): void {\n    const mockClients: ClientDto[] = [\n      {\n        id: 1,\n        name: 'TechCorp Solutions',\n        email: '<EMAIL>',\n        phone: '+33 1 23 45 67 89',\n        address: '123 Avenue des Champs-Élysées, 75008 Paris',\n        contactPerson: 'Marie Dub<PERSON>',\n        industry: 'Technology',\n        website: 'https://techcorp.fr',\n        notes: 'Client principal, projets récurrents',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-06-20')\n      },\n      {\n        id: 2,\n        name: 'StartupInnovate',\n        email: '<EMAIL>',\n        phone: '+33 1 98 76 54 32',\n        address: '45 Rue de Rivoli, 75001 Paris',\n        contactPerson: 'Jean Martin',\n        industry: 'Fintech',\n        website: 'https://startupinnovate.com',\n        notes: 'Startup prometteuse, budget limité',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-02-10'),\n        updatedAt: new Date('2024-06-15')\n      },\n      {\n        id: 3,\n        name: 'E-Commerce Plus',\n        email: '<EMAIL>',\n        phone: '+33 1 11 22 33 44',\n        address: '78 Boulevard Saint-Germain, 75006 Paris',\n        contactPerson: 'Sophie Laurent',\n        industry: 'E-commerce',\n        status: 'PROSPECT',\n        createdAt: new Date('2024-06-01'),\n        updatedAt: new Date('2024-06-01')\n      },\n      {\n        id: 4,\n        name: 'ConsultingPro',\n        email: '<EMAIL>',\n        phone: '+33 1 55 66 77 88',\n        address: '12 Place Vendôme, 75001 Paris',\n        contactPerson: 'Pierre Moreau',\n        industry: 'Consulting',\n        notes: 'Client exigeant mais bien payeur',\n        status: 'INACTIVE',\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-03-15')\n      }\n    ];\n\n    this.clients$.next(mockClients);\n    this.nextId = Math.max(...mockClients.map(c => c.id)) + 1;\n  }\n\n  getClients(): Observable<ClientDto[]> {\n    return this.clients$.asObservable();\n  }\n\n  getClient(id: number): Observable<ClientDto | undefined> {\n    return this.clients$.pipe(\n      map(clients => clients.find(client => client.id === id)),\n      delay(300) // Simulate API delay\n    );\n  }\n\n  createClient(clientData: CreateClientDto): Observable<ClientDto> {\n    const newClient: ClientDto = {\n      ...clientData,\n      id: this.nextId++,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    const currentClients = this.clients$.value;\n    this.clients$.next([...currentClients, newClient]);\n\n    return of(newClient).pipe(delay(500));\n  }\n\n  updateClient(clientData: UpdateClientDto): Observable<ClientDto> {\n    const currentClients = this.clients$.value;\n    const index = currentClients.findIndex(client => client.id === clientData.id);\n    \n    if (index === -1) {\n      throw new Error('Client not found');\n    }\n\n    const updatedClient: ClientDto = {\n      ...currentClients[index],\n      ...clientData,\n      updatedAt: new Date()\n    };\n\n    const updatedClients = [...currentClients];\n    updatedClients[index] = updatedClient;\n    this.clients$.next(updatedClients);\n\n    return of(updatedClient).pipe(delay(500));\n  }\n\n  deleteClient(id: number): Observable<boolean> {\n    const currentClients = this.clients$.value;\n    const filteredClients = currentClients.filter(client => client.id !== id);\n    \n    if (filteredClients.length === currentClients.length) {\n      throw new Error('Client not found');\n    }\n\n    this.clients$.next(filteredClients);\n    return of(true).pipe(delay(300));\n  }\n\n  searchClients(query: string): Observable<ClientDto[]> {\n    return this.clients$.pipe(\n      map(clients => \n        clients.filter(client =>\n          client.name.toLowerCase().includes(query.toLowerCase()) ||\n          client.email.toLowerCase().includes(query.toLowerCase()) ||\n          client.contactPerson.toLowerCase().includes(query.toLowerCase()) ||\n          client.industry.toLowerCase().includes(query.toLowerCase())\n        )\n      ),\n      delay(300)\n    );\n  }\n\n  getClientsByStatus(status: 'ACTIVE' | 'INACTIVE' | 'PROSPECT'): Observable<ClientDto[]> {\n    return this.clients$.pipe(\n      map(clients => clients.filter(client => client.status === status)),\n      delay(300)\n    );\n  }\n\n  getClientStats(): Observable<{total: number, active: number, inactive: number, prospects: number}> {\n    return this.clients$.pipe(\n      map(clients => ({\n        total: clients.length,\n        active: clients.filter(c => c.status === 'ACTIVE').length,\n        inactive: clients.filter(c => c.status === 'INACTIVE').length,\n        prospects: clients.filter(c => c.status === 'PROSPECT').length\n      })),\n      delay(200)\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,EAAE,EAAEC,eAAe,QAAQ,MAAM;AACtD,SAASC,KAAK,EAAEC,GAAG,QAAQ,gBAAgB;;AA8B3C,OAAM,MAAOC,aAAa;EAIxBC,YAAA;IAHQ,KAAAC,QAAQ,GAAG,IAAIL,eAAe,CAAc,EAAE,CAAC;IAC/C,KAAAM,MAAM,GAAG,CAAC;IAGhB;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,MAAMC,WAAW,GAAgB,CAC/B;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,4CAA4C;MACrDC,aAAa,EAAE,cAAc;MAC7BC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE,sCAAsC;MAC7CC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,EACD;MACEX,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,2BAA2B;MAClCC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,+BAA+B;MACxCC,aAAa,EAAE,aAAa;MAC5BC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,oCAAoC;MAC3CC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,EACD;MACEX,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,yCAAyC;MAClDC,aAAa,EAAE,gBAAgB;MAC/BC,QAAQ,EAAE,YAAY;MACtBG,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,EACD;MACEX,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,0BAA0B;MACjCC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,+BAA+B;MACxCC,aAAa,EAAE,eAAe;MAC9BC,QAAQ,EAAE,YAAY;MACtBE,KAAK,EAAE,kCAAkC;MACzCC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,CACF;IAED,IAAI,CAACf,QAAQ,CAACiB,IAAI,CAACd,WAAW,CAAC;IAC/B,IAAI,CAACF,MAAM,GAAGiB,IAAI,CAACC,GAAG,CAAC,GAAGhB,WAAW,CAACN,GAAG,CAACuB,CAAC,IAAIA,CAAC,CAAChB,EAAE,CAAC,CAAC,GAAG,CAAC;EAC3D;EAEAiB,UAAUA,CAAA;IACR,OAAO,IAAI,CAACrB,QAAQ,CAACsB,YAAY,EAAE;EACrC;EAEAC,SAASA,CAACnB,EAAU;IAClB,OAAO,IAAI,CAACJ,QAAQ,CAACwB,IAAI,CACvB3B,GAAG,CAAC4B,OAAO,IAAIA,OAAO,CAACC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACvB,EAAE,KAAKA,EAAE,CAAC,CAAC,EACxDR,KAAK,CAAC,GAAG,CAAC,CAAC;KACZ;EACH;EAEAgC,YAAYA,CAACC,UAA2B;IACtC,MAAMC,SAAS,GAAc;MAC3B,GAAGD,UAAU;MACbzB,EAAE,EAAE,IAAI,CAACH,MAAM,EAAE;MACjBa,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,SAAS,EAAE,IAAID,IAAI;KACpB;IAED,MAAMgB,cAAc,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,KAAK;IAC1C,IAAI,CAAChC,QAAQ,CAACiB,IAAI,CAAC,CAAC,GAAGc,cAAc,EAAED,SAAS,CAAC,CAAC;IAElD,OAAOpC,EAAE,CAACoC,SAAS,CAAC,CAACN,IAAI,CAAC5B,KAAK,CAAC,GAAG,CAAC,CAAC;EACvC;EAEAqC,YAAYA,CAACJ,UAA2B;IACtC,MAAME,cAAc,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,KAAK;IAC1C,MAAME,KAAK,GAAGH,cAAc,CAACI,SAAS,CAACR,MAAM,IAAIA,MAAM,CAACvB,EAAE,KAAKyB,UAAU,CAACzB,EAAE,CAAC;IAE7E,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAIE,KAAK,CAAC,kBAAkB,CAAC;;IAGrC,MAAMC,aAAa,GAAc;MAC/B,GAAGN,cAAc,CAACG,KAAK,CAAC;MACxB,GAAGL,UAAU;MACbb,SAAS,EAAE,IAAID,IAAI;KACpB;IAED,MAAMuB,cAAc,GAAG,CAAC,GAAGP,cAAc,CAAC;IAC1CO,cAAc,CAACJ,KAAK,CAAC,GAAGG,aAAa;IACrC,IAAI,CAACrC,QAAQ,CAACiB,IAAI,CAACqB,cAAc,CAAC;IAElC,OAAO5C,EAAE,CAAC2C,aAAa,CAAC,CAACb,IAAI,CAAC5B,KAAK,CAAC,GAAG,CAAC,CAAC;EAC3C;EAEA2C,YAAYA,CAACnC,EAAU;IACrB,MAAM2B,cAAc,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,KAAK;IAC1C,MAAMQ,eAAe,GAAGT,cAAc,CAACU,MAAM,CAACd,MAAM,IAAIA,MAAM,CAACvB,EAAE,KAAKA,EAAE,CAAC;IAEzE,IAAIoC,eAAe,CAACE,MAAM,KAAKX,cAAc,CAACW,MAAM,EAAE;MACpD,MAAM,IAAIN,KAAK,CAAC,kBAAkB,CAAC;;IAGrC,IAAI,CAACpC,QAAQ,CAACiB,IAAI,CAACuB,eAAe,CAAC;IACnC,OAAO9C,EAAE,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC5B,KAAK,CAAC,GAAG,CAAC,CAAC;EAClC;EAEA+C,aAAaA,CAACC,KAAa;IACzB,OAAO,IAAI,CAAC5C,QAAQ,CAACwB,IAAI,CACvB3B,GAAG,CAAC4B,OAAO,IACTA,OAAO,CAACgB,MAAM,CAACd,MAAM,IACnBA,MAAM,CAACtB,IAAI,CAACwC,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,EAAE,CAAC,IACvDlB,MAAM,CAACrB,KAAK,CAACuC,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,EAAE,CAAC,IACxDlB,MAAM,CAAClB,aAAa,CAACoC,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,EAAE,CAAC,IAChElB,MAAM,CAACjB,QAAQ,CAACmC,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,EAAE,CAAC,CAC5D,CACF,EACDjD,KAAK,CAAC,GAAG,CAAC,CACX;EACH;EAEAmD,kBAAkBA,CAAClC,MAA0C;IAC3D,OAAO,IAAI,CAACb,QAAQ,CAACwB,IAAI,CACvB3B,GAAG,CAAC4B,OAAO,IAAIA,OAAO,CAACgB,MAAM,CAACd,MAAM,IAAIA,MAAM,CAACd,MAAM,KAAKA,MAAM,CAAC,CAAC,EAClEjB,KAAK,CAAC,GAAG,CAAC,CACX;EACH;EAEAoD,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAChD,QAAQ,CAACwB,IAAI,CACvB3B,GAAG,CAAC4B,OAAO,KAAK;MACdwB,KAAK,EAAExB,OAAO,CAACiB,MAAM;MACrBQ,MAAM,EAAEzB,OAAO,CAACgB,MAAM,CAACrB,CAAC,IAAIA,CAAC,CAACP,MAAM,KAAK,QAAQ,CAAC,CAAC6B,MAAM;MACzDS,QAAQ,EAAE1B,OAAO,CAACgB,MAAM,CAACrB,CAAC,IAAIA,CAAC,CAACP,MAAM,KAAK,UAAU,CAAC,CAAC6B,MAAM;MAC7DU,SAAS,EAAE3B,OAAO,CAACgB,MAAM,CAACrB,CAAC,IAAIA,CAAC,CAACP,MAAM,KAAK,UAAU,CAAC,CAAC6B;KACzD,CAAC,CAAC,EACH9C,KAAK,CAAC,GAAG,CAAC,CACX;EACH;;;uBA/JWE,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAuD,OAAA,EAAbvD,aAAa,CAAAwD,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}