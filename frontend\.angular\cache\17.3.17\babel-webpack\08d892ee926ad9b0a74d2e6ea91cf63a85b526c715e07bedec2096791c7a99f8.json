{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction LoginComponent_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction LoginComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(fb, authService, router, snackBar) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.hidePassword = true;\n      this.loginForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]]\n      });\n    }\n    ngOnInit() {\n      // Redirect if already authenticated\n      if (this.authService.isAuthenticated()) {\n        this.router.navigate(['/dashboard']);\n      }\n    }\n    onSubmit() {\n      if (this.loginForm.valid) {\n        this.isLoading = true;\n        const credentials = this.loginForm.value;\n        this.authService.login(credentials).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.snackBar.open('Connexion réussie!', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate(['/dashboard']);\n          },\n          error: error => {\n            this.isLoading = false;\n            let errorMessage = 'Erreur de connexion. Veuillez réessayer.';\n            if (error.status === 401) {\n              errorMessage = 'Email ou mot de passe incorrect.';\n            } else if (error.status === 0) {\n              errorMessage = 'Impossible de se connecter au serveur.';\n            }\n            this.snackBar.open(errorMessage, 'Fermer', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      }\n    }\n    getErrorMessage(field) {\n      const control = this.loginForm.get(field);\n      if (control?.hasError('required')) {\n        return `${field === 'email' ? 'Email' : 'Mot de passe'} requis`;\n      }\n      if (control?.hasError('email')) {\n        return 'Email invalide';\n      }\n      if (control?.hasError('minlength')) {\n        return 'Le mot de passe doit contenir au moins 6 caractères';\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 36,\n        vars: 11,\n        consts: [[1, \"login-container\"], [1, \"login-card-container\"], [1, \"login-card\"], [1, \"login-title\"], [1, \"login-icon\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", \"full-width\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"login-spinner\", 4, \"ngIf\"], [1, \"login-actions\"], [1, \"signup-link\"], [\"routerLink\", \"/register\", 1, \"signup-link-text\"], [\"diameter\", \"20\", 1, \"login-spinner\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\")(4, \"mat-card-title\", 3)(5, \"mat-icon\", 4);\n            i0.ɵɵtext(6, \"work\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7, \" Connexion \\u00E0 Indezy \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n            i0.ɵɵtext(9, \" G\\u00E9rez vos missions freelance en toute simplicit\\u00E9 \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(12, \"mat-form-field\", 6)(13, \"mat-label\");\n            i0.ɵɵtext(14, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"input\", 7);\n            i0.ɵɵelementStart(16, \"mat-icon\", 8);\n            i0.ɵɵtext(17, \"email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(18, LoginComponent_mat_error_18_Template, 2, 1, \"mat-error\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"mat-form-field\", 6)(20, \"mat-label\");\n            i0.ɵɵtext(21, \"Mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 10);\n            i0.ɵɵelementStart(23, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_23_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(24, \"mat-icon\");\n            i0.ɵɵtext(25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(26, LoginComponent_mat_error_26_Template, 2, 1, \"mat-error\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"button\", 12);\n            i0.ɵɵtemplate(28, LoginComponent_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 13)(29, LoginComponent_span_29_Template, 2, 0, \"span\", 9)(30, LoginComponent_span_30_Template, 2, 0, \"span\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"mat-card-actions\", 14)(32, \"p\", 15);\n            i0.ɵɵtext(33, \" Pas encore de compte ? \");\n            i0.ɵɵelementStart(34, \"a\", 16);\n            i0.ɵɵtext(35, \"Cr\\u00E9er un compte\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_6_0;\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance();\n            i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatSnackBarModule, MatProgressSpinnerModule, i11.MatProgressSpinner],\n        styles: [\".login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.login-card-container[_ngcontent-%COMP%]{width:100%;max-width:400px}.login-card[_ngcontent-%COMP%]{padding:20px;border-radius:12px;box-shadow:0 8px 32px #0000001a}.login-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:24px;font-weight:600;color:#333;margin-bottom:8px}.login-icon[_ngcontent-%COMP%]{font-size:28px;color:#667eea}.login-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;margin-top:20px}.full-width[_ngcontent-%COMP%]{width:100%}.login-button[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:8px;position:relative}.login-spinner[_ngcontent-%COMP%]{margin-right:8px}.login-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;padding-top:16px}.signup-link[_ngcontent-%COMP%]{margin:0;text-align:center;color:#666}.signup-link-text[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.signup-link-text[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 480px){.login-container[_ngcontent-%COMP%]{padding:10px}.login-card[_ngcontent-%COMP%]{padding:16px}.login-title[_ngcontent-%COMP%]{font-size:20px}}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}