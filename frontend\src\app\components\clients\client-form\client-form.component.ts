import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil } from 'rxjs';

import { ClientService, ClientDto, CreateClientDto, UpdateClientDto } from '../../../services/client.service';

@Component({
  selector: 'app-client-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,

    MatProgressSpinnerModule
  ],
  templateUrl: './client-form.component.html',
  styleUrls: ['./client-form.component.scss']
})
export class ClientFormComponent implements OnInit, OnDestroy {
  clientForm: FormGroup;
  isEditMode = false;
  isLoading = false;
  isSaving = false;
  clientId?: number;
  
  industries = [
    'Technology',
    'Finance',
    'Healthcare',
    'Education',
    'E-commerce',
    'Manufacturing',
    'Consulting',
    'Marketing',
    'Real Estate',
    'Retail',
    'Transportation',
    'Energy',
    'Media',
    'Government',
    'Non-profit',
    'Other'
  ];
  
  statuses = [
    { value: 'ACTIVE', label: 'Actif' },
    { value: 'INACTIVE', label: 'Inactif' },
    { value: 'PROSPECT', label: 'Prospect' }
  ];
  
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private clientService: ClientService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.clientForm = this.createForm();
  }

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['id']) {
        this.clientId = +params['id'];
        this.isEditMode = true;
        this.loadClient();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      industry: ['', [Validators.required]],
      address: [''],
      website: [''],
      notes: [''],
      status: ['ACTIVE', [Validators.required]]
    });
  }

  private loadClient(): void {
    if (!this.clientId) return;
    
    this.isLoading = true;
    this.clientService.getClient(this.clientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (client) => {
          if (client) {
            this.clientForm.patchValue(client);
          } else {
            this.snackBar.open('Client non trouvé', 'Fermer', { duration: 3000 });
            this.router.navigate(['/clients']);
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading client:', error);
          this.snackBar.open('Erreur lors du chargement du client', 'Fermer', { duration: 3000 });
          this.isLoading = false;
        }
      });
  }

  onSubmit(): void {
    if (this.clientForm.valid) {
      this.isSaving = true;
      const formValue = this.clientForm.value;
      
      if (this.isEditMode && this.clientId) {
        const updateData: UpdateClientDto = {
          id: this.clientId,
          ...formValue
        };
        
        this.clientService.updateClient(updateData)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.snackBar.open('Client modifié avec succès', 'Fermer', { duration: 3000 });
              this.router.navigate(['/clients']);
            },
            error: (error) => {
              console.error('Error updating client:', error);
              this.snackBar.open('Erreur lors de la modification du client', 'Fermer', { duration: 3000 });
              this.isSaving = false;
            }
          });
      } else {
        const createData: CreateClientDto = formValue;
        
        this.clientService.createClient(createData)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.snackBar.open('Client créé avec succès', 'Fermer', { duration: 3000 });
              this.router.navigate(['/clients']);
            },
            error: (error) => {
              console.error('Error creating client:', error);
              this.snackBar.open('Erreur lors de la création du client', 'Fermer', { duration: 3000 });
              this.isSaving = false;
            }
          });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/clients']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.clientForm.controls).forEach(key => {
      const control = this.clientForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const control = this.clientForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return 'Ce champ est requis';
      }
      if (control.errors['email']) {
        return 'Veuillez saisir une adresse email valide';
      }
      if (control.errors['minlength']) {
        return `Minimum ${control.errors['minlength'].requiredLength} caractères`;
      }
    }
    return '';
  }
}
