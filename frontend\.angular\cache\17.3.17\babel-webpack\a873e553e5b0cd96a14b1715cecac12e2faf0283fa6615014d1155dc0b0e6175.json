{"ast": null, "code": "import { of, delay } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ContactService = /*#__PURE__*/(() => {\n  class ContactService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = '/api/contacts';\n      // Mock data for development\n      this.mockContacts = [{\n        id: 1,\n        firstName: 'Marie',\n        lastName: 'Dubois',\n        email: '<EMAIL>',\n        phone: '+33 1 23 45 67 89',\n        position: 'Directrice RH',\n        clientId: 1,\n        clientName: 'TechCorp Solutions',\n        notes: 'Contact principal pour les recrutements développeurs',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-15')\n      }, {\n        id: 2,\n        firstName: '<PERSON>',\n        lastName: '<PERSON>',\n        email: '<EMAIL>',\n        phone: '+33 1 98 76 54 32',\n        position: 'CTO',\n        clientId: 2,\n        clientName: 'Innovate Digital',\n        notes: 'Décideur technique, préfère les entretiens le matin',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-02-01'),\n        updatedAt: new Date('2024-02-10')\n      }, {\n        id: 3,\n        firstName: 'Sophie',\n        lastName: 'Leroy',\n        email: '<EMAIL>',\n        phone: '+33 6 12 34 56 78',\n        position: 'Founder & CEO',\n        clientId: 3,\n        clientName: 'StartupBoost',\n        notes: 'Très réactive, privilégier les échanges par email',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-02-15'),\n        updatedAt: new Date('2024-02-15')\n      }, {\n        id: 4,\n        firstName: 'Jean',\n        lastName: 'Moreau',\n        email: '<EMAIL>',\n        phone: '+33 1 45 67 89 01',\n        position: 'Senior Manager',\n        clientId: 4,\n        clientName: 'Consulting Pro',\n        notes: 'Contact pour missions de conseil en transformation digitale',\n        status: 'INACTIVE',\n        createdAt: new Date('2024-01-20'),\n        updatedAt: new Date('2024-03-01')\n      }];\n    }\n    getContacts() {\n      return of(this.mockContacts).pipe(delay(500));\n    }\n    getContact(id) {\n      const contact = this.mockContacts.find(c => c.id === id);\n      return of(contact).pipe(delay(300));\n    }\n    createContact(contact) {\n      const newContact = {\n        ...contact,\n        id: Math.max(...this.mockContacts.map(c => c.id)) + 1,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n      this.mockContacts.push(newContact);\n      return of(newContact).pipe(delay(500));\n    }\n    updateContact(id, contact) {\n      const index = this.mockContacts.findIndex(c => c.id === id);\n      if (index !== -1) {\n        this.mockContacts[index] = {\n          ...this.mockContacts[index],\n          ...contact,\n          updatedAt: new Date()\n        };\n        return of(this.mockContacts[index]).pipe(delay(500));\n      }\n      throw new Error('Contact not found');\n    }\n    deleteContact(id) {\n      const index = this.mockContacts.findIndex(c => c.id === id);\n      if (index !== -1) {\n        this.mockContacts.splice(index, 1);\n      }\n      return of(void 0).pipe(delay(300));\n    }\n    searchContacts(query) {\n      const filtered = this.mockContacts.filter(contact => contact.firstName.toLowerCase().includes(query.toLowerCase()) || contact.lastName.toLowerCase().includes(query.toLowerCase()) || contact.email.toLowerCase().includes(query.toLowerCase()) || contact.position.toLowerCase().includes(query.toLowerCase()) || contact.clientName.toLowerCase().includes(query.toLowerCase()));\n      return of(filtered).pipe(delay(300));\n    }\n    getContactsByClient(clientId) {\n      const filtered = this.mockContacts.filter(contact => contact.clientId === clientId);\n      return of(filtered).pipe(delay(300));\n    }\n    filterContacts(filters) {\n      let filtered = [...this.mockContacts];\n      if (filters.status) {\n        filtered = filtered.filter(contact => contact.status === filters.status);\n      }\n      if (filters.clientId) {\n        filtered = filtered.filter(contact => contact.clientId === filters.clientId);\n      }\n      return of(filtered).pipe(delay(300));\n    }\n    static {\n      this.ɵfac = function ContactService_Factory(t) {\n        return new (t || ContactService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ContactService,\n        factory: ContactService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ContactService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}