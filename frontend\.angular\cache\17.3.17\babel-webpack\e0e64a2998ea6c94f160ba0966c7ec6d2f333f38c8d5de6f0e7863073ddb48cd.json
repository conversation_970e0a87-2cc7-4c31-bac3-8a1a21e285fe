{"ast": null, "code": "import { BehaviorSubject, Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.API_URL = `${environment.apiUrl}/auth`;\n      this.TOKEN_KEY = 'indezy_token';\n      this.USER_KEY = 'indezy_user';\n      this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n      this.currentUser$ = this.currentUserSubject.asObservable();\n    }\n    login(credentials) {\n      // Mock login for development - replace with real API call when backend is ready\n      return new Observable(observer => {\n        setTimeout(() => {\n          if (credentials.email && credentials.password) {\n            const mockResponse = {\n              token: 'mock-jwt-token-' + Date.now(),\n              user: {\n                id: 1,\n                email: credentials.email,\n                firstName: 'John',\n                lastName: 'Doe'\n              }\n            };\n            this.setToken(mockResponse.token);\n            this.setUser(mockResponse.user);\n            this.currentUserSubject.next(mockResponse.user);\n            observer.next(mockResponse);\n            observer.complete();\n          } else {\n            observer.error({\n              status: 401,\n              message: 'Invalid credentials'\n            });\n          }\n        }, 1000);\n      });\n    }\n    register(userData) {\n      // Mock register for development - replace with real API call when backend is ready\n      return new Observable(observer => {\n        setTimeout(() => {\n          if (userData.email && userData.password && userData.firstName && userData.lastName) {\n            const mockResponse = {\n              token: 'mock-jwt-token-' + Date.now(),\n              user: {\n                id: 1,\n                email: userData.email,\n                firstName: userData.firstName,\n                lastName: userData.lastName\n              }\n            };\n            this.setToken(mockResponse.token);\n            this.setUser(mockResponse.user);\n            this.currentUserSubject.next(mockResponse.user);\n            observer.next(mockResponse);\n            observer.complete();\n          } else {\n            observer.error({\n              status: 400,\n              message: 'Invalid user data'\n            });\n          }\n        }, 1000);\n      });\n    }\n    logout() {\n      localStorage.removeItem(this.TOKEN_KEY);\n      localStorage.removeItem(this.USER_KEY);\n      this.currentUserSubject.next(null);\n      this.router.navigate(['/login']);\n    }\n    getToken() {\n      return localStorage.getItem(this.TOKEN_KEY);\n    }\n    getUser() {\n      return this.getUserFromStorage();\n    }\n    isAuthenticated() {\n      const token = this.getToken();\n      if (!token) {\n        return false;\n      }\n      // For mock tokens, just check if token exists and user exists\n      if (token.startsWith('mock-jwt-token-')) {\n        return this.getUser() !== null;\n      }\n      // Check if real JWT token is expired (basic check)\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        return payload.exp > currentTime;\n      } catch {\n        return false;\n      }\n    }\n    setToken(token) {\n      localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    setUser(user) {\n      localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    }\n    getUserFromStorage() {\n      const userStr = localStorage.getItem(this.USER_KEY);\n      return userStr ? JSON.parse(userStr) : null;\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}