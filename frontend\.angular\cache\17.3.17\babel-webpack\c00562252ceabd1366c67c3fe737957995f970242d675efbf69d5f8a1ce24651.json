{"ast": null, "code": "export const projectRoutes = [{\n  path: '',\n  loadComponent: () => import('./project-list/project-list.component').then(m => m.ProjectListComponent)\n}, {\n  path: 'new',\n  loadComponent: () => import('./project-form/project-form.component').then(m => m.ProjectFormComponent)\n}, {\n  path: ':id',\n  loadComponent: () => import('./project-detail/project-detail.component').then(m => m.ProjectDetailComponent)\n}, {\n  path: ':id/edit',\n  loadComponent: () => import('./project-form/project-form.component').then(m => m.ProjectFormComponent)\n}];", "map": {"version": 3, "names": ["projectRoutes", "path", "loadComponent", "then", "m", "ProjectListComponent", "ProjectFormComponent", "ProjectDetailComponent"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\projects.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const projectRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./project-list/project-list.component').then(m => m.ProjectListComponent)\n  },\n  {\n    path: 'new',\n    loadComponent: () => import('./project-form/project-form.component').then(m => m.ProjectFormComponent)\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./project-detail/project-detail.component').then(m => m.ProjectDetailComponent)\n  },\n  {\n    path: ':id/edit',\n    loadComponent: () => import('./project-form/project-form.component').then(m => m.ProjectFormComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,oBAAoB;CACtG,EACD;EACEJ,IAAI,EAAE,KAAK;EACXC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,oBAAoB;CACtG,EACD;EACEL,IAAI,EAAE,KAAK;EACXC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,sBAAsB;CAC5G,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,oBAAoB;CACtG,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}