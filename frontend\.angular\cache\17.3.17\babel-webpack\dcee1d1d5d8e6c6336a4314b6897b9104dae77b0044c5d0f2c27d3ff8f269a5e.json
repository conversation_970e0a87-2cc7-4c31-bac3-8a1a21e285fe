{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/toolbar\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/sidenav\";\nimport * as i8 from \"@angular/material/list\";\nimport * as i9 from \"@angular/material/menu\";\nfunction AppComponent_div_1_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r4.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.label);\n  }\n}\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-toolbar\", 5)(2, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const sidenav_r2 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(sidenav_r2.toggle());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 7);\n    i0.ɵɵtext(6, \"Indezy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"span\", 8);\n    i0.ɵɵelementStart(8, \"button\", 9)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"arrow_drop_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-menu\", null, 1)(17, \"button\", 11)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(27, \"mat-sidenav-container\", 13)(28, \"mat-sidenav\", 14, 2)(30, \"mat-nav-list\");\n    i0.ɵɵtemplate(31, AppComponent_div_1_a_31_Template, 5, 3, \"a\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-sidenav-content\", 16)(33, \"div\", 17);\n    i0.ɵɵelement(34, \"router-outlet\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const userMenu_r5 = i0.ɵɵreference(16);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.firstName, \" \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.lastName, \"\");\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.menuItems);\n  }\n}\nfunction AppComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(authService) {\n      this.authService = authService;\n      this.title = 'Indezy - Suivi de missions pour freelances';\n      this.currentUser = null;\n      this.menuItems = [{\n        label: 'Tableau de bord',\n        route: '/dashboard',\n        icon: 'dashboard'\n      }, {\n        label: 'Projets',\n        route: '/projects',\n        icon: 'work'\n      }, {\n        label: 'Clients & Contacts',\n        route: '/clients',\n        icon: 'business'\n      }, {\n        label: 'Sources',\n        route: '/sources',\n        icon: 'source'\n      }, {\n        label: 'Kanban',\n        route: '/kanban',\n        icon: 'view_kanban'\n      }, {\n        label: 'Profil',\n        route: '/profile',\n        icon: 'person'\n      }];\n    }\n    ngOnInit() {\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n      });\n    }\n    logout() {\n      this.authService.logout();\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 2,\n        consts: [[\"unauthenticatedView\", \"\"], [\"userMenu\", \"matMenu\"], [\"sidenav\", \"\"], [1, \"app-container\"], [\"class\", \"app-container\", 4, \"ngIf\", \"ngIfElse\"], [\"color\", \"primary\", 1, \"app-toolbar\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [\"mat-button\", \"\", 1, \"user-button\", 3, \"matMenuTriggerFor\"], [1, \"user-name\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"app-sidenav-container\"], [\"mode\", \"side\", \"opened\", \"\", 1, \"app-sidenav\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"app-main-content\"], [1, \"app-content\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\"], [\"matListItemTitle\", \"\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 3);\n            i0.ɵɵtemplate(1, AppComponent_div_1_Template, 35, 4, \"div\", 4)(2, AppComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const unauthenticatedView_r6 = i0.ɵɵreference(3);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.isAuthenticated())(\"ngIfElse\", unauthenticatedView_r6);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterOutlet, RouterModule, i3.RouterLink, i3.RouterLinkActive, MatToolbarModule, i4.MatToolbar, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatSidenavModule, i7.MatSidenav, i7.MatSidenavContainer, i7.MatSidenavContent, MatListModule, i8.MatNavList, i8.MatListItem, i8.MatListItemIcon, i8.MatListItemTitle, MatMenuModule, i9.MatMenu, i9.MatMenuItem, i9.MatMenuTrigger],\n        styles: [\".app-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh}.app-toolbar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:1000}.app-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600}.app-toolbar[_ngcontent-%COMP%]   .spacer[_ngcontent-%COMP%]{flex:1 1 auto}.app-toolbar[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.app-toolbar[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-weight:500}.app-sidenav-container[_ngcontent-%COMP%]{flex:1;margin-top:64px}.app-sidenav[_ngcontent-%COMP%]{width:280px;border-right:1px solid rgba(0,0,0,.12)}.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-nav-list[_ngcontent-%COMP%]{padding-top:16px}.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]{margin:4px 12px;border-radius:8px}.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item.active-nav-item[_ngcontent-%COMP%]{background-color:rgba(var(--mat-primary-500),.1);color:var(--mat-primary-500)}.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item.active-nav-item[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{color:var(--mat-primary-500)}.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]:hover:not(.active-nav-item){background-color:#0000000a}.app-main-content[_ngcontent-%COMP%]{background-color:#f5f5f5}.app-content[_ngcontent-%COMP%]{padding:24px;min-height:calc(100vh - 64px)}@media (max-width: 768px){.app-sidenav[_ngcontent-%COMP%]{width:100%}.app-content[_ngcontent-%COMP%]{padding:16px}}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}