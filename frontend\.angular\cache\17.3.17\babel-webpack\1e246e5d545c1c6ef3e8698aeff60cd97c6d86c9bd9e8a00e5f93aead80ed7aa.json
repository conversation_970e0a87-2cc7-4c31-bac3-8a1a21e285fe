{"ast": null, "code": "export const projectRoutes = [{\n  path: '',\n  loadComponent: () => import('./project-list/project-list.component').then(m => m.ProjectListComponent)\n}, {\n  path: 'new',\n  loadComponent: () => import('./project-form/project-form.component').then(m => m.ProjectFormComponent)\n}, {\n  path: ':id',\n  loadComponent: () => import('./project-detail/project-detail.component').then(m => m.ProjectDetailComponent)\n}, {\n  path: ':id/edit',\n  loadComponent: () => import('./project-form/project-form.component').then(m => m.ProjectFormComponent)\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}