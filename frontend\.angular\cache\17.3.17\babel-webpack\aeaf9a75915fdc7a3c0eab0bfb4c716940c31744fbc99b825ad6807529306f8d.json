{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/project.service\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/chips\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nconst _c0 = a0 => [\"/projects\", a0];\nconst _c1 = a0 => [\"/projects\", a0, \"edit\"];\nfunction ProjectListComponent_mat_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const mode_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", mode_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", mode_r1.label, \" \");\n  }\n}\nfunction ProjectListComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des projets...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", project_r3.dailyRate, \"\\u20AC/jour\");\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.getWorkModeLabel(project_r3.workMode));\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", project_r3.durationInMonths, \" mois\");\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, project_r3.startDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(project_r3.techStack);\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r3.description, \" \");\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 24)(1, \"mat-card-header\")(2, \"mat-card-title\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"mat-chip\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 29);\n    i0.ɵɵtemplate(11, ProjectListComponent_div_46_mat_card_1_div_11_Template, 5, 1, \"div\", 30)(12, ProjectListComponent_div_46_mat_card_1_div_12_Template, 5, 1, \"div\", 30)(13, ProjectListComponent_div_46_mat_card_1_div_13_Template, 5, 1, \"div\", 30)(14, ProjectListComponent_div_46_mat_card_1_div_14_Template, 6, 4, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ProjectListComponent_div_46_mat_card_1_div_15_Template, 5, 1, \"div\", 31)(16, ProjectListComponent_div_46_mat_card_1_p_16_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-actions\", 33)(18, \"button\", 34)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Voir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 34)(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_46_mat_card_1_Template_button_click_26_listener() {\n      const project_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.deleteProject(project_r3.id));\n    });\n    i0.ɵɵelementStart(27, \"mat-icon\");\n    i0.ɵɵtext(28, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", project_r3.role, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", project_r3.clientName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r3.getProjectStatusColor(project_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getProjectStatusText(project_r3), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", project_r3.dailyRate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.workMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.durationInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.startDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.techStack);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c0, project_r3.id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(14, _c1, project_r3.id));\n  }\n}\nfunction ProjectListComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProjectListComponent_div_46_mat_card_1_Template, 30, 16, \"mat-card\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredProjects);\n  }\n}\nfunction ProjectListComponent_div_47_h3_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1, \"Aucun projet trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_h3_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1, \"Aucun projet ne correspond aux filtres\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Commencez par ajouter votre premier projet !\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Essayez de modifier vos crit\\u00E8res de recherche.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 4)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Ajouter un projet \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_47_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.clearFilters());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Effacer les filtres \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 40)(4, \"mat-icon\", 41);\n    i0.ɵɵtext(5, \"work_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ProjectListComponent_div_47_h3_6_Template, 2, 0, \"h3\", 42)(7, ProjectListComponent_div_47_h3_7_Template, 2, 0, \"h3\", 42)(8, ProjectListComponent_div_47_p_8_Template, 2, 0, \"p\", 42)(9, ProjectListComponent_div_47_p_9_Template, 2, 0, \"p\", 42)(10, ProjectListComponent_div_47_button_10_Template, 4, 0, \"button\", 43)(11, ProjectListComponent_div_47_button_11_Template, 4, 0, \"button\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length > 0);\n  }\n}\nexport let ProjectListComponent = /*#__PURE__*/(() => {\n  class ProjectListComponent {\n    constructor(projectService, authService, fb, snackBar) {\n      this.projectService = projectService;\n      this.authService = authService;\n      this.fb = fb;\n      this.snackBar = snackBar;\n      this.projects = [];\n      this.filteredProjects = [];\n      this.isLoading = false;\n      this.workModeOptions = [{\n        value: 'REMOTE',\n        label: 'Télétravail'\n      }, {\n        value: 'ONSITE',\n        label: 'Sur site'\n      }, {\n        value: 'HYBRID',\n        label: 'Hybride'\n      }];\n      this.filterForm = this.fb.group({\n        minRate: [''],\n        maxRate: [''],\n        workMode: [''],\n        techStack: ['']\n      });\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getUser();\n      this.loadProjects();\n      this.setupFilters();\n    }\n    loadProjects() {\n      if (!this.currentUser?.id) return;\n      this.isLoading = true;\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: projects => {\n          this.projects = projects;\n          this.filteredProjects = projects;\n          this.isLoading = false;\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open('Erreur lors du chargement des projets', 'Fermer', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          console.error('Error loading projects:', error);\n        }\n      });\n    }\n    setupFilters() {\n      this.filterForm.valueChanges.subscribe(() => {\n        this.applyFilters();\n      });\n    }\n    applyFilters() {\n      const filters = this.filterForm.value;\n      this.filteredProjects = this.projects.filter(project => {\n        // Rate filter\n        if (filters.minRate && project.dailyRate && project.dailyRate < filters.minRate) {\n          return false;\n        }\n        if (filters.maxRate && project.dailyRate && project.dailyRate > filters.maxRate) {\n          return false;\n        }\n        // Work mode filter\n        if (filters.workMode && project.workMode !== filters.workMode) {\n          return false;\n        }\n        // Tech stack filter\n        if (filters.techStack && project.techStack && !project.techStack.toLowerCase().includes(filters.techStack.toLowerCase())) {\n          return false;\n        }\n        return true;\n      });\n    }\n    clearFilters() {\n      this.filterForm.reset();\n      this.filteredProjects = this.projects;\n    }\n    deleteProject(projectId) {\n      if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n        this.projectService.delete(projectId).subscribe({\n          next: () => {\n            this.projects = this.projects.filter(p => p.id !== projectId);\n            this.applyFilters();\n            this.snackBar.open('Projet supprimé avec succès', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n          },\n          error: error => {\n            this.snackBar.open('Erreur lors de la suppression', 'Fermer', {\n              duration: 3000,\n              panelClass: ['error-snackbar']\n            });\n            console.error('Error deleting project:', error);\n          }\n        });\n      }\n    }\n    getWorkModeLabel(workMode) {\n      const option = this.workModeOptions.find(opt => opt.value === workMode);\n      return option ? option.label : workMode;\n    }\n    getProjectStatusColor(project) {\n      if (project.startDate) {\n        const startDate = new Date(project.startDate);\n        const now = new Date();\n        if (startDate > now) {\n          return 'accent'; // Future project\n        } else if (project.durationInMonths) {\n          const endDate = new Date(startDate);\n          endDate.setMonth(endDate.getMonth() + project.durationInMonths);\n          if (endDate > now) {\n            return 'primary'; // Active project\n          } else {\n            return 'warn'; // Completed project\n          }\n        }\n      }\n      return 'primary';\n    }\n    getProjectStatusText(project) {\n      if (project.startDate) {\n        const startDate = new Date(project.startDate);\n        const now = new Date();\n        if (startDate > now) {\n          return 'À venir';\n        } else if (project.durationInMonths) {\n          const endDate = new Date(startDate);\n          endDate.setMonth(endDate.getMonth() + project.durationInMonths);\n          if (endDate > now) {\n            return 'En cours';\n          } else {\n            return 'Terminé';\n          }\n        }\n      }\n      return 'Statut inconnu';\n    }\n    static {\n      this.ɵfac = function ProjectListComponent_Factory(t) {\n        return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectListComponent,\n        selectors: [[\"app-project-list\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 48,\n        vars: 5,\n        consts: [[1, \"project-list-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\"], [1, \"filters-card\"], [1, \"filters-form\", 3, \"formGroup\"], [1, \"filter-row\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"minRate\", \"placeholder\", \"400\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"maxRate\", \"placeholder\", \"800\"], [\"formControlName\", \"workMode\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"techStack\", \"placeholder\", \"React, Angular...\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"clear-filters-btn\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"projects-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [1, \"projects-grid\"], [\"class\", \"project-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"project-card\"], [1, \"project-title\"], [1, \"project-client\"], [1, \"project-status\"], [\"selected\", \"\", 3, \"color\"], [1, \"project-details\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"class\", \"project-tech\", 4, \"ngIf\"], [\"class\", \"project-description\", 4, \"ngIf\"], [1, \"project-actions\"], [\"mat-button\", \"\", 3, \"routerLink\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"detail-item\"], [1, \"project-tech\"], [1, \"project-description\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"empty-icon\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\"]],\n        template: function ProjectListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"work\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6, \" Mes Projets \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"button\", 4)(8, \"mat-icon\");\n            i0.ɵɵtext(9, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Nouveau projet \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"mat-card\", 5)(12, \"mat-card-header\")(13, \"mat-card-title\");\n            i0.ɵɵtext(14, \"Filtres\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"form\", 6)(17, \"div\", 7)(18, \"mat-form-field\", 8)(19, \"mat-label\");\n            i0.ɵɵtext(20, \"TJM minimum\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"input\", 9);\n            i0.ɵɵelementStart(22, \"span\", 10);\n            i0.ɵɵtext(23, \"\\u20AC\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"mat-form-field\", 8)(25, \"mat-label\");\n            i0.ɵɵtext(26, \"TJM maximum\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(27, \"input\", 11);\n            i0.ɵɵelementStart(28, \"span\", 10);\n            i0.ɵɵtext(29, \"\\u20AC\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"mat-form-field\", 8)(31, \"mat-label\");\n            i0.ɵɵtext(32, \"Mode de travail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"mat-select\", 12)(34, \"mat-option\", 13);\n            i0.ɵɵtext(35, \"Tous\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(36, ProjectListComponent_mat_option_36_Template, 2, 2, \"mat-option\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"mat-form-field\", 8)(38, \"mat-label\");\n            i0.ɵɵtext(39, \"Technologies\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function ProjectListComponent_Template_button_click_41_listener() {\n              return ctx.clearFilters();\n            });\n            i0.ɵɵelementStart(42, \"mat-icon\");\n            i0.ɵɵtext(43, \"clear\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(44, \" Effacer \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(45, ProjectListComponent_div_45_Template, 4, 0, \"div\", 17)(46, ProjectListComponent_div_46_Template, 2, 1, \"div\", 18)(47, ProjectListComponent_div_47_Template, 12, 6, \"div\", 19);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n            i0.ɵɵadvance(20);\n            i0.ɵɵproperty(\"ngForOf\", ctx.workModeOptions);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredProjects.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredProjects.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DatePipe, RouterModule, i6.RouterLink, ReactiveFormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, MatCardModule, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, MatButtonModule, i8.MatButton, MatIconModule, i9.MatIcon, MatFormFieldModule, i10.MatFormField, i10.MatLabel, i10.MatSuffix, MatInputModule, i11.MatInput, MatSelectModule, i12.MatSelect, i13.MatOption, MatChipsModule, i14.MatChip, MatProgressSpinnerModule, i15.MatProgressSpinner, MatSnackBarModule],\n        styles: [\".project-list-container[_ngcontent-%COMP%]{padding:24px;max-width:1200px;margin:0 auto}.page-header[_ngcontent-%COMP%]{margin-bottom:24px}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.page-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:28px;font-weight:600;margin:0;color:#333}.page-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:32px;color:#667eea}.filters-card[_ngcontent-%COMP%]{margin-bottom:24px;border-radius:12px;box-shadow:0 2px 8px #0000001a}.filters-form[_ngcontent-%COMP%]{margin-top:16px}.filter-row[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:flex-end;flex-wrap:wrap}.filter-field[_ngcontent-%COMP%]{min-width:150px;flex:1}.clear-filters-btn[_ngcontent-%COMP%]{height:56px;min-width:120px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px}.loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:16px}.projects-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(350px,1fr));gap:20px}.project-card[_ngcontent-%COMP%]{border-radius:12px;box-shadow:0 2px 8px #0000001a;transition:transform .2s ease,box-shadow .2s ease}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #00000026}.project-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333;margin-bottom:4px}.project-client[_ngcontent-%COMP%]{color:#667eea;font-weight:500}.project-status[_ngcontent-%COMP%]{margin-left:auto}.project-details[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:12px;margin-bottom:16px}.detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:14px;color:#666}.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.project-tech[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px;padding:8px 12px;background-color:#f5f5f5;border-radius:8px;font-size:14px;color:#555}.project-tech[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#667eea}.project-description[_ngcontent-%COMP%]{font-size:14px;color:#666;line-height:1.5;margin:0;overflow:hidden;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}.project-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.empty-state[_ngcontent-%COMP%]{margin-top:40px}.empty-state[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{border-radius:12px;box-shadow:0 2px 8px #0000001a}.empty-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:#ccc;margin-bottom:16px}.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:500;margin:0 0 8px;color:#333}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin:0 0 24px}.empty-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px}@media (max-width: 768px){.project-list-container[_ngcontent-%COMP%]{padding:16px}.header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.page-title[_ngcontent-%COMP%]{font-size:24px}.filter-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filter-field[_ngcontent-%COMP%]{min-width:unset}.projects-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.project-details[_ngcontent-%COMP%]{flex-direction:column;gap:8px}}\"]\n      });\n    }\n  }\n  return ProjectListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}