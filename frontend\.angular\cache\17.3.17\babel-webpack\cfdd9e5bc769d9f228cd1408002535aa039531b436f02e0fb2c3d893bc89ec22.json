{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBarModule } from '@angular/material/snackbar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nlet ContactDetailComponent = class ContactDetailComponent {\n  constructor(contactService, router, route, snackBar) {\n    this.contactService = contactService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.contactId = +params['id'];\n        this.loadContact();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadContact() {\n    if (!this.contactId) return;\n    this.isLoading = true;\n    this.contactService.getContact(this.contactId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: contact => {\n        if (contact) {\n          this.contact = contact;\n        } else {\n          this.snackBar.open('Contact non trouvé', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/contacts']);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading contact:', error);\n        this.snackBar.open('Erreur lors du chargement du contact', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onEdit() {\n    if (this.contact) {\n      this.router.navigate(['/contacts', this.contact.id, 'edit']);\n    }\n  }\n  onDelete() {\n    if (!this.contact) return;\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le contact \"${this.contact.firstName} ${this.contact.lastName}\" ?`)) {\n      this.contactService.deleteContact(this.contact.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Contact supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/contacts']);\n        },\n        error: error => {\n          console.error('Error deleting contact:', error);\n          this.snackBar.open('Erreur lors de la suppression du contact', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  onBack() {\n    this.router.navigate(['/contacts']);\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      default:\n        return '';\n    }\n  }\n  getStatusLabel(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      default:\n        return status;\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  sendEmail() {\n    if (this.contact?.email) {\n      window.location.href = `mailto:${this.contact.email}`;\n    }\n  }\n  callPhone() {\n    if (this.contact?.phone) {\n      window.location.href = `tel:${this.contact.phone}`;\n    }\n  }\n  getFullName() {\n    if (!this.contact) return '';\n    return `${this.contact.firstName} ${this.contact.lastName}`;\n  }\n};\nContactDetailComponent = __decorate([Component({\n  selector: 'app-contact-detail',\n  standalone: true,\n  imports: [CommonModule, RouterModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatDividerModule, MatSnackBarModule, MatProgressSpinnerModule],\n  templateUrl: './contact-detail.component.html',\n  styleUrls: ['./contact-detail.component.scss']\n})], ContactDetailComponent);\nexport { ContactDetailComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatDividerModule", "MatSnackBarModule", "MatProgressSpinnerModule", "Subject", "takeUntil", "ContactDetailComponent", "constructor", "contactService", "router", "route", "snackBar", "isLoading", "destroy$", "ngOnInit", "params", "pipe", "subscribe", "contactId", "loadContact", "ngOnDestroy", "next", "complete", "getContact", "contact", "open", "duration", "navigate", "error", "console", "onEdit", "id", "onDelete", "confirm", "firstName", "lastName", "deleteContact", "onBack", "getStatusColor", "status", "getStatusLabel", "formatDate", "date", "Date", "toLocaleDateString", "year", "month", "day", "sendEmail", "email", "window", "location", "href", "callPhone", "phone", "getFullName", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\contacts\\contact-detail\\contact-detail.component.ts"], "sourcesContent": ["import { Compo<PERSON>, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snackbar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { ContactService, ContactDto } from '../../../services/contact.service';\n\n@Component({\n  selector: 'app-contact-detail',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatDividerModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './contact-detail.component.html',\n  styleUrls: ['./contact-detail.component.scss']\n})\nexport class ContactDetailComponent implements OnInit, OnDestroy {\n  contact?: ContactDto;\n  isLoading = false;\n  contactId?: number;\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private contactService: ContactService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.contactId = +params['id'];\n        this.loadContact();\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private loadContact(): void {\n    if (!this.contactId) return;\n    \n    this.isLoading = true;\n    this.contactService.getContact(this.contactId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (contact) => {\n          if (contact) {\n            this.contact = contact;\n          } else {\n            this.snackBar.open('Contact non trouvé', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/contacts']);\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading contact:', error);\n          this.snackBar.open('Erreur lors du chargement du contact', 'Fermer', { duration: 3000 });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onEdit(): void {\n    if (this.contact) {\n      this.router.navigate(['/contacts', this.contact.id, 'edit']);\n    }\n  }\n\n  onDelete(): void {\n    if (!this.contact) return;\n    \n    if (confirm(`Êtes-vous sûr de vouloir supprimer le contact \"${this.contact.firstName} ${this.contact.lastName}\" ?`)) {\n      this.contactService.deleteContact(this.contact.id)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Contact supprimé avec succès', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/contacts']);\n          },\n          error: (error) => {\n            console.error('Error deleting contact:', error);\n            this.snackBar.open('Erreur lors de la suppression du contact', 'Fermer', { duration: 3000 });\n          }\n        });\n    }\n  }\n\n  onBack(): void {\n    this.router.navigate(['/contacts']);\n  }\n\n  getStatusColor(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      default:\n        return '';\n    }\n  }\n\n  getStatusLabel(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      default:\n        return status;\n    }\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  sendEmail(): void {\n    if (this.contact?.email) {\n      window.location.href = `mailto:${this.contact.email}`;\n    }\n  }\n\n  callPhone(): void {\n    if (this.contact?.phone) {\n      window.location.href = `tel:${this.contact.phone}`;\n    }\n  }\n\n  getFullName(): string {\n    if (!this.contact) return '';\n    return `${this.contact.firstName} ${this.contact.lastName}`;\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgC,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAqB,4BAA4B;AAC3E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAqBlC,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAOjCC,YACUC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAHrB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,SAAS,GAAG,KAAK;IAGT,KAAAC,QAAQ,GAAG,IAAIT,OAAO,EAAQ;EAOnC;EAEHU,QAAQA,CAAA;IACN,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,IAAI,CAACX,SAAS,CAAC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAG;MAClE,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACG,SAAS,GAAG,CAACH,MAAM,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACI,WAAW,EAAE;;IAEtB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACpB,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE;EAC1B;EAEQH,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;IAErB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,cAAc,CAACe,UAAU,CAAC,IAAI,CAACL,SAAS,CAAC,CAC3CF,IAAI,CAACX,SAAS,CAAC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTI,IAAI,EAAGG,OAAO,IAAI;QAChB,IAAIA,OAAO,EAAE;UACX,IAAI,CAACA,OAAO,GAAGA,OAAO;SACvB,MAAM;UACL,IAAI,CAACb,QAAQ,CAACc,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACtE,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAErC,IAAI,CAACf,SAAS,GAAG,KAAK;MACxB,CAAC;MACDgB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACjB,QAAQ,CAACc,IAAI,CAAC,sCAAsC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACxF,IAAI,CAACd,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAkB,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACN,OAAO,EAAE;MAChB,IAAI,CAACf,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,EAAE,IAAI,CAACH,OAAO,CAACO,EAAE,EAAE,MAAM,CAAC,CAAC;;EAEhE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACR,OAAO,EAAE;IAEnB,IAAIS,OAAO,CAAC,kDAAkD,IAAI,CAACT,OAAO,CAACU,SAAS,IAAI,IAAI,CAACV,OAAO,CAACW,QAAQ,KAAK,CAAC,EAAE;MACnH,IAAI,CAAC3B,cAAc,CAAC4B,aAAa,CAAC,IAAI,CAACZ,OAAO,CAACO,EAAE,CAAC,CAC/Cf,IAAI,CAACX,SAAS,CAAC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;QACTI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACV,QAAQ,CAACc,IAAI,CAAC,8BAA8B,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAChF,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACjB,QAAQ,CAACc,IAAI,CAAC,0CAA0C,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9F;OACD,CAAC;;EAER;EAEAW,MAAMA,CAAA;IACJ,IAAI,CAAC5B,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAW,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,MAAM;MACf;QACE,OAAO,EAAE;;EAEf;EAEAC,cAAcA,CAACD,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAOA,MAAM;;EAEnB;EAEAE,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACxB,OAAO,EAAEyB,KAAK,EAAE;MACvBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU,IAAI,CAAC5B,OAAO,CAACyB,KAAK,EAAE;;EAEzD;EAEAI,SAASA,CAAA;IACP,IAAI,IAAI,CAAC7B,OAAO,EAAE8B,KAAK,EAAE;MACvBJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO,IAAI,CAAC5B,OAAO,CAAC8B,KAAK,EAAE;;EAEtD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC/B,OAAO,EAAE,OAAO,EAAE;IAC5B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACU,SAAS,IAAI,IAAI,CAACV,OAAO,CAACW,QAAQ,EAAE;EAC7D;CACD;AA/HY7B,sBAAsB,GAAAkD,UAAA,EAjBlC9D,SAAS,CAAC;EACT+D,QAAQ,EAAE,oBAAoB;EAC9BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhE,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,wBAAwB,CACzB;EACDyD,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,C,EACWvD,sBAAsB,CA+HlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}