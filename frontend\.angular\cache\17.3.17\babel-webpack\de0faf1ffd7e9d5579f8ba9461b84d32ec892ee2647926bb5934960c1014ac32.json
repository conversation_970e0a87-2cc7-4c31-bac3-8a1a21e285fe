{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProjectService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/projects`;\n  }\n  getAll() {\n    return this.http.get(this.API_URL);\n  }\n  getById(id) {\n    // Mock data for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        const mockProjects = [{\n          id: 1,\n          role: 'Développeur Full Stack',\n          clientName: 'TechCorp',\n          clientId: 1,\n          dailyRate: 600,\n          workMode: 'HYBRID',\n          remoteDaysPerMonth: 15,\n          onsiteDaysPerMonth: 5,\n          techStack: 'React, Node.js, PostgreSQL',\n          description: 'Développement d\\'une application web moderne pour la gestion des ressources humaines. Le projet inclut la création d\\'un système complet de gestion des employés, des congés, et des évaluations de performance.',\n          advantages: 'Équipe dynamique, technologies modernes, possibilité de télétravail',\n          startDate: '2024-01-15',\n          durationInMonths: 6,\n          orderRenewalInMonths: 3,\n          daysPerYear: 220,\n          documents: ['Contrat signé', 'Cahier des charges', 'Spécifications techniques'],\n          link: 'https://techcorp.com/project-portal',\n          personalRating: 4,\n          notes: 'Excellent projet avec une équipe très professionnelle. Technologies intéressantes et défis techniques stimulants.',\n          freelanceId: 1,\n          sourceName: 'LinkedIn',\n          sourceId: 1,\n          totalRevenue: 72000,\n          totalSteps: 5,\n          completedSteps: 3,\n          failedSteps: 0\n        }, {\n          id: 2,\n          role: 'Consultant Angular',\n          clientName: 'StartupInnovante',\n          clientId: 2,\n          dailyRate: 550,\n          workMode: 'REMOTE',\n          remoteDaysPerMonth: 20,\n          onsiteDaysPerMonth: 0,\n          techStack: 'Angular, TypeScript, Firebase',\n          description: 'Refonte complète de l\\'interface utilisateur d\\'une plateforme e-commerce. Migration d\\'une ancienne application vers Angular 17 avec une nouvelle architecture moderne.',\n          advantages: 'Flexibilité totale, startup innovante, stack technique moderne',\n          startDate: '2024-03-01',\n          durationInMonths: 4,\n          orderRenewalInMonths: 2,\n          daysPerYear: 200,\n          documents: ['Contrat freelance', 'NDA', 'Guide de style'],\n          link: 'https://startup-innovante.com/dev-portal',\n          personalRating: 5,\n          notes: 'Projet très enrichissant avec beaucoup d\\'autonomie. Équipe jeune et dynamique.',\n          freelanceId: 1,\n          sourceName: 'Recommandation',\n          sourceId: 2,\n          totalRevenue: 44000,\n          totalSteps: 4,\n          completedSteps: 4,\n          failedSteps: 0\n        }, {\n          id: 3,\n          role: 'Architecte Solution',\n          clientName: 'GrandGroupe',\n          clientId: 3,\n          dailyRate: 750,\n          workMode: 'ONSITE',\n          remoteDaysPerMonth: 0,\n          onsiteDaysPerMonth: 20,\n          techStack: 'Java, Spring Boot, Microservices',\n          description: 'Architecture et mise en place d\\'une solution microservices pour un système bancaire. Conception de l\\'architecture globale et accompagnement des équipes de développement.',\n          advantages: 'Projet d\\'envergure, équipe expérimentée, secteur bancaire stable',\n          startDate: '2024-06-01',\n          durationInMonths: 12,\n          orderRenewalInMonths: 6,\n          daysPerYear: 230,\n          documents: ['Contrat cadre', 'Habilitation bancaire', 'Architecture document'],\n          link: 'https://grandgroupe.com/contractor-portal',\n          personalRating: 3,\n          notes: 'Projet complexe avec beaucoup de contraintes réglementaires. Environnement très structuré.',\n          freelanceId: 1,\n          sourceName: 'Cabinet de recrutement',\n          sourceId: 3,\n          totalRevenue: 180000,\n          totalSteps: 6,\n          completedSteps: 2,\n          failedSteps: 1\n        }];\n        const project = mockProjects.find(p => p.id === id);\n        if (project) {\n          observer.next(project);\n        } else {\n          observer.error(new Error('Project not found'));\n        }\n        observer.complete();\n      }, 300);\n    });\n  }\n  getByIdWithSteps(id) {\n    return this.http.get(`${this.API_URL}/${id}/with-steps`);\n  }\n  getByFreelanceId(freelanceId) {\n    // Mock data for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        const mockProjects = [{\n          id: 1,\n          role: 'Développeur Full Stack',\n          clientName: 'TechCorp',\n          dailyRate: 600,\n          workMode: 'HYBRID',\n          techStack: 'React, Node.js, PostgreSQL',\n          description: 'Développement d\\'une application web moderne pour la gestion des ressources humaines.',\n          startDate: '2024-01-15',\n          durationInMonths: 6,\n          daysPerYear: 220\n        }, {\n          id: 2,\n          role: 'Consultant Angular',\n          clientName: 'StartupInnovante',\n          dailyRate: 550,\n          workMode: 'REMOTE',\n          techStack: 'Angular, TypeScript, Firebase',\n          description: 'Refonte complète de l\\'interface utilisateur d\\'une plateforme e-commerce.',\n          startDate: '2024-03-01',\n          durationInMonths: 4,\n          daysPerYear: 200\n        }, {\n          id: 3,\n          role: 'Architecte Solution',\n          clientName: 'GrandGroupe',\n          dailyRate: 750,\n          workMode: 'ONSITE',\n          techStack: 'Java, Spring Boot, Microservices',\n          description: 'Architecture et mise en place d\\'une solution microservices pour un système bancaire.',\n          startDate: '2024-06-01',\n          durationInMonths: 12,\n          daysPerYear: 230\n        }];\n        observer.next(mockProjects);\n        observer.complete();\n      }, 500);\n    });\n  }\n  getByClientId(clientId) {\n    return this.http.get(`${this.API_URL}/by-client/${clientId}`);\n  }\n  getByFreelanceIdWithFilters(freelanceId, filters) {\n    let params = new HttpParams();\n    if (filters.minRate !== undefined) {\n      params = params.set('minRate', filters.minRate.toString());\n    }\n    if (filters.maxRate !== undefined) {\n      params = params.set('maxRate', filters.maxRate.toString());\n    }\n    if (filters.workMode) {\n      params = params.set('workMode', filters.workMode);\n    }\n    if (filters.startDateAfter) {\n      params = params.set('startDateAfter', filters.startDateAfter);\n    }\n    if (filters.techStack) {\n      params = params.set('techStack', filters.techStack);\n    }\n    return this.http.get(`${this.API_URL}/by-freelance/${freelanceId}/filtered`, {\n      params\n    });\n  }\n  create(project) {\n    return this.http.post(this.API_URL, project);\n  }\n  update(id, project) {\n    return this.http.put(`${this.API_URL}/${id}`, project);\n  }\n  delete(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  getAverageDailyRate(freelanceId) {\n    return this.http.get(`${this.API_URL}/stats/average-rate/${freelanceId}`);\n  }\n  getProjectCount(freelanceId) {\n    return this.http.get(`${this.API_URL}/stats/count/${freelanceId}`);\n  }\n  static {\n    this.ɵfac = function ProjectService_Factory(t) {\n      return new (t || ProjectService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProjectService,\n      factory: ProjectService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "Observable", "environment", "ProjectService", "constructor", "http", "API_URL", "apiUrl", "getAll", "get", "getById", "id", "observer", "setTimeout", "mockProjects", "role", "clientName", "clientId", "dailyRate", "workMode", "remoteDaysPerMonth", "onsiteDaysPerMonth", "techStack", "description", "advantages", "startDate", "durationInMonths", "orderRenewalInMonths", "daysPerYear", "documents", "link", "personalRating", "notes", "freelanceId", "sourceName", "sourceId", "totalRevenue", "totalSteps", "completedSteps", "failedSteps", "project", "find", "p", "next", "error", "Error", "complete", "getByIdWithSteps", "getByFreelanceId", "getByClientId", "getByFreelanceIdWithFilters", "filters", "params", "minRate", "undefined", "set", "toString", "maxRate", "startDateAfter", "create", "post", "update", "put", "delete", "getAverageDailyRate", "getProjectCount", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\services\\project.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\nexport interface ProjectDto {\n  id?: number;\n  role: string;\n  description?: string;\n  techStack?: string;\n  dailyRate: number;\n  workMode?: 'REMOTE' | 'ONSITE' | 'HYBRID';\n  remoteDaysPerMonth?: number;\n  onsiteDaysPerMonth?: number;\n  advantages?: string;\n  startDate?: string;\n  durationInMonths?: number;\n  orderRenewalInMonths?: number;\n  daysPerYear?: number;\n  documents?: string[];\n  link?: string;\n  personalRating?: number;\n  notes?: string;\n  freelanceId?: number;\n  clientId?: number;\n  clientName?: string;\n  middlemanId?: number;\n  middlemanName?: string;\n  sourceId?: number;\n  sourceName?: string;\n  totalRevenue?: number;\n  totalSteps?: number;\n  completedSteps?: number;\n  failedSteps?: number;\n}\n\nexport interface ProjectFilters {\n  minRate?: number;\n  maxRate?: number;\n  workMode?: 'REMOTE' | 'ONSITE' | 'HYBRID';\n  startDateAfter?: string;\n  techStack?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProjectService {\n  private readonly API_URL = `${environment.apiUrl}/projects`;\n\n  constructor(private http: HttpClient) {}\n\n  getAll(): Observable<ProjectDto[]> {\n    return this.http.get<ProjectDto[]>(this.API_URL);\n  }\n\n  getById(id: number): Observable<ProjectDto> {\n    // Mock data for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        const mockProjects: ProjectDto[] = [\n          {\n            id: 1,\n            role: 'Développeur Full Stack',\n            clientName: 'TechCorp',\n            clientId: 1,\n            dailyRate: 600,\n            workMode: 'HYBRID',\n            remoteDaysPerMonth: 15,\n            onsiteDaysPerMonth: 5,\n            techStack: 'React, Node.js, PostgreSQL',\n            description: 'Développement d\\'une application web moderne pour la gestion des ressources humaines. Le projet inclut la création d\\'un système complet de gestion des employés, des congés, et des évaluations de performance.',\n            advantages: 'Équipe dynamique, technologies modernes, possibilité de télétravail',\n            startDate: '2024-01-15',\n            durationInMonths: 6,\n            orderRenewalInMonths: 3,\n            daysPerYear: 220,\n            documents: ['Contrat signé', 'Cahier des charges', 'Spécifications techniques'],\n            link: 'https://techcorp.com/project-portal',\n            personalRating: 4,\n            notes: 'Excellent projet avec une équipe très professionnelle. Technologies intéressantes et défis techniques stimulants.',\n            freelanceId: 1,\n            sourceName: 'LinkedIn',\n            sourceId: 1,\n            totalRevenue: 72000,\n            totalSteps: 5,\n            completedSteps: 3,\n            failedSteps: 0\n          },\n          {\n            id: 2,\n            role: 'Consultant Angular',\n            clientName: 'StartupInnovante',\n            clientId: 2,\n            dailyRate: 550,\n            workMode: 'REMOTE',\n            remoteDaysPerMonth: 20,\n            onsiteDaysPerMonth: 0,\n            techStack: 'Angular, TypeScript, Firebase',\n            description: 'Refonte complète de l\\'interface utilisateur d\\'une plateforme e-commerce. Migration d\\'une ancienne application vers Angular 17 avec une nouvelle architecture moderne.',\n            advantages: 'Flexibilité totale, startup innovante, stack technique moderne',\n            startDate: '2024-03-01',\n            durationInMonths: 4,\n            orderRenewalInMonths: 2,\n            daysPerYear: 200,\n            documents: ['Contrat freelance', 'NDA', 'Guide de style'],\n            link: 'https://startup-innovante.com/dev-portal',\n            personalRating: 5,\n            notes: 'Projet très enrichissant avec beaucoup d\\'autonomie. Équipe jeune et dynamique.',\n            freelanceId: 1,\n            sourceName: 'Recommandation',\n            sourceId: 2,\n            totalRevenue: 44000,\n            totalSteps: 4,\n            completedSteps: 4,\n            failedSteps: 0\n          },\n          {\n            id: 3,\n            role: 'Architecte Solution',\n            clientName: 'GrandGroupe',\n            clientId: 3,\n            dailyRate: 750,\n            workMode: 'ONSITE',\n            remoteDaysPerMonth: 0,\n            onsiteDaysPerMonth: 20,\n            techStack: 'Java, Spring Boot, Microservices',\n            description: 'Architecture et mise en place d\\'une solution microservices pour un système bancaire. Conception de l\\'architecture globale et accompagnement des équipes de développement.',\n            advantages: 'Projet d\\'envergure, équipe expérimentée, secteur bancaire stable',\n            startDate: '2024-06-01',\n            durationInMonths: 12,\n            orderRenewalInMonths: 6,\n            daysPerYear: 230,\n            documents: ['Contrat cadre', 'Habilitation bancaire', 'Architecture document'],\n            link: 'https://grandgroupe.com/contractor-portal',\n            personalRating: 3,\n            notes: 'Projet complexe avec beaucoup de contraintes réglementaires. Environnement très structuré.',\n            freelanceId: 1,\n            sourceName: 'Cabinet de recrutement',\n            sourceId: 3,\n            totalRevenue: 180000,\n            totalSteps: 6,\n            completedSteps: 2,\n            failedSteps: 1\n          }\n        ];\n\n        const project = mockProjects.find(p => p.id === id);\n        if (project) {\n          observer.next(project);\n        } else {\n          observer.error(new Error('Project not found'));\n        }\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  getByIdWithSteps(id: number): Observable<ProjectDto> {\n    return this.http.get<ProjectDto>(`${this.API_URL}/${id}/with-steps`);\n  }\n\n  getByFreelanceId(freelanceId: number): Observable<ProjectDto[]> {\n    // Mock data for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        const mockProjects: ProjectDto[] = [\n          {\n            id: 1,\n            role: 'Développeur Full Stack',\n            clientName: 'TechCorp',\n            dailyRate: 600,\n            workMode: 'HYBRID',\n            techStack: 'React, Node.js, PostgreSQL',\n            description: 'Développement d\\'une application web moderne pour la gestion des ressources humaines.',\n            startDate: '2024-01-15',\n            durationInMonths: 6,\n            daysPerYear: 220\n          },\n          {\n            id: 2,\n            role: 'Consultant Angular',\n            clientName: 'StartupInnovante',\n            dailyRate: 550,\n            workMode: 'REMOTE',\n            techStack: 'Angular, TypeScript, Firebase',\n            description: 'Refonte complète de l\\'interface utilisateur d\\'une plateforme e-commerce.',\n            startDate: '2024-03-01',\n            durationInMonths: 4,\n            daysPerYear: 200\n          },\n          {\n            id: 3,\n            role: 'Architecte Solution',\n            clientName: 'GrandGroupe',\n            dailyRate: 750,\n            workMode: 'ONSITE',\n            techStack: 'Java, Spring Boot, Microservices',\n            description: 'Architecture et mise en place d\\'une solution microservices pour un système bancaire.',\n            startDate: '2024-06-01',\n            durationInMonths: 12,\n            daysPerYear: 230\n          }\n        ];\n        observer.next(mockProjects);\n        observer.complete();\n      }, 500);\n    });\n  }\n\n  getByClientId(clientId: number): Observable<ProjectDto[]> {\n    return this.http.get<ProjectDto[]>(`${this.API_URL}/by-client/${clientId}`);\n  }\n\n  getByFreelanceIdWithFilters(freelanceId: number, filters: ProjectFilters): Observable<ProjectDto[]> {\n    let params = new HttpParams();\n    \n    if (filters.minRate !== undefined) {\n      params = params.set('minRate', filters.minRate.toString());\n    }\n    if (filters.maxRate !== undefined) {\n      params = params.set('maxRate', filters.maxRate.toString());\n    }\n    if (filters.workMode) {\n      params = params.set('workMode', filters.workMode);\n    }\n    if (filters.startDateAfter) {\n      params = params.set('startDateAfter', filters.startDateAfter);\n    }\n    if (filters.techStack) {\n      params = params.set('techStack', filters.techStack);\n    }\n\n    return this.http.get<ProjectDto[]>(`${this.API_URL}/by-freelance/${freelanceId}/filtered`, { params });\n  }\n\n  create(project: ProjectDto): Observable<ProjectDto> {\n    return this.http.post<ProjectDto>(this.API_URL, project);\n  }\n\n  update(id: number, project: ProjectDto): Observable<ProjectDto> {\n    return this.http.put<ProjectDto>(`${this.API_URL}/${id}`, project);\n  }\n\n  delete(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  getAverageDailyRate(freelanceId: number): Observable<number> {\n    return this.http.get<number>(`${this.API_URL}/stats/average-rate/${freelanceId}`);\n  }\n\n  getProjectCount(freelanceId: number): Observable<number> {\n    return this.http.get<number>(`${this.API_URL}/stats/count/${freelanceId}`);\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,WAAW,QAAQ,gCAAgC;;;AA4C5D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,GAAGJ,WAAW,CAACK,MAAM,WAAW;EAEpB;EAEvCC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAe,IAAI,CAACH,OAAO,CAAC;EAClD;EAEAI,OAAOA,CAACC,EAAU;IAChB;IACA,OAAO,IAAIV,UAAU,CAACW,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACd,MAAMC,YAAY,GAAiB,CACjC;UACEH,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,wBAAwB;UAC9BC,UAAU,EAAE,UAAU;UACtBC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE,QAAQ;UAClBC,kBAAkB,EAAE,EAAE;UACtBC,kBAAkB,EAAE,CAAC;UACrBC,SAAS,EAAE,4BAA4B;UACvCC,WAAW,EAAE,kNAAkN;UAC/NC,UAAU,EAAE,qEAAqE;UACjFC,SAAS,EAAE,YAAY;UACvBC,gBAAgB,EAAE,CAAC;UACnBC,oBAAoB,EAAE,CAAC;UACvBC,WAAW,EAAE,GAAG;UAChBC,SAAS,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,2BAA2B,CAAC;UAC/EC,IAAI,EAAE,qCAAqC;UAC3CC,cAAc,EAAE,CAAC;UACjBC,KAAK,EAAE,mHAAmH;UAC1HC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,UAAU;UACtBC,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,CAAC;UACbC,cAAc,EAAE,CAAC;UACjBC,WAAW,EAAE;SACd,EACD;UACE5B,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,oBAAoB;UAC1BC,UAAU,EAAE,kBAAkB;UAC9BC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE,QAAQ;UAClBC,kBAAkB,EAAE,EAAE;UACtBC,kBAAkB,EAAE,CAAC;UACrBC,SAAS,EAAE,+BAA+B;UAC1CC,WAAW,EAAE,0KAA0K;UACvLC,UAAU,EAAE,gEAAgE;UAC5EC,SAAS,EAAE,YAAY;UACvBC,gBAAgB,EAAE,CAAC;UACnBC,oBAAoB,EAAE,CAAC;UACvBC,WAAW,EAAE,GAAG;UAChBC,SAAS,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,gBAAgB,CAAC;UACzDC,IAAI,EAAE,0CAA0C;UAChDC,cAAc,EAAE,CAAC;UACjBC,KAAK,EAAE,iFAAiF;UACxFC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,gBAAgB;UAC5BC,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,CAAC;UACbC,cAAc,EAAE,CAAC;UACjBC,WAAW,EAAE;SACd,EACD;UACE5B,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,qBAAqB;UAC3BC,UAAU,EAAE,aAAa;UACzBC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE,QAAQ;UAClBC,kBAAkB,EAAE,CAAC;UACrBC,kBAAkB,EAAE,EAAE;UACtBC,SAAS,EAAE,kCAAkC;UAC7CC,WAAW,EAAE,6KAA6K;UAC1LC,UAAU,EAAE,mEAAmE;UAC/EC,SAAS,EAAE,YAAY;UACvBC,gBAAgB,EAAE,EAAE;UACpBC,oBAAoB,EAAE,CAAC;UACvBC,WAAW,EAAE,GAAG;UAChBC,SAAS,EAAE,CAAC,eAAe,EAAE,uBAAuB,EAAE,uBAAuB,CAAC;UAC9EC,IAAI,EAAE,2CAA2C;UACjDC,cAAc,EAAE,CAAC;UACjBC,KAAK,EAAE,4FAA4F;UACnGC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,wBAAwB;UACpCC,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,CAAC;UACbC,cAAc,EAAE,CAAC;UACjBC,WAAW,EAAE;SACd,CACF;QAED,MAAMC,OAAO,GAAG1B,YAAY,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAKA,EAAE,CAAC;QACnD,IAAI6B,OAAO,EAAE;UACX5B,QAAQ,CAAC+B,IAAI,CAACH,OAAO,CAAC;SACvB,MAAM;UACL5B,QAAQ,CAACgC,KAAK,CAAC,IAAIC,KAAK,CAAC,mBAAmB,CAAC,CAAC;;QAEhDjC,QAAQ,CAACkC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAACpC,EAAU;IACzB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAa,GAAG,IAAI,CAACH,OAAO,IAAIK,EAAE,aAAa,CAAC;EACtE;EAEAqC,gBAAgBA,CAACf,WAAmB;IAClC;IACA,OAAO,IAAIhC,UAAU,CAACW,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACd,MAAMC,YAAY,GAAiB,CACjC;UACEH,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,wBAAwB;UAC9BC,UAAU,EAAE,UAAU;UACtBE,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE,QAAQ;UAClBG,SAAS,EAAE,4BAA4B;UACvCC,WAAW,EAAE,uFAAuF;UACpGE,SAAS,EAAE,YAAY;UACvBC,gBAAgB,EAAE,CAAC;UACnBE,WAAW,EAAE;SACd,EACD;UACEjB,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,oBAAoB;UAC1BC,UAAU,EAAE,kBAAkB;UAC9BE,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE,QAAQ;UAClBG,SAAS,EAAE,+BAA+B;UAC1CC,WAAW,EAAE,4EAA4E;UACzFE,SAAS,EAAE,YAAY;UACvBC,gBAAgB,EAAE,CAAC;UACnBE,WAAW,EAAE;SACd,EACD;UACEjB,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,qBAAqB;UAC3BC,UAAU,EAAE,aAAa;UACzBE,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE,QAAQ;UAClBG,SAAS,EAAE,kCAAkC;UAC7CC,WAAW,EAAE,uFAAuF;UACpGE,SAAS,EAAE,YAAY;UACvBC,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAE;SACd,CACF;QACDhB,QAAQ,CAAC+B,IAAI,CAAC7B,YAAY,CAAC;QAC3BF,QAAQ,CAACkC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEAG,aAAaA,CAAChC,QAAgB;IAC5B,OAAO,IAAI,CAACZ,IAAI,CAACI,GAAG,CAAe,GAAG,IAAI,CAACH,OAAO,cAAcW,QAAQ,EAAE,CAAC;EAC7E;EAEAiC,2BAA2BA,CAACjB,WAAmB,EAAEkB,OAAuB;IACtE,IAAIC,MAAM,GAAG,IAAIpD,UAAU,EAAE;IAE7B,IAAImD,OAAO,CAACE,OAAO,KAAKC,SAAS,EAAE;MACjCF,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,SAAS,EAAEJ,OAAO,CAACE,OAAO,CAACG,QAAQ,EAAE,CAAC;;IAE5D,IAAIL,OAAO,CAACM,OAAO,KAAKH,SAAS,EAAE;MACjCF,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,SAAS,EAAEJ,OAAO,CAACM,OAAO,CAACD,QAAQ,EAAE,CAAC;;IAE5D,IAAIL,OAAO,CAAChC,QAAQ,EAAE;MACpBiC,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,UAAU,EAAEJ,OAAO,CAAChC,QAAQ,CAAC;;IAEnD,IAAIgC,OAAO,CAACO,cAAc,EAAE;MAC1BN,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,gBAAgB,EAAEJ,OAAO,CAACO,cAAc,CAAC;;IAE/D,IAAIP,OAAO,CAAC7B,SAAS,EAAE;MACrB8B,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,WAAW,EAAEJ,OAAO,CAAC7B,SAAS,CAAC;;IAGrD,OAAO,IAAI,CAACjB,IAAI,CAACI,GAAG,CAAe,GAAG,IAAI,CAACH,OAAO,iBAAiB2B,WAAW,WAAW,EAAE;MAAEmB;IAAM,CAAE,CAAC;EACxG;EAEAO,MAAMA,CAACnB,OAAmB;IACxB,OAAO,IAAI,CAACnC,IAAI,CAACuD,IAAI,CAAa,IAAI,CAACtD,OAAO,EAAEkC,OAAO,CAAC;EAC1D;EAEAqB,MAAMA,CAAClD,EAAU,EAAE6B,OAAmB;IACpC,OAAO,IAAI,CAACnC,IAAI,CAACyD,GAAG,CAAa,GAAG,IAAI,CAACxD,OAAO,IAAIK,EAAE,EAAE,EAAE6B,OAAO,CAAC;EACpE;EAEAuB,MAAMA,CAACpD,EAAU;IACf,OAAO,IAAI,CAACN,IAAI,CAAC0D,MAAM,CAAO,GAAG,IAAI,CAACzD,OAAO,IAAIK,EAAE,EAAE,CAAC;EACxD;EAEAqD,mBAAmBA,CAAC/B,WAAmB;IACrC,OAAO,IAAI,CAAC5B,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,OAAO,uBAAuB2B,WAAW,EAAE,CAAC;EACnF;EAEAgC,eAAeA,CAAChC,WAAmB;IACjC,OAAO,IAAI,CAAC5B,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,OAAO,gBAAgB2B,WAAW,EAAE,CAAC;EAC5E;;;uBA/MW9B,cAAc,EAAA+D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdlE,cAAc;MAAAmE,OAAA,EAAdnE,cAAc,CAAAoE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}