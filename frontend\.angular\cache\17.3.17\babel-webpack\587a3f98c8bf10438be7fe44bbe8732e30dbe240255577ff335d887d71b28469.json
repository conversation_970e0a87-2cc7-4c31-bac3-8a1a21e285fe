{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/project.service\";\nimport * as i3 from \"../../../services/client.service\";\nimport * as i4 from \"../../../services/auth.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/select\";\nimport * as i14 from \"@angular/material/core\";\nimport * as i15 from \"@angular/material/datepicker\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nfunction ProjectFormComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement du projet...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectFormComponent_form_10_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", client_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", client_r3.name, \" \");\n  }\n}\nfunction ProjectFormComponent_form_10_mat_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const mode_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", mode_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", mode_r4.label, \" \");\n  }\n}\nfunction ProjectFormComponent_form_10_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-form-field\", 20)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Jours t\\u00E9l\\u00E9travail/mois\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-form-field\", 20)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Jours sur site/mois\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 38);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectFormComponent_form_10_mat_option_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rating_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", rating_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", rating_r5.label, \" \");\n  }\n}\nfunction ProjectFormComponent_form_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 12);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectFormComponent_form_10_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"h3\");\n    i0.ɵɵtext(4, \"Informations g\\u00E9n\\u00E9rales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 15)(6, \"mat-form-field\", 16)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"R\\u00F4le / Poste *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 17);\n    i0.ɵɵelementStart(10, \"mat-error\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 15)(13, \"mat-form-field\", 16)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"textarea\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 15)(18, \"mat-form-field\", 16)(19, \"mat-label\");\n    i0.ɵɵtext(20, \"Stack technique\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 15)(23, \"mat-form-field\", 20)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-select\", 21);\n    i0.ɵɵtemplate(27, ProjectFormComponent_form_10_mat_option_27_Template, 2, 2, \"mat-option\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"mat-form-field\", 20)(29, \"mat-label\");\n    i0.ɵɵtext(30, \"TJM *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 23);\n    i0.ɵɵelementStart(32, \"span\", 24);\n    i0.ɵɵtext(33, \"\\u20AC\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"mat-error\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 14)(37, \"h3\");\n    i0.ɵɵtext(38, \"Mode de travail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 15)(40, \"mat-form-field\", 16)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"Mode de travail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-select\", 25);\n    i0.ɵɵtemplate(44, ProjectFormComponent_form_10_mat_option_44_Template, 2, 2, \"mat-option\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(45, ProjectFormComponent_form_10_div_45_Template, 9, 0, \"div\", 26);\n    i0.ɵɵelementStart(46, \"div\", 15)(47, \"mat-form-field\", 16)(48, \"mat-label\");\n    i0.ɵɵtext(49, \"Avantages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(50, \"textarea\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 14)(52, \"h3\");\n    i0.ɵɵtext(53, \"Planning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 15)(55, \"mat-form-field\", 20)(56, \"mat-label\");\n    i0.ɵɵtext(57, \"Date de d\\u00E9but\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(58, \"input\", 28)(59, \"mat-datepicker-toggle\", 29)(60, \"mat-datepicker\", null, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"mat-form-field\", 20)(63, \"mat-label\");\n    i0.ɵɵtext(64, \"Dur\\u00E9e (mois)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"input\", 30);\n    i0.ɵɵelementStart(66, \"mat-error\");\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 15)(69, \"mat-form-field\", 20)(70, \"mat-label\");\n    i0.ɵɵtext(71, \"Renouvellement (mois)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(72, \"input\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"mat-form-field\", 20)(74, \"mat-label\");\n    i0.ɵɵtext(75, \"Jours travaill\\u00E9s/an\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(76, \"input\", 32);\n    i0.ɵɵelementStart(77, \"mat-error\");\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(79, \"div\", 14)(80, \"h3\");\n    i0.ɵɵtext(81, \"Informations compl\\u00E9mentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"div\", 15)(83, \"mat-form-field\", 16)(84, \"mat-label\");\n    i0.ɵɵtext(85, \"Lien du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(86, \"input\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(87, \"div\", 15)(88, \"mat-form-field\", 16)(89, \"mat-label\");\n    i0.ɵɵtext(90, \"\\u00C9valuation personnelle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"mat-select\", 34);\n    i0.ɵɵtemplate(92, ProjectFormComponent_form_10_mat_option_92_Template, 2, 2, \"mat-option\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(93, \"div\", 15)(94, \"mat-form-field\", 16)(95, \"mat-label\");\n    i0.ɵɵtext(96, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(97, \"textarea\", 35);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const startDatePicker_r6 = i0.ɵɵreference(61);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.projectForm);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"role\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.clients);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"dailyRate\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.workModeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r1.projectForm.get(\"workMode\")) == null ? null : tmp_7_0.value) === \"HYBRID\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matDatepicker\", startDatePicker_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", startDatePicker_r6);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"durationInMonths\"));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"daysPerYear\"));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.ratingOptions);\n  }\n}\nfunction ProjectFormComponent_mat_spinner_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 39);\n  }\n}\nfunction ProjectFormComponent_mat_icon_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"save\" : \"add\");\n  }\n}\nexport class ProjectFormComponent {\n  constructor(fb, projectService, clientService, authService, router, route, snackBar) {\n    this.fb = fb;\n    this.projectService = projectService;\n    this.clientService = clientService;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.clients = [];\n    this.isLoading = false;\n    this.isSubmitting = false;\n    this.isEditMode = false;\n    this.workModeOptions = [{\n      value: 'REMOTE',\n      label: 'Télétravail'\n    }, {\n      value: 'ONSITE',\n      label: 'Sur site'\n    }, {\n      value: 'HYBRID',\n      label: 'Hybride'\n    }];\n    this.ratingOptions = [{\n      value: 1,\n      label: '1 - Très mauvais'\n    }, {\n      value: 2,\n      label: '2 - Mauvais'\n    }, {\n      value: 3,\n      label: '3 - Moyen'\n    }, {\n      value: 4,\n      label: '4 - Bon'\n    }, {\n      value: 5,\n      label: '5 - Excellent'\n    }];\n    this.destroy$ = new Subject();\n    this.projectForm = this.createForm();\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getUser();\n    this.loadClients();\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.projectId = +params['id'];\n        this.isEditMode = true;\n        this.loadProject();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createForm() {\n    return this.fb.group({\n      role: ['', [Validators.required, Validators.minLength(2)]],\n      description: [''],\n      techStack: [''],\n      dailyRate: ['', [Validators.required, Validators.min(1)]],\n      workMode: [''],\n      remoteDaysPerMonth: [''],\n      onsiteDaysPerMonth: [''],\n      advantages: [''],\n      startDate: [''],\n      durationInMonths: ['', [Validators.min(1)]],\n      orderRenewalInMonths: [''],\n      daysPerYear: ['', [Validators.min(1), Validators.max(365)]],\n      link: [''],\n      personalRating: [''],\n      notes: [''],\n      clientId: ['']\n    });\n  }\n  loadClients() {\n    this.clientService.getClients().pipe(takeUntil(this.destroy$)).subscribe({\n      next: clients => {\n        this.clients = clients.filter(client => client.status === 'ACTIVE');\n      },\n      error: error => {\n        console.error('Error loading clients:', error);\n        this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  loadProject() {\n    if (!this.projectId) return;\n    this.isLoading = true;\n    this.projectService.getById(this.projectId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: project => {\n        if (project) {\n          this.projectForm.patchValue({\n            role: project.role,\n            description: project.description,\n            techStack: project.techStack,\n            dailyRate: project.dailyRate,\n            workMode: project.workMode,\n            remoteDaysPerMonth: project.remoteDaysPerMonth,\n            onsiteDaysPerMonth: project.onsiteDaysPerMonth,\n            advantages: project.advantages,\n            startDate: project.startDate ? new Date(project.startDate) : null,\n            durationInMonths: project.durationInMonths,\n            orderRenewalInMonths: project.orderRenewalInMonths,\n            daysPerYear: project.daysPerYear,\n            link: project.link,\n            personalRating: project.personalRating,\n            notes: project.notes,\n            clientId: project.clientId\n          });\n        } else {\n          this.snackBar.open('Projet non trouvé', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/projects']);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading project:', error);\n        this.snackBar.open('Erreur lors du chargement du projet', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onSubmit() {\n    if (this.projectForm.valid && !this.isSubmitting) {\n      this.isSubmitting = true;\n      const formValue = this.projectForm.value;\n      // Find client name for the project\n      const selectedClient = this.clients.find(client => client.id === formValue.clientId);\n      const projectData = {\n        ...formValue,\n        clientName: selectedClient?.name || '',\n        freelanceId: this.currentUser?.id,\n        startDate: formValue.startDate ? formValue.startDate.toISOString().split('T')[0] : undefined\n      };\n      const operation = this.isEditMode ? this.projectService.update(this.projectId, projectData) : this.projectService.create(projectData);\n      operation.pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          const message = this.isEditMode ? 'Projet modifié avec succès' : 'Projet créé avec succès';\n          this.snackBar.open(message, 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/projects']);\n        },\n        error: error => {\n          console.error('Error saving project:', error);\n          const message = this.isEditMode ? 'Erreur lors de la modification du projet' : 'Erreur lors de la création du projet';\n          this.snackBar.open(message, 'Fermer', {\n            duration: 3000\n          });\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  onCancel() {\n    this.router.navigate(['/projects']);\n  }\n  markFormGroupTouched() {\n    Object.keys(this.projectForm.controls).forEach(key => {\n      const control = this.projectForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const control = this.projectForm.get(fieldName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return 'Ce champ est requis';\n      }\n      if (control.errors['minlength']) {\n        return `Minimum ${control.errors['minlength'].requiredLength} caractères`;\n      }\n      if (control.errors['min']) {\n        return `La valeur doit être supérieure à ${control.errors['min'].min}`;\n      }\n      if (control.errors['max']) {\n        return `La valeur doit être inférieure à ${control.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n  get pageTitle() {\n    return this.isEditMode ? 'Modifier le projet' : 'Nouveau projet';\n  }\n  get submitButtonText() {\n    return this.isEditMode ? 'Modifier' : 'Créer';\n  }\n  static {\n    this.ɵfac = function ProjectFormComponent_Factory(t) {\n      return new (t || ProjectFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.ClientService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectFormComponent,\n      selectors: [[\"app-project-form\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 8,\n      consts: [[\"startDatePicker\", \"\"], [1, \"project-form-container\"], [1, \"form-card\"], [1, \"header-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-container\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-grid\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"role\", \"placeholder\", \"Ex: D\\u00E9veloppeur Full Stack\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"Description d\\u00E9taill\\u00E9e du projet...\"], [\"matInput\", \"\", \"formControlName\", \"techStack\", \"placeholder\", \"Ex: React, Node.js, PostgreSQL\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"formControlName\", \"clientId\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"dailyRate\", \"placeholder\", \"600\"], [\"matSuffix\", \"\"], [\"formControlName\", \"workMode\"], [\"class\", \"form-row\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"advantages\", \"rows\", \"2\", \"placeholder\", \"Ex: \\u00C9quipe dynamique, technologies modernes...\"], [\"matInput\", \"\", \"formControlName\", \"startDate\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"durationInMonths\", \"placeholder\", \"6\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"orderRenewalInMonths\", \"placeholder\", \"3\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"daysPerYear\", \"placeholder\", \"220\"], [\"matInput\", \"\", \"formControlName\", \"link\", \"placeholder\", \"https://...\"], [\"formControlName\", \"personalRating\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"4\", \"placeholder\", \"Notes personnelles sur le projet...\"], [3, \"value\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"remoteDaysPerMonth\", \"placeholder\", \"15\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"onsiteDaysPerMonth\", \"placeholder\", \"5\"], [\"diameter\", \"20\"]],\n      template: function ProjectFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"mat-card\", 2)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"div\", 3)(5, \"mat-icon\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"mat-card-content\");\n          i0.ɵɵtemplate(9, ProjectFormComponent_div_9_Template, 4, 0, \"div\", 4)(10, ProjectFormComponent_form_10_Template, 98, 11, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"mat-card-actions\", 6)(12, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ProjectFormComponent_Template_button_click_12_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ProjectFormComponent_Template_button_click_16_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(17, ProjectFormComponent_mat_spinner_17_Template, 1, 0, \"mat-spinner\", 9)(18, ProjectFormComponent_mat_icon_18_Template, 2, 1, \"mat-icon\", 10);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.pageTitle, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.projectForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.submitButtonText, \" \");\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, RouterModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, MatButtonModule, i9.MatButton, MatIconModule, i10.MatIcon, MatInputModule, i11.MatInput, i12.MatFormField, i12.MatLabel, i12.MatError, i12.MatSuffix, MatFormFieldModule, MatSelectModule, i13.MatSelect, i14.MatOption, MatDatepickerModule, i15.MatDatepicker, i15.MatDatepickerInput, i15.MatDatepickerToggle, MatNativeDateModule, MatProgressSpinnerModule, i16.MatProgressSpinner],\n      styles: [\".project-form-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 32px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.8);\\n  border-bottom: 2px solid var(--mdc-theme-primary, #1976d2);\\n  padding-bottom: 8px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 100%;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n@media (max-width: 600px) {\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 12px;\\n  padding: 16px 24px;\\n  border-top: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 120px;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n}\\n.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n@media (max-width: 600px) {\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n.mat-mdc-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-mdc-form-field-flex[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\n.mat-mdc-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.12);\\n}\\n.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%] {\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.mat-mdc-form-field.mat-form-field-appearance-outline.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.mat-mdc-form-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.mat-mdc-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.mat-mdc-input-element[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.87);\\n}\\n.mat-mdc-input-element[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(0, 0, 0, 0.4);\\n}\\n\\n.mat-mdc-select[_ngcontent-%COMP%]   .mat-mdc-select-value[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.87);\\n}\\n.mat-mdc-select[_ngcontent-%COMP%]   .mat-mdc-select-placeholder[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.4);\\n}\\n\\n.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%] {\\n  background-color: var(--mdc-theme-primary, #1976d2);\\n  color: white;\\n}\\n.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]:disabled {\\n  background-color: rgba(0, 0, 0, 0.12);\\n  color: rgba(0, 0, 0, 0.26);\\n}\\n\\n.mat-mdc-button[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.mat-mdc-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n\\n.mat-mdc-form-field-error[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  font-size: 0.75rem;\\n  margin-top: 4px;\\n}\\n\\n.mat-datepicker-toggle[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.54);\\n}\\n\\n@media (max-width: 768px) {\\n  .project-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n    gap: 24px;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-bottom: 16px;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .project-form-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatProgressSpinnerModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "client_r3", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "mode_r4", "value", "label", "rating_r5", "ɵɵlistener", "ProjectFormComponent_form_10_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵtemplate", "ProjectFormComponent_form_10_mat_option_27_Template", "ProjectFormComponent_form_10_mat_option_44_Template", "ProjectFormComponent_form_10_div_45_Template", "ProjectFormComponent_form_10_mat_option_92_Template", "projectForm", "ɵɵtextInterpolate", "getFieldError", "clients", "workModeOptions", "tmp_7_0", "get", "startDatePicker_r6", "ratingOptions", "isEditMode", "ProjectFormComponent", "constructor", "fb", "projectService", "clientService", "authService", "router", "route", "snackBar", "isLoading", "isSubmitting", "destroy$", "createForm", "ngOnInit", "currentUser", "getUser", "loadClients", "params", "pipe", "subscribe", "projectId", "loadProject", "ngOnDestroy", "next", "complete", "group", "role", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "techStack", "dailyRate", "min", "workMode", "remoteDaysPerMonth", "onsiteDaysPerMonth", "advantages", "startDate", "durationInMonths", "orderRenewalInMonths", "daysPerYear", "max", "link", "personalRating", "notes", "clientId", "getClients", "filter", "client", "status", "error", "console", "open", "duration", "getById", "project", "patchValue", "Date", "navigate", "valid", "formValue", "selectedClient", "find", "projectData", "clientName", "freelanceId", "toISOString", "split", "undefined", "operation", "update", "create", "message", "markFormGroupTouched", "onCancel", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "<PERSON><PERSON><PERSON><PERSON>", "pageTitle", "submitButtonText", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProjectService", "i3", "ClientService", "i4", "AuthService", "i5", "Router", "ActivatedRoute", "i6", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProjectFormComponent_Template", "rf", "ctx", "ProjectFormComponent_div_9_Template", "ProjectFormComponent_form_10_Template", "ProjectFormComponent_Template_button_click_12_listener", "ProjectFormComponent_Template_button_click_16_listener", "ProjectFormComponent_mat_spinner_17_Template", "ProjectFormComponent_mat_icon_18_Template", "invalid", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i8", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i9", "MatButton", "i10", "MatIcon", "i11", "MatInput", "i12", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i13", "MatSelect", "i14", "MatOption", "i15", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i16", "MatProgressSpinner", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\project-form\\project-form.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\project-form\\project-form.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { ProjectService, ProjectDto } from '../../../services/project.service';\nimport { ClientService, ClientDto } from '../../../services/client.service';\nimport { AuthService } from '../../../services/auth.service';\n\n@Component({\n  selector: 'app-project-form',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './project-form.component.html',\n  styleUrls: ['./project-form.component.scss']\n})\nexport class ProjectFormComponent implements OnInit, OnDestroy {\n  projectForm: FormGroup;\n  clients: ClientDto[] = [];\n  isLoading = false;\n  isSubmitting = false;\n  isEditMode = false;\n  projectId?: number;\n  currentUser: any;\n\n  workModeOptions = [\n    { value: 'REMOTE', label: 'Télétravail' },\n    { value: 'ONSITE', label: 'Sur site' },\n    { value: 'HYBRID', label: 'Hybride' }\n  ];\n\n  ratingOptions = [\n    { value: 1, label: '1 - Très mauvais' },\n    { value: 2, label: '2 - Mauvais' },\n    { value: 3, label: '3 - Moyen' },\n    { value: 4, label: '4 - Bon' },\n    { value: 5, label: '5 - Excellent' }\n  ];\n\n  private readonly destroy$ = new Subject<void>();\n\n  constructor(\n    private readonly fb: FormBuilder,\n    private readonly projectService: ProjectService,\n    private readonly clientService: ClientService,\n    private readonly authService: AuthService,\n    private readonly router: Router,\n    private readonly route: ActivatedRoute,\n    private readonly snackBar: MatSnackBar\n  ) {\n    this.projectForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getUser();\n    this.loadClients();\n    \n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.projectId = +params['id'];\n        this.isEditMode = true;\n        this.loadProject();\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private createForm(): FormGroup {\n    return this.fb.group({\n      role: ['', [Validators.required, Validators.minLength(2)]],\n      description: [''],\n      techStack: [''],\n      dailyRate: ['', [Validators.required, Validators.min(1)]],\n      workMode: [''],\n      remoteDaysPerMonth: [''],\n      onsiteDaysPerMonth: [''],\n      advantages: [''],\n      startDate: [''],\n      durationInMonths: ['', [Validators.min(1)]],\n      orderRenewalInMonths: [''],\n      daysPerYear: ['', [Validators.min(1), Validators.max(365)]],\n      link: [''],\n      personalRating: [''],\n      notes: [''],\n      clientId: ['']\n    });\n  }\n\n  private loadClients(): void {\n    this.clientService.getClients()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (clients) => {\n          this.clients = clients.filter(client => client.status === 'ACTIVE');\n        },\n        error: (error) => {\n          console.error('Error loading clients:', error);\n          this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', { duration: 3000 });\n        }\n      });\n  }\n\n  private loadProject(): void {\n    if (!this.projectId) return;\n    \n    this.isLoading = true;\n    this.projectService.getById(this.projectId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (project) => {\n          if (project) {\n            this.projectForm.patchValue({\n              role: project.role,\n              description: project.description,\n              techStack: project.techStack,\n              dailyRate: project.dailyRate,\n              workMode: project.workMode,\n              remoteDaysPerMonth: project.remoteDaysPerMonth,\n              onsiteDaysPerMonth: project.onsiteDaysPerMonth,\n              advantages: project.advantages,\n              startDate: project.startDate ? new Date(project.startDate) : null,\n              durationInMonths: project.durationInMonths,\n              orderRenewalInMonths: project.orderRenewalInMonths,\n              daysPerYear: project.daysPerYear,\n              link: project.link,\n              personalRating: project.personalRating,\n              notes: project.notes,\n              clientId: project.clientId\n            });\n          } else {\n            this.snackBar.open('Projet non trouvé', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/projects']);\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading project:', error);\n          this.snackBar.open('Erreur lors du chargement du projet', 'Fermer', { duration: 3000 });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onSubmit(): void {\n    if (this.projectForm.valid && !this.isSubmitting) {\n      this.isSubmitting = true;\n      const formValue = this.projectForm.value;\n      \n      // Find client name for the project\n      const selectedClient = this.clients.find(client => client.id === formValue.clientId);\n      const projectData: ProjectDto = {\n        ...formValue,\n        clientName: selectedClient?.name || '',\n        freelanceId: this.currentUser?.id,\n        startDate: formValue.startDate ? formValue.startDate.toISOString().split('T')[0] : undefined\n      };\n\n      const operation = this.isEditMode\n        ? this.projectService.update(this.projectId!, projectData)\n        : this.projectService.create(projectData);\n\n      operation.pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          const message = this.isEditMode ? 'Projet modifié avec succès' : 'Projet créé avec succès';\n          this.snackBar.open(message, 'Fermer', { duration: 3000 });\n          this.router.navigate(['/projects']);\n        },\n        error: (error) => {\n          console.error('Error saving project:', error);\n          const message = this.isEditMode ? 'Erreur lors de la modification du projet' : 'Erreur lors de la création du projet';\n          this.snackBar.open(message, 'Fermer', { duration: 3000 });\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  onCancel(): void {\n    this.router.navigate(['/projects']);\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.projectForm.controls).forEach(key => {\n      const control = this.projectForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const control = this.projectForm.get(fieldName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return 'Ce champ est requis';\n      }\n      if (control.errors['minlength']) {\n        return `Minimum ${control.errors['minlength'].requiredLength} caractères`;\n      }\n      if (control.errors['min']) {\n        return `La valeur doit être supérieure à ${control.errors['min'].min}`;\n      }\n      if (control.errors['max']) {\n        return `La valeur doit être inférieure à ${control.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n\n  get pageTitle(): string {\n    return this.isEditMode ? 'Modifier le projet' : 'Nouveau projet';\n  }\n\n  get submitButtonText(): string {\n    return this.isEditMode ? 'Modifier' : 'Créer';\n  }\n}\n", "<div class=\"project-form-container\">\n  <mat-card class=\"form-card\">\n    <mat-card-header>\n      <mat-card-title>\n        <div class=\"header-content\">\n          <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>\n          {{ pageTitle }}\n        </div>\n      </mat-card-title>\n    </mat-card-header>\n\n    <mat-card-content>\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner></mat-spinner>\n        <p>Chargement du projet...</p>\n      </div>\n\n      <form [formGroup]=\"projectForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"!isLoading\">\n        <div class=\"form-grid\">\n          <!-- Basic Information Section -->\n          <div class=\"form-section\">\n            <h3>Informations générales</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Rôle / Poste *</mat-label>\n                <input matInput formControlName=\"role\" placeholder=\"Ex: Développeur Full Stack\">\n                <mat-error>{{ getFieldError('role') }}</mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Description</mat-label>\n                <textarea matInput formControlName=\"description\" rows=\"3\" \n                         placeholder=\"Description détaillée du projet...\"></textarea>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Stack technique</mat-label>\n                <input matInput formControlName=\"techStack\" \n                       placeholder=\"Ex: React, Node.js, PostgreSQL\">\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Client</mat-label>\n                <mat-select formControlName=\"clientId\">\n                  <mat-option *ngFor=\"let client of clients\" [value]=\"client.id\">\n                    {{ client.name }}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>TJM *</mat-label>\n                <input matInput type=\"number\" formControlName=\"dailyRate\" placeholder=\"600\">\n                <span matSuffix>€</span>\n                <mat-error>{{ getFieldError('dailyRate') }}</mat-error>\n              </mat-form-field>\n            </div>\n          </div>\n\n          <!-- Work Mode Section -->\n          <div class=\"form-section\">\n            <h3>Mode de travail</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mode de travail</mat-label>\n                <mat-select formControlName=\"workMode\">\n                  <mat-option *ngFor=\"let mode of workModeOptions\" [value]=\"mode.value\">\n                    {{ mode.label }}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\" *ngIf=\"projectForm.get('workMode')?.value === 'HYBRID'\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Jours télétravail/mois</mat-label>\n                <input matInput type=\"number\" formControlName=\"remoteDaysPerMonth\" placeholder=\"15\">\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Jours sur site/mois</mat-label>\n                <input matInput type=\"number\" formControlName=\"onsiteDaysPerMonth\" placeholder=\"5\">\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Avantages</mat-label>\n                <textarea matInput formControlName=\"advantages\" rows=\"2\" \n                         placeholder=\"Ex: Équipe dynamique, technologies modernes...\"></textarea>\n              </mat-form-field>\n            </div>\n          </div>\n\n          <!-- Timeline Section -->\n          <div class=\"form-section\">\n            <h3>Planning</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Date de début</mat-label>\n                <input matInput [matDatepicker]=\"startDatePicker\" formControlName=\"startDate\">\n                <mat-datepicker-toggle matSuffix [for]=\"startDatePicker\"></mat-datepicker-toggle>\n                <mat-datepicker #startDatePicker></mat-datepicker>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Durée (mois)</mat-label>\n                <input matInput type=\"number\" formControlName=\"durationInMonths\" placeholder=\"6\">\n                <mat-error>{{ getFieldError('durationInMonths') }}</mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Renouvellement (mois)</mat-label>\n                <input matInput type=\"number\" formControlName=\"orderRenewalInMonths\" placeholder=\"3\">\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Jours travaillés/an</mat-label>\n                <input matInput type=\"number\" formControlName=\"daysPerYear\" placeholder=\"220\">\n                <mat-error>{{ getFieldError('daysPerYear') }}</mat-error>\n              </mat-form-field>\n            </div>\n          </div>\n\n          <!-- Additional Information Section -->\n          <div class=\"form-section\">\n            <h3>Informations complémentaires</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Lien du projet</mat-label>\n                <input matInput formControlName=\"link\" placeholder=\"https://...\">\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Évaluation personnelle</mat-label>\n                <mat-select formControlName=\"personalRating\">\n                  <mat-option *ngFor=\"let rating of ratingOptions\" [value]=\"rating.value\">\n                    {{ rating.label }}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Notes</mat-label>\n                <textarea matInput formControlName=\"notes\" rows=\"4\" \n                         placeholder=\"Notes personnelles sur le projet...\"></textarea>\n              </mat-form-field>\n            </div>\n          </div>\n        </div>\n      </form>\n    </mat-card-content>\n\n    <mat-card-actions class=\"form-actions\">\n      <button mat-button type=\"button\" (click)=\"onCancel()\">\n        <mat-icon>cancel</mat-icon>\n        Annuler\n      </button>\n      <button mat-raised-button color=\"primary\" \n              [disabled]=\"projectForm.invalid || isSubmitting\"\n              (click)=\"onSubmit()\">\n        <mat-spinner *ngIf=\"isSubmitting\" diameter=\"20\"></mat-spinner>\n        <mat-icon *ngIf=\"!isSubmitting\">{{ isEditMode ? 'save' : 'add' }}</mat-icon>\n        {{ submitButtonText }}\n      </button>\n    </mat-card-actions>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgC,iBAAiB;AACtE,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICFnCC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAC5BH,EAD4B,CAAAI,YAAA,EAAI,EAC1B;;;;;IAoCMJ,EAAA,CAAAC,cAAA,qBAA+D;IAC7DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAa;;;;IAF8BJ,EAAA,CAAAK,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAmB;IAC5DP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAH,SAAA,CAAAI,IAAA,MACF;;;;;IAqBAV,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAa;;;;IAFoCJ,EAAA,CAAAK,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IACnEZ,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;IAOFb,EAFJ,CAAAC,cAAA,cAA8E,yBACpB,gBAC3C;IAAAD,EAAA,CAAAG,MAAA,uCAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC7CJ,EAAA,CAAAE,SAAA,gBAAoF;IACtFF,EAAA,CAAAI,YAAA,EAAiB;IAGfJ,EADF,CAAAC,cAAA,yBAAwD,gBAC3C;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC1CJ,EAAA,CAAAE,SAAA,gBAAmF;IAEvFF,EADE,CAAAI,YAAA,EAAiB,EACb;;;;;IA2DAJ,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAa;;;;IAFoCJ,EAAA,CAAAK,UAAA,UAAAS,SAAA,CAAAF,KAAA,CAAsB;IACrEZ,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAK,SAAA,CAAAD,KAAA,MACF;;;;;;IAvIZb,EAAA,CAAAC,cAAA,eAA2E;IAA3CD,EAAA,CAAAe,UAAA,sBAAAC,+DAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAYF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAIjDtB,EAHJ,CAAAC,cAAA,cAAuB,cAEK,SACpB;IAAAD,EAAA,CAAAG,MAAA,uCAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI3BJ,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,gBAC3C;IAAAD,EAAA,CAAAG,MAAA,0BAAc;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACrCJ,EAAA,CAAAE,SAAA,gBAAgF;IAChFF,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAE1CH,EAF0C,CAAAI,YAAA,EAAY,EACnC,EACb;IAIFJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAClCJ,EAAA,CAAAE,SAAA,oBACqE;IAEzEF,EADE,CAAAI,YAAA,EAAiB,EACb;IAIFJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACtCJ,EAAA,CAAAE,SAAA,iBACoD;IAExDF,EADE,CAAAI,YAAA,EAAiB,EACb;IAIFJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC7BJ,EAAA,CAAAC,cAAA,sBAAuC;IACrCD,EAAA,CAAAuB,UAAA,KAAAC,mDAAA,yBAA+D;IAInExB,EADE,CAAAI,YAAA,EAAa,EACE;IAGfJ,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5BJ,EAAA,CAAAE,SAAA,iBAA4E;IAC5EF,EAAA,CAAAC,cAAA,gBAAgB;IAAAD,EAAA,CAAAG,MAAA,cAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAGjDH,EAHiD,CAAAI,YAAA,EAAY,EACxC,EACb,EACF;IAIJJ,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIpBJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACtCJ,EAAA,CAAAC,cAAA,sBAAuC;IACrCD,EAAA,CAAAuB,UAAA,KAAAE,mDAAA,yBAAsE;IAK5EzB,EAFI,CAAAI,YAAA,EAAa,EACE,EACb;IAENJ,EAAA,CAAAuB,UAAA,KAAAG,4CAAA,kBAA8E;IAc1E1B,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAChCJ,EAAA,CAAAE,SAAA,oBACiF;IAGvFF,EAFI,CAAAI,YAAA,EAAiB,EACb,EACF;IAIJJ,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIbJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,0BAAa;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAGpCJ,EAFA,CAAAE,SAAA,iBAA8E,iCACG,+BAC/B;IACpDF,EAAA,CAAAI,YAAA,EAAiB;IAGfJ,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,yBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACnCJ,EAAA,CAAAE,SAAA,iBAAiF;IACjFF,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAEtDH,EAFsD,CAAAI,YAAA,EAAY,EAC/C,EACb;IAIFJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,6BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5CJ,EAAA,CAAAE,SAAA,iBAAqF;IACvFF,EAAA,CAAAI,YAAA,EAAiB;IAGfJ,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,gCAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC1CJ,EAAA,CAAAE,SAAA,iBAA8E;IAC9EF,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAG,MAAA,IAAkC;IAGnDH,EAHmD,CAAAI,YAAA,EAAY,EAC1C,EACb,EACF;IAIJJ,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAG,MAAA,yCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIjCJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACrCJ,EAAA,CAAAE,SAAA,iBAAiE;IAErEF,EADE,CAAAI,YAAA,EAAiB,EACb;IAIFJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,mCAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC7CJ,EAAA,CAAAC,cAAA,sBAA6C;IAC3CD,EAAA,CAAAuB,UAAA,KAAAI,mDAAA,yBAAwE;IAK9E3B,EAFI,CAAAI,YAAA,EAAa,EACE,EACb;IAIFJ,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5BJ,EAAA,CAAAE,SAAA,oBACsE;IAKhFF,EAJQ,CAAAI,YAAA,EAAiB,EACb,EACF,EACF,EACD;;;;;;IArJDJ,EAAA,CAAAK,UAAA,cAAAc,MAAA,CAAAS,WAAA,CAAyB;IAUV5B,EAAA,CAAAQ,SAAA,IAA2B;IAA3BR,EAAA,CAAA6B,iBAAA,CAAAV,MAAA,CAAAW,aAAA,SAA2B;IAwBL9B,EAAA,CAAAQ,SAAA,IAAU;IAAVR,EAAA,CAAAK,UAAA,YAAAc,MAAA,CAAAY,OAAA,CAAU;IAUhC/B,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAA6B,iBAAA,CAAAV,MAAA,CAAAW,aAAA,cAAgC;IAaZ9B,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAK,UAAA,YAAAc,MAAA,CAAAa,eAAA,CAAkB;IAO9BhC,EAAA,CAAAQ,SAAA,EAAqD;IAArDR,EAAA,CAAAK,UAAA,WAAA4B,OAAA,GAAAd,MAAA,CAAAS,WAAA,CAAAM,GAAA,+BAAAD,OAAA,CAAArB,KAAA,eAAqD;IA4BxDZ,EAAA,CAAAQ,SAAA,IAAiC;IAAjCR,EAAA,CAAAK,UAAA,kBAAA8B,kBAAA,CAAiC;IAChBnC,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAK,UAAA,QAAA8B,kBAAA,CAAuB;IAO7CnC,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAA6B,iBAAA,CAAAV,MAAA,CAAAW,aAAA,qBAAuC;IAavC9B,EAAA,CAAAQ,SAAA,IAAkC;IAAlCR,EAAA,CAAA6B,iBAAA,CAAAV,MAAA,CAAAW,aAAA,gBAAkC;IAoBZ9B,EAAA,CAAAQ,SAAA,IAAgB;IAAhBR,EAAA,CAAAK,UAAA,YAAAc,MAAA,CAAAiB,aAAA,CAAgB;;;;;IA2BzDpC,EAAA,CAAAE,SAAA,sBAA8D;;;;;IAC9DF,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IAA5CJ,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAA6B,iBAAA,CAAAV,MAAA,CAAAkB,UAAA,kBAAiC;;;AD1IzE,OAAM,MAAOC,oBAAoB;EAyB/BC,YACmBC,EAAe,EACfC,cAA8B,EAC9BC,aAA4B,EAC5BC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IANrB,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IA9B3B,KAAAf,OAAO,GAAgB,EAAE;IACzB,KAAAgB,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAX,UAAU,GAAG,KAAK;IAIlB,KAAAL,eAAe,GAAG,CAChB;MAAEpB,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAa,CAAE,EACzC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAU,CAAE,EACtC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAE,CACtC;IAED,KAAAuB,aAAa,GAAG,CACd;MAAExB,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACvC;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAa,CAAE,EAClC;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAW,CAAE,EAChC;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAE,EAC9B;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAe,CAAE,CACrC;IAEgB,KAAAoC,QAAQ,GAAG,IAAInD,OAAO,EAAQ;IAW7C,IAAI,CAAC8B,WAAW,GAAG,IAAI,CAACsB,UAAU,EAAE;EACtC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACT,WAAW,CAACU,OAAO,EAAE;IAC7C,IAAI,CAACC,WAAW,EAAE;IAElB,IAAI,CAACT,KAAK,CAACU,MAAM,CAACC,IAAI,CAACzD,SAAS,CAAC,IAAI,CAACkD,QAAQ,CAAC,CAAC,CAACQ,SAAS,CAACF,MAAM,IAAG;MAClE,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACG,SAAS,GAAG,CAACH,MAAM,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAClB,UAAU,GAAG,IAAI;QACtB,IAAI,CAACsB,WAAW,EAAE;;IAEtB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,QAAQ,CAACY,IAAI,EAAE;IACpB,IAAI,CAACZ,QAAQ,CAACa,QAAQ,EAAE;EAC1B;EAEQZ,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACV,EAAE,CAACuB,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC5E,UAAU,CAAC6E,QAAQ,EAAE7E,UAAU,CAAC8E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACjF,UAAU,CAAC6E,QAAQ,EAAE7E,UAAU,CAACkF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAACxF,UAAU,CAACkF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3CO,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC1F,UAAU,CAACkF,GAAG,CAAC,CAAC,CAAC,EAAElF,UAAU,CAAC2F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3DC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEQ7B,WAAWA,CAAA;IACjB,IAAI,CAACZ,aAAa,CAAC0C,UAAU,EAAE,CAC5B5B,IAAI,CAACzD,SAAS,CAAC,IAAI,CAACkD,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTI,IAAI,EAAG9B,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO,CAACsD,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,QAAQ,CAAC;MACrE,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAC1C,QAAQ,CAAC4C,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC3F;KACD,CAAC;EACN;EAEQhC,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;IAErB,IAAI,CAACX,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,cAAc,CAACmD,OAAO,CAAC,IAAI,CAAClC,SAAS,CAAC,CACxCF,IAAI,CAACzD,SAAS,CAAC,IAAI,CAACkD,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTI,IAAI,EAAGgC,OAAO,IAAI;QAChB,IAAIA,OAAO,EAAE;UACX,IAAI,CAACjE,WAAW,CAACkE,UAAU,CAAC;YAC1B9B,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;YAClBG,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;YAChCC,SAAS,EAAEyB,OAAO,CAACzB,SAAS;YAC5BC,SAAS,EAAEwB,OAAO,CAACxB,SAAS;YAC5BE,QAAQ,EAAEsB,OAAO,CAACtB,QAAQ;YAC1BC,kBAAkB,EAAEqB,OAAO,CAACrB,kBAAkB;YAC9CC,kBAAkB,EAAEoB,OAAO,CAACpB,kBAAkB;YAC9CC,UAAU,EAAEmB,OAAO,CAACnB,UAAU;YAC9BC,SAAS,EAAEkB,OAAO,CAAClB,SAAS,GAAG,IAAIoB,IAAI,CAACF,OAAO,CAAClB,SAAS,CAAC,GAAG,IAAI;YACjEC,gBAAgB,EAAEiB,OAAO,CAACjB,gBAAgB;YAC1CC,oBAAoB,EAAEgB,OAAO,CAAChB,oBAAoB;YAClDC,WAAW,EAAEe,OAAO,CAACf,WAAW;YAChCE,IAAI,EAAEa,OAAO,CAACb,IAAI;YAClBC,cAAc,EAAEY,OAAO,CAACZ,cAAc;YACtCC,KAAK,EAAEW,OAAO,CAACX,KAAK;YACpBC,QAAQ,EAAEU,OAAO,CAACV;WACnB,CAAC;SACH,MAAM;UACL,IAAI,CAACrC,QAAQ,CAAC4C,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACrE,IAAI,CAAC/C,MAAM,CAACoD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAErC,IAAI,CAACjD,SAAS,GAAG,KAAK;MACxB,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAC1C,QAAQ,CAAC4C,IAAI,CAAC,qCAAqC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAAC5C,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAzB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACM,WAAW,CAACqE,KAAK,IAAI,CAAC,IAAI,CAACjD,YAAY,EAAE;MAChD,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,MAAMkD,SAAS,GAAG,IAAI,CAACtE,WAAW,CAAChB,KAAK;MAExC;MACA,MAAMuF,cAAc,GAAG,IAAI,CAACpE,OAAO,CAACqE,IAAI,CAACd,MAAM,IAAIA,MAAM,CAAC/E,EAAE,KAAK2F,SAAS,CAACf,QAAQ,CAAC;MACpF,MAAMkB,WAAW,GAAe;QAC9B,GAAGH,SAAS;QACZI,UAAU,EAAEH,cAAc,EAAEzF,IAAI,IAAI,EAAE;QACtC6F,WAAW,EAAE,IAAI,CAACnD,WAAW,EAAE7C,EAAE;QACjCoE,SAAS,EAAEuB,SAAS,CAACvB,SAAS,GAAGuB,SAAS,CAACvB,SAAS,CAAC6B,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC;OACpF;MAED,MAAMC,SAAS,GAAG,IAAI,CAACtE,UAAU,GAC7B,IAAI,CAACI,cAAc,CAACmE,MAAM,CAAC,IAAI,CAAClD,SAAU,EAAE2C,WAAW,CAAC,GACxD,IAAI,CAAC5D,cAAc,CAACoE,MAAM,CAACR,WAAW,CAAC;MAE3CM,SAAS,CAACnD,IAAI,CAACzD,SAAS,CAAC,IAAI,CAACkD,QAAQ,CAAC,CAAC,CAACQ,SAAS,CAAC;QACjDI,IAAI,EAAEA,CAAA,KAAK;UACT,MAAMiD,OAAO,GAAG,IAAI,CAACzE,UAAU,GAAG,4BAA4B,GAAG,yBAAyB;UAC1F,IAAI,CAACS,QAAQ,CAAC4C,IAAI,CAACoB,OAAO,EAAE,QAAQ,EAAE;YAAEnB,QAAQ,EAAE;UAAI,CAAE,CAAC;UACzD,IAAI,CAAC/C,MAAM,CAACoD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACDR,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C,MAAMsB,OAAO,GAAG,IAAI,CAACzE,UAAU,GAAG,0CAA0C,GAAG,sCAAsC;UACrH,IAAI,CAACS,QAAQ,CAAC4C,IAAI,CAACoB,OAAO,EAAE,QAAQ,EAAE;YAAEnB,QAAQ,EAAE;UAAI,CAAE,CAAC;UACzD,IAAI,CAAC3C,YAAY,GAAG,KAAK;QAC3B;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC+D,oBAAoB,EAAE;;EAE/B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACpE,MAAM,CAACoD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEQe,oBAAoBA,CAAA;IAC1BE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtF,WAAW,CAACuF,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAAC1F,WAAW,CAACM,GAAG,CAACmF,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAzF,aAAaA,CAAC0F,SAAiB;IAC7B,MAAMF,OAAO,GAAG,IAAI,CAAC1F,WAAW,CAACM,GAAG,CAACsF,SAAS,CAAC;IAC/C,IAAIF,OAAO,EAAEG,MAAM,IAAIH,OAAO,CAACI,OAAO,EAAE;MACtC,IAAIJ,OAAO,CAACG,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,qBAAqB;;MAE9B,IAAIH,OAAO,CAACG,MAAM,CAAC,WAAW,CAAC,EAAE;QAC/B,OAAO,WAAWH,OAAO,CAACG,MAAM,CAAC,WAAW,CAAC,CAACE,cAAc,aAAa;;MAE3E,IAAIL,OAAO,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,oCAAoCH,OAAO,CAACG,MAAM,CAAC,KAAK,CAAC,CAACnD,GAAG,EAAE;;MAExE,IAAIgD,OAAO,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,oCAAoCH,OAAO,CAACG,MAAM,CAAC,KAAK,CAAC,CAAC1C,GAAG,EAAE;;;IAG1E,OAAO,EAAE;EACX;EAEA,IAAI6C,SAASA,CAAA;IACX,OAAO,IAAI,CAACvF,UAAU,GAAG,oBAAoB,GAAG,gBAAgB;EAClE;EAEA,IAAIwF,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACxF,UAAU,GAAG,UAAU,GAAG,OAAO;EAC/C;;;uBA3MWC,oBAAoB,EAAAtC,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAApI,EAAA,CAAA8H,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAtI,EAAA,CAAA8H,iBAAA,CAAAS,EAAA,CAAAC,MAAA,GAAAxI,EAAA,CAAA8H,iBAAA,CAAAS,EAAA,CAAAE,cAAA,GAAAzI,EAAA,CAAA8H,iBAAA,CAAAY,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBrG,oBAAoB;MAAAsG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9I,EAAA,CAAA+I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnCvBrJ,EALV,CAAAC,cAAA,aAAoC,kBACN,sBACT,qBACC,aACc,eAChB;UAAAD,EAAA,CAAAG,MAAA,GAAiC;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACtDJ,EAAA,CAAAG,MAAA,GACF;UAEJH,EAFI,CAAAI,YAAA,EAAM,EACS,EACD;UAElBJ,EAAA,CAAAC,cAAA,uBAAkB;UAMhBD,EALA,CAAAuB,UAAA,IAAAgI,mCAAA,iBAAiD,KAAAC,qCAAA,oBAK0B;UAsJ7ExJ,EAAA,CAAAI,YAAA,EAAmB;UAGjBJ,EADF,CAAAC,cAAA,2BAAuC,iBACiB;UAArBD,EAAA,CAAAe,UAAA,mBAAA0I,uDAAA;YAAA,OAASH,GAAA,CAAAtC,QAAA,EAAU;UAAA,EAAC;UACnDhH,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC3BJ,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,iBAE6B;UAArBD,EAAA,CAAAe,UAAA,mBAAA2I,uDAAA;YAAA,OAASJ,GAAA,CAAAhI,QAAA,EAAU;UAAA,EAAC;UAE1BtB,EADA,CAAAuB,UAAA,KAAAoI,4CAAA,yBAAgD,KAAAC,yCAAA,uBAChB;UAChC5J,EAAA,CAAAG,MAAA,IACF;UAGNH,EAHM,CAAAI,YAAA,EAAS,EACQ,EACV,EACP;;;UAlLcJ,EAAA,CAAAQ,SAAA,GAAiC;UAAjCR,EAAA,CAAA6B,iBAAA,CAAAyH,GAAA,CAAAjH,UAAA,kBAAiC;UAC3CrC,EAAA,CAAAQ,SAAA,EACF;UADER,EAAA,CAAAS,kBAAA,MAAA6I,GAAA,CAAA1B,SAAA,MACF;UAKI5H,EAAA,CAAAQ,SAAA,GAAe;UAAfR,EAAA,CAAAK,UAAA,SAAAiJ,GAAA,CAAAvG,SAAA,CAAe;UAKoC/C,EAAA,CAAAQ,SAAA,EAAgB;UAAhBR,EAAA,CAAAK,UAAA,UAAAiJ,GAAA,CAAAvG,SAAA,CAAgB;UA8JjE/C,EAAA,CAAAQ,SAAA,GAAgD;UAAhDR,EAAA,CAAAK,UAAA,aAAAiJ,GAAA,CAAA1H,WAAA,CAAAiI,OAAA,IAAAP,GAAA,CAAAtG,YAAA,CAAgD;UAExChD,EAAA,CAAAQ,SAAA,EAAkB;UAAlBR,EAAA,CAAAK,UAAA,SAAAiJ,GAAA,CAAAtG,YAAA,CAAkB;UACrBhD,EAAA,CAAAQ,SAAA,EAAmB;UAAnBR,EAAA,CAAAK,UAAA,UAAAiJ,GAAA,CAAAtG,YAAA,CAAmB;UAC9BhD,EAAA,CAAAQ,SAAA,EACF;UADER,EAAA,CAAAS,kBAAA,MAAA6I,GAAA,CAAAzB,gBAAA,MACF;;;qBD5JF5I,YAAY,EAAA6K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9K,YAAY,EACZC,mBAAmB,EAAA4I,EAAA,CAAAkC,aAAA,EAAAlC,EAAA,CAAAmC,oBAAA,EAAAnC,EAAA,CAAAoC,mBAAA,EAAApC,EAAA,CAAAqC,eAAA,EAAArC,EAAA,CAAAsC,oBAAA,EAAAtC,EAAA,CAAAuC,kBAAA,EAAAvC,EAAA,CAAAwC,eAAA,EACnBlL,aAAa,EAAAmL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,YAAA,EACbvL,eAAe,EAAAwL,EAAA,CAAAC,SAAA,EACfxL,aAAa,EAAAyL,GAAA,CAAAC,OAAA,EACbzL,cAAc,EAAA0L,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACd/L,kBAAkB,EAClBC,eAAe,EAAA+L,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfjM,mBAAmB,EAAAkM,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnBpM,mBAAmB,EACnBC,wBAAwB,EAAAoM,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}