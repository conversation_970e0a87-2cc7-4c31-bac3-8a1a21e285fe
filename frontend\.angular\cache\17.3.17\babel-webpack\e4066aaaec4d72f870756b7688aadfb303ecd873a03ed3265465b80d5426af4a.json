{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/toolbar\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/sidenav\";\nimport * as i8 from \"@angular/material/list\";\nimport * as i9 from \"@angular/material/menu\";\nfunction AppComponent_div_1_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r4.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.label);\n  }\n}\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-toolbar\", 5)(2, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const sidenav_r2 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(sidenav_r2.toggle());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 7);\n    i0.ɵɵtext(6, \"Indezy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"span\", 8);\n    i0.ɵɵelementStart(8, \"button\", 9)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"arrow_drop_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-menu\", null, 1)(17, \"button\", 11)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(27, \"mat-sidenav-container\", 13)(28, \"mat-sidenav\", 14, 2)(30, \"mat-nav-list\");\n    i0.ɵɵtemplate(31, AppComponent_div_1_a_31_Template, 5, 3, \"a\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-sidenav-content\", 16)(33, \"div\", 17);\n    i0.ɵɵelement(34, \"router-outlet\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const userMenu_r5 = i0.ɵɵreference(16);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.firstName, \" \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.lastName, \"\");\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.menuItems);\n  }\n}\nfunction AppComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport class AppComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.title = 'Indezy - Suivi de missions pour freelances';\n    this.currentUser = null;\n    this.menuItems = [{\n      label: 'Tableau de bord',\n      route: '/dashboard',\n      icon: 'dashboard'\n    }, {\n      label: 'Projets',\n      route: '/projects',\n      icon: 'work'\n    }, {\n      label: 'Clients',\n      route: '/clients',\n      icon: 'business'\n    }, {\n      label: 'Contacts',\n      route: '/contacts',\n      icon: 'contacts'\n    }, {\n      label: 'Sources',\n      route: '/sources',\n      icon: 'source'\n    }, {\n      label: 'Kanban',\n      route: '/kanban',\n      icon: 'view_kanban'\n    }, {\n      label: 'Profil',\n      route: '/profile',\n      icon: 'person'\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  logout() {\n    this.authService.logout();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 2,\n      consts: [[\"unauthenticatedView\", \"\"], [\"userMenu\", \"matMenu\"], [\"sidenav\", \"\"], [1, \"app-container\"], [\"class\", \"app-container\", 4, \"ngIf\", \"ngIfElse\"], [\"color\", \"primary\", 1, \"app-toolbar\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [\"mat-button\", \"\", 1, \"user-button\", 3, \"matMenuTriggerFor\"], [1, \"user-name\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"app-sidenav-container\"], [\"mode\", \"side\", \"opened\", \"\", 1, \"app-sidenav\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"app-main-content\"], [1, \"app-content\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\"], [\"matListItemTitle\", \"\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, AppComponent_div_1_Template, 35, 4, \"div\", 4)(2, AppComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const unauthenticatedView_r6 = i0.ɵɵreference(3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isAuthenticated())(\"ngIfElse\", unauthenticatedView_r6);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterOutlet, RouterModule, i3.RouterLink, i3.RouterLinkActive, MatToolbarModule, i4.MatToolbar, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatSidenavModule, i7.MatSidenav, i7.MatSidenavContainer, i7.MatSidenavContent, MatListModule, i8.MatNavList, i8.MatListItem, i8.MatListItemIcon, i8.MatListItemTitle, MatMenuModule, i9.MatMenu, i9.MatMenuItem, i9.MatMenuTrigger],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n}\\n\\n.app-toolbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n}\\n.app-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n.app-toolbar[_ngcontent-%COMP%]   .spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n.app-toolbar[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.app-toolbar[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.app-sidenav-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-top: 64px;\\n}\\n\\n.app-sidenav[_ngcontent-%COMP%] {\\n  width: 280px;\\n  border-right: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-nav-list[_ngcontent-%COMP%] {\\n  padding-top: 16px;\\n}\\n.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%] {\\n  margin: 4px 12px;\\n  border-radius: 8px;\\n}\\n.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item.active-nav-item[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--mat-primary-500), 0.1);\\n  color: var(--mat-primary-500);\\n}\\n.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item.active-nav-item[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: var(--mat-primary-500);\\n}\\n.app-sidenav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]:hover:not(.active-nav-item) {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n\\n.app-main-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n}\\n\\n.app-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  min-height: calc(100vh - 64px);\\n}\\n\\n@media (max-width: 768px) {\\n  .app-sidenav[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .app-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "RouterModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatSidenavModule", "MatListModule", "MatMenuModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r4", "route", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "ɵɵlistener", "AppComponent_div_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "sidenav_r2", "ɵɵreference", "ɵɵresetView", "toggle", "ɵɵelement", "AppComponent_div_1_Template_button_click_22_listener", "ctx_r2", "ɵɵnextContext", "logout", "ɵɵtemplate", "AppComponent_div_1_a_31_Template", "userMenu_r5", "ɵɵtextInterpolate2", "currentUser", "firstName", "lastName", "menuItems", "AppComponent", "constructor", "authService", "title", "ngOnInit", "currentUser$", "subscribe", "user", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_div_1_Template", "AppComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "isAuthenticated", "unauthenticatedView_r6", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "RouterLink", "RouterLinkActive", "i4", "MatToolbar", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i8", "MatNavList", "MatListItem", "MatListItemIcon", "MatListItemTitle", "i9", "MatMenu", "MatMenuItem", "MatMenuTrigger", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { AuthService } from './services/auth.service';\n\ninterface MenuItem {\n  label: string;\n  route: string;\n  icon: string;\n}\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    RouterModule,\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSidenavModule,\n    MatListModule,\n    MatMenuModule\n  ],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'Indezy - Suivi de missions pour freelances';\n  currentUser: any = null;\n\n  menuItems: MenuItem[] = [\n    { label: 'Tableau de bord', route: '/dashboard', icon: 'dashboard' },\n    { label: 'Projets', route: '/projects', icon: 'work' },\n    { label: 'Clients', route: '/clients', icon: 'business' },\n    { label: 'Contacts', route: '/contacts', icon: 'contacts' },\n    { label: 'Sources', route: '/sources', icon: 'source' },\n    { label: 'Kanban', route: '/kanban', icon: 'view_kanban' },\n    { label: 'Profil', route: '/profile', icon: 'person' }\n  ];\n\n  constructor(\n    public authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n}\n", "<div class=\"app-container\">\n  <div class=\"app-container\" *ngIf=\"authService.isAuthenticated(); else unauthenticatedView\">\n  <!-- Top Navigation -->\n  <mat-toolbar color=\"primary\" class=\"app-toolbar\">\n    <button mat-icon-button (click)=\"sidenav.toggle()\" class=\"menu-button\">\n      <mat-icon>menu</mat-icon>\n    </button>\n\n    <span class=\"toolbar-title\">Indezy</span>\n\n    <span class=\"spacer\"></span>\n\n    <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-button\">\n      <mat-icon>account_circle</mat-icon>\n      <span class=\"user-name\">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</span>\n      <mat-icon>arrow_drop_down</mat-icon>\n    </button>\n\n    <mat-menu #userMenu=\"matMenu\">\n      <button mat-menu-item routerLink=\"/profile\">\n        <mat-icon>person</mat-icon>\n        <span>Profil</span>\n      </button>\n      <button mat-menu-item (click)=\"logout()\">\n        <mat-icon>logout</mat-icon>\n        <span>Déconnexion</span>\n      </button>\n    </mat-menu>\n  </mat-toolbar>\n\n  <!-- Sidenav Container -->\n  <mat-sidenav-container class=\"app-sidenav-container\">\n    <mat-sidenav #sidenav mode=\"side\" opened class=\"app-sidenav\">\n      <mat-nav-list>\n        <a mat-list-item\n           *ngFor=\"let item of menuItems\"\n           [routerLink]=\"item.route\"\n           routerLinkActive=\"active-nav-item\">\n          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>\n          <span matListItemTitle>{{ item.label }}</span>\n        </a>\n      </mat-nav-list>\n    </mat-sidenav>\n\n    <!-- Main Content -->\n    <mat-sidenav-content class=\"app-main-content\">\n      <div class=\"app-content\">\n        <router-outlet></router-outlet>\n      </div>\n    </mat-sidenav-content>\n  </mat-sidenav-container>\n</div>\n\n<ng-template #unauthenticatedView>\n  <router-outlet></router-outlet>\n</ng-template>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,YAAY,QAAQ,iBAAiB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;IC8B5CC,EAJF,CAAAC,cAAA,YAGsC,mBACV;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAC5C;;;;IAJDH,EAAA,CAAAI,UAAA,eAAAC,OAAA,CAAAC,KAAA,CAAyB;IAEAN,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAClBT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;;;;;;IAnC7CV,EAHF,CAAAC,cAAA,aAA2F,qBAE1C,gBACwB;IAA/CD,EAAA,CAAAW,UAAA,mBAAAC,oDAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,UAAA,CAAAG,MAAA,EAAgB;IAAA,EAAC;IAChDlB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IAETH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzCH,EAAA,CAAAmB,SAAA,cAA4B;IAG1BnB,EADF,CAAAC,cAAA,gBAAsE,eAC1D;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvFH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAC7B;IAILH,EAFJ,CAAAC,cAAA,yBAA8B,kBACgB,gBAChC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACdF,EADc,CAAAG,YAAA,EAAO,EACZ;IACTH,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAW,UAAA,mBAAAS,qDAAA;MAAApB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAASI,MAAA,CAAAE,MAAA,EAAQ;IAAA,EAAC;IACtCvB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAW;IAGvBF,EAHuB,CAAAG,YAAA,EAAO,EACjB,EACA,EACC;IAKVH,EAFJ,CAAAC,cAAA,iCAAqD,0BACU,oBAC7C;IACZD,EAAA,CAAAwB,UAAA,KAAAC,gCAAA,gBAGsC;IAK1CzB,EADE,CAAAG,YAAA,EAAe,EACH;IAIZH,EADF,CAAAC,cAAA,+BAA8C,eACnB;IACvBD,EAAA,CAAAmB,SAAA,qBAA+B;IAIvCnB,EAHM,CAAAG,YAAA,EAAM,EACc,EACA,EACpB;;;;;IAvCiBH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAI,UAAA,sBAAAsB,WAAA,CAA8B;IAEvB1B,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAA2B,kBAAA,KAAAN,MAAA,CAAAO,WAAA,kBAAAP,MAAA,CAAAO,WAAA,CAAAC,SAAA,OAAAR,MAAA,CAAAO,WAAA,kBAAAP,MAAA,CAAAO,WAAA,CAAAE,QAAA,KAAwD;IAqB1D9B,EAAA,CAAAO,SAAA,IAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAU,SAAA,CAAY;;;;;IAmBtC/B,EAAA,CAAAmB,SAAA,oBAA+B;;;ADpBjC,OAAM,MAAOa,YAAY;EAcvBC,YACSC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAdpB,KAAAC,KAAK,GAAG,4CAA4C;IACpD,KAAAP,WAAW,GAAQ,IAAI;IAEvB,KAAAG,SAAS,GAAe,CACtB;MAAErB,KAAK,EAAE,iBAAiB;MAAEJ,KAAK,EAAE,YAAY;MAAEG,IAAI,EAAE;IAAW,CAAE,EACpE;MAAEC,KAAK,EAAE,SAAS;MAAEJ,KAAK,EAAE,WAAW;MAAEG,IAAI,EAAE;IAAM,CAAE,EACtD;MAAEC,KAAK,EAAE,SAAS;MAAEJ,KAAK,EAAE,UAAU;MAAEG,IAAI,EAAE;IAAU,CAAE,EACzD;MAAEC,KAAK,EAAE,UAAU;MAAEJ,KAAK,EAAE,WAAW;MAAEG,IAAI,EAAE;IAAU,CAAE,EAC3D;MAAEC,KAAK,EAAE,SAAS;MAAEJ,KAAK,EAAE,UAAU;MAAEG,IAAI,EAAE;IAAQ,CAAE,EACvD;MAAEC,KAAK,EAAE,QAAQ;MAAEJ,KAAK,EAAE,SAAS;MAAEG,IAAI,EAAE;IAAa,CAAE,EAC1D;MAAEC,KAAK,EAAE,QAAQ;MAAEJ,KAAK,EAAE,UAAU;MAAEG,IAAI,EAAE;IAAQ,CAAE,CACvD;EAIE;EAEH2B,QAAQA,CAAA;IACN,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACX,WAAW,GAAGW,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAhB,MAAMA,CAAA;IACJ,IAAI,CAACW,WAAW,CAACX,MAAM,EAAE;EAC3B;;;uBA1BWS,YAAY,EAAAhC,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZV,YAAY;MAAAW,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7C,EAAA,CAAA8C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClCzBpD,EAAA,CAAAC,cAAA,aAA2B;UAqD3BD,EApDE,CAAAwB,UAAA,IAAA8B,2BAAA,kBAA2F,IAAAC,mCAAA,gCAAAvD,EAAA,CAAAwD,sBAAA,CAoD3D;UAGlCxD,EAAA,CAAAG,YAAA,EAAM;;;;UAvDwBH,EAAA,CAAAO,SAAA,EAAqC;UAAAP,EAArC,CAAAI,UAAA,SAAAiD,GAAA,CAAAnB,WAAA,CAAAuB,eAAA,GAAqC,aAAAC,sBAAA,CAAwB;;;qBDoBvFnE,YAAY,EAAAoE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZrE,YAAY,EACZC,YAAY,EAAAqE,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA,EACZtE,gBAAgB,EAAAuE,EAAA,CAAAC,UAAA,EAChBvE,eAAe,EAAAwE,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfzE,aAAa,EAAA0E,EAAA,CAAAC,OAAA,EACb1E,gBAAgB,EAAA2E,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChB7E,aAAa,EAAA8E,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,gBAAA,EACbjF,aAAa,EAAAkF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}