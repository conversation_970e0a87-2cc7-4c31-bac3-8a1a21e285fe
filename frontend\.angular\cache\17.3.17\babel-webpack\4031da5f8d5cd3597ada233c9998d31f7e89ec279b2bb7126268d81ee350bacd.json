{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { routes } from './app.routes';\nimport { authInterceptor } from './interceptors/auth.interceptor';\nimport { errorInterceptor } from './interceptors/error.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])), provideAnimations(), importProvidersFrom(MatSnackBarModule, MatDialogModule)]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "provideRouter", "provideHttpClient", "withInterceptors", "provideAnimations", "MatSnackBarModule", "MatDialogModule", "routes", "authInterceptor", "errorInterceptor", "appConfig", "providers"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\n\nimport { routes } from './app.routes';\nimport { authInterceptor } from './interceptors/auth.interceptor';\nimport { errorInterceptor } from './interceptors/error.interceptor';\n\nexport const appConfig: ApplicationConfig = {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(\n      withInterceptors([authInterceptor, errorInterceptor])\n    ),\n    provideAnimations(),\n    importProvidersFrom(\n      MatSnackBarModule,\n      MatDialogModule\n    )\n  ]\n};\n"], "mappings": "AAAA,SAA4BA,mBAAmB,QAAQ,eAAe;AACtE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,gBAAgB,QAAQ,kCAAkC;AAEnE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTV,aAAa,CAACM,MAAM,CAAC,EACrBL,iBAAiB,CACfC,gBAAgB,CAAC,CAACK,eAAe,EAAEC,gBAAgB,CAAC,CAAC,CACtD,EACDL,iBAAiB,EAAE,EACnBJ,mBAAmB,CACjBK,iBAAiB,EACjBC,eAAe,CAChB;CAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}