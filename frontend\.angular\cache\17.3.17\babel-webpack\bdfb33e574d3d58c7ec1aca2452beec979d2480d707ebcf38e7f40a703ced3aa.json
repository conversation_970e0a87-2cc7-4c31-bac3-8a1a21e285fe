{"ast": null, "code": "import { authGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'profile',\n  loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'projects',\n  loadChildren: () => import('./components/projects/projects.routes').then(m => m.projectRoutes),\n  canActivate: [authGuard]\n}, {\n  path: 'clients',\n  loadChildren: () => import('./components/clients/clients.routes').then(m => m.clientRoutes),\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}