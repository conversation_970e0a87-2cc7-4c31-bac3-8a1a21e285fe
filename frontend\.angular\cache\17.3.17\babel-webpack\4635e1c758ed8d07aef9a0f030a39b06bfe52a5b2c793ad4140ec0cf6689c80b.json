{"ast": null, "code": "export const contactRoutes = [{\n  path: '',\n  loadComponent: () => import('./contact-list/contact-list.component').then(m => m.ContactListComponent)\n}, {\n  path: 'create',\n  loadComponent: () => import('./contact-form/contact-form.component').then(m => m.ContactFormComponent)\n}, {\n  path: ':id',\n  loadComponent: () => import('./contact-detail/contact-detail.component').then(m => m.ContactDetailComponent)\n}, {\n  path: ':id/edit',\n  loadComponent: () => import('./contact-form/contact-form.component').then(m => m.ContactFormComponent)\n}];", "map": {"version": 3, "names": ["contactRoutes", "path", "loadComponent", "then", "m", "ContactListComponent", "ContactFormComponent", "ContactDetailComponent"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\contacts\\contacts.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const contactRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./contact-list/contact-list.component').then(m => m.ContactListComponent)\n  },\n  {\n    path: 'create',\n    loadComponent: () => import('./contact-form/contact-form.component').then(m => m.ContactFormComponent)\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./contact-detail/contact-detail.component').then(m => m.ContactDetailComponent)\n  },\n  {\n    path: ':id/edit',\n    loadComponent: () => import('./contact-form/contact-form.component').then(m => m.ContactFormComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,oBAAoB;CACtG,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,oBAAoB;CACtG,EACD;EACEL,IAAI,EAAE,KAAK;EACXC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,sBAAsB;CAC5G,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,oBAAoB;CACtG,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}