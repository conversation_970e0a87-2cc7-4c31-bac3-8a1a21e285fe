{"ast": null, "code": "import { of, BehaviorSubject } from 'rxjs';\nimport { delay, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let ClientService = /*#__PURE__*/(() => {\n  class ClientService {\n    constructor() {\n      this.clients$ = new BehaviorSubject([]);\n      this.nextId = 1;\n      // Initialize with mock data\n      this.initializeMockData();\n    }\n    initializeMockData() {\n      const mockClients = [{\n        id: 1,\n        name: 'TechCorp Solutions',\n        email: '<EMAIL>',\n        phone: '+33 1 23 45 67 89',\n        address: '123 Avenue des Champs-Élysées, 75008 Paris',\n        contactPerson: '<PERSON>',\n        industry: 'Technology',\n        website: 'https://techcorp.fr',\n        notes: 'Client principal, projets récurrents',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-06-20')\n      }, {\n        id: 2,\n        name: 'StartupInnovate',\n        email: '<EMAIL>',\n        phone: '+33 1 98 76 54 32',\n        address: '45 Rue de Rivoli, 75001 Paris',\n        contactPerson: '<PERSON>',\n        industry: 'Fintech',\n        website: 'https://startupinnovate.com',\n        notes: 'Startup prometteuse, budget limité',\n        status: 'ACTIVE',\n        createdAt: new Date('2024-02-10'),\n        updatedAt: new Date('2024-06-15')\n      }, {\n        id: 3,\n        name: 'E-Commerce Plus',\n        email: '<EMAIL>',\n        phone: '+33 1 11 22 33 44',\n        address: '78 Boulevard Saint-Germain, 75006 Paris',\n        contactPerson: 'Sophie Laurent',\n        industry: 'E-commerce',\n        status: 'PROSPECT',\n        createdAt: new Date('2024-06-01'),\n        updatedAt: new Date('2024-06-01')\n      }, {\n        id: 4,\n        name: 'ConsultingPro',\n        email: '<EMAIL>',\n        phone: '+33 1 55 66 77 88',\n        address: '12 Place Vendôme, 75001 Paris',\n        contactPerson: 'Pierre Moreau',\n        industry: 'Consulting',\n        notes: 'Client exigeant mais bien payeur',\n        status: 'INACTIVE',\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-03-15')\n      }];\n      this.clients$.next(mockClients);\n      this.nextId = Math.max(...mockClients.map(c => c.id)) + 1;\n    }\n    getClients() {\n      return this.clients$.asObservable();\n    }\n    getClient(id) {\n      return this.clients$.pipe(map(clients => clients.find(client => client.id === id)), delay(300) // Simulate API delay\n      );\n    }\n    createClient(clientData) {\n      const newClient = {\n        ...clientData,\n        id: this.nextId++,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n      const currentClients = this.clients$.value;\n      this.clients$.next([...currentClients, newClient]);\n      return of(newClient).pipe(delay(500));\n    }\n    updateClient(clientData) {\n      const currentClients = this.clients$.value;\n      const index = currentClients.findIndex(client => client.id === clientData.id);\n      if (index === -1) {\n        throw new Error('Client not found');\n      }\n      const updatedClient = {\n        ...currentClients[index],\n        ...clientData,\n        updatedAt: new Date()\n      };\n      const updatedClients = [...currentClients];\n      updatedClients[index] = updatedClient;\n      this.clients$.next(updatedClients);\n      return of(updatedClient).pipe(delay(500));\n    }\n    deleteClient(id) {\n      const currentClients = this.clients$.value;\n      const filteredClients = currentClients.filter(client => client.id !== id);\n      if (filteredClients.length === currentClients.length) {\n        throw new Error('Client not found');\n      }\n      this.clients$.next(filteredClients);\n      return of(true).pipe(delay(300));\n    }\n    searchClients(query) {\n      return this.clients$.pipe(map(clients => clients.filter(client => client.name.toLowerCase().includes(query.toLowerCase()) || client.email.toLowerCase().includes(query.toLowerCase()) || client.contactPerson.toLowerCase().includes(query.toLowerCase()) || client.industry.toLowerCase().includes(query.toLowerCase()))), delay(300));\n    }\n    getClientsByStatus(status) {\n      return this.clients$.pipe(map(clients => clients.filter(client => client.status === status)), delay(300));\n    }\n    getClientStats() {\n      return this.clients$.pipe(map(clients => ({\n        total: clients.length,\n        active: clients.filter(c => c.status === 'ACTIVE').length,\n        inactive: clients.filter(c => c.status === 'INACTIVE').length,\n        prospects: clients.filter(c => c.status === 'PROSPECT').length\n      })), delay(200));\n    }\n    static {\n      this.ɵfac = function ClientService_Factory(t) {\n        return new (t || ClientService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ClientService,\n        factory: ClientService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ClientService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}