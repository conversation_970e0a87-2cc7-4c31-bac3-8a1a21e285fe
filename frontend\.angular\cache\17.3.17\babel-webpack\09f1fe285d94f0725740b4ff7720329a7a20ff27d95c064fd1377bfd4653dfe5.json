{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/toolbar\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/sidenav\";\nimport * as i6 from \"@angular/material/list\";\nfunction AppComponent_div_1_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r4.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.label);\n  }\n}\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-toolbar\", 5)(2, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const sidenav_r2 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(sidenav_r2.toggle());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 7);\n    i0.ɵɵtext(6, \"Indezy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"span\", 8);\n    i0.ɵɵelementStart(8, \"button\", 9)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"arrow_drop_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-menu\", null, 1)(17, \"button\", 11)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(27, \"mat-sidenav-container\", 13)(28, \"mat-sidenav\", 14, 2)(30, \"mat-nav-list\");\n    i0.ɵɵtemplate(31, AppComponent_div_1_a_31_Template, 5, 3, \"a\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-sidenav-content\", 16)(33, \"div\", 17);\n    i0.ɵɵelement(34, \"router-outlet\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const userMenu_r5 = i0.ɵɵreference(16);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.firstName, \" \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.lastName, \"\");\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.menuItems);\n  }\n}\nfunction AppComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport class AppComponent {\n  constructor() {\n    this.title = 'Indezy - Suivi de missions pour freelances';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 2,\n      consts: [[\"unauthenticatedView\", \"\"], [\"userMenu\", \"matMenu\"], [\"sidenav\", \"\"], [1, \"app-container\"], [\"class\", \"app-container\", 4, \"ngIf\", \"ngIfElse\"], [\"color\", \"primary\", 1, \"app-toolbar\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [\"mat-button\", \"\", 1, \"user-button\", 3, \"matMenuTriggerFor\"], [1, \"user-name\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"app-sidenav-container\"], [\"mode\", \"side\", \"opened\", \"\", 1, \"app-sidenav\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"app-main-content\"], [1, \"app-content\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\"], [\"matListItemTitle\", \"\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, AppComponent_div_1_Template, 35, 4, \"div\", 4)(2, AppComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const unauthenticatedView_r6 = i0.ɵɵreference(3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isAuthenticated())(\"ngIfElse\", unauthenticatedView_r6);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterOutlet, MatToolbarModule, i2.MatToolbar, MatButtonModule, i3.MatButton, i3.MatIconButton, MatIconModule, i4.MatIcon, MatSidenavModule, i5.MatSidenav, i5.MatSidenavContainer, i5.MatSidenavContent, MatListModule, i6.MatNavList, i6.MatListItem, i6.MatListItemIcon, i6.MatListItemTitle],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmFwcC1jb250YWluZXIge1xuICBoZWlnaHQ6IDEwMHZoO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatSidenavModule", "MatListModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r4", "route", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "ɵɵlistener", "AppComponent_div_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "sidenav_r2", "ɵɵreference", "ɵɵresetView", "toggle", "ɵɵelement", "AppComponent_div_1_Template_button_click_22_listener", "ctx_r2", "ɵɵnextContext", "logout", "ɵɵtemplate", "AppComponent_div_1_a_31_Template", "userMenu_r5", "ɵɵtextInterpolate2", "currentUser", "firstName", "lastName", "menuItems", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_div_1_Template", "AppComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "authService", "isAuthenticated", "unauthenticatedView_r6", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatToolbar", "i3", "MatButton", "MatIconButton", "i4", "MatIcon", "i5", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i6", "MatNavList", "MatListItem", "MatListItemIcon", "MatListItemTitle", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { AuthService } from './services/auth.service';\n\ninterface MenuItem {\n  label: string;\n  route: string;\n  icon: string;\n}\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSidenavModule,\n    MatListModule\n  ],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'Indezy - Suivi de missions pour freelances';\n}\n", "<div class=\"app-container\">\n  <div class=\"app-container\" *ngIf=\"authService.isAuthenticated(); else unauthenticatedView\">\n  <!-- Top Navigation -->\n  <mat-toolbar color=\"primary\" class=\"app-toolbar\">\n    <button mat-icon-button (click)=\"sidenav.toggle()\" class=\"menu-button\">\n      <mat-icon>menu</mat-icon>\n    </button>\n\n    <span class=\"toolbar-title\">Indezy</span>\n\n    <span class=\"spacer\"></span>\n\n    <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-button\">\n      <mat-icon>account_circle</mat-icon>\n      <span class=\"user-name\">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</span>\n      <mat-icon>arrow_drop_down</mat-icon>\n    </button>\n\n    <mat-menu #userMenu=\"matMenu\">\n      <button mat-menu-item routerLink=\"/profile\">\n        <mat-icon>person</mat-icon>\n        <span>Profil</span>\n      </button>\n      <button mat-menu-item (click)=\"logout()\">\n        <mat-icon>logout</mat-icon>\n        <span>Déconnexion</span>\n      </button>\n    </mat-menu>\n  </mat-toolbar>\n\n  <!-- Sidenav Container -->\n  <mat-sidenav-container class=\"app-sidenav-container\">\n    <mat-sidenav #sidenav mode=\"side\" opened class=\"app-sidenav\">\n      <mat-nav-list>\n        <a mat-list-item\n           *ngFor=\"let item of menuItems\"\n           [routerLink]=\"item.route\"\n           routerLinkActive=\"active-nav-item\">\n          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>\n          <span matListItemTitle>{{ item.label }}</span>\n        </a>\n      </mat-nav-list>\n    </mat-sidenav>\n\n    <!-- Main Content -->\n    <mat-sidenav-content class=\"app-main-content\">\n      <div class=\"app-content\">\n        <router-outlet></router-outlet>\n      </div>\n    </mat-sidenav-content>\n  </mat-sidenav-container>\n</div>\n\n<ng-template #unauthenticatedView>\n  <router-outlet></router-outlet>\n</ng-template>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;IC+B5CC,EAJF,CAAAC,cAAA,YAGsC,mBACV;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAC5C;;;;IAJDH,EAAA,CAAAI,UAAA,eAAAC,OAAA,CAAAC,KAAA,CAAyB;IAEAN,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAClBT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;;;;;;IAnC7CV,EAHF,CAAAC,cAAA,aAA2F,qBAE1C,gBACwB;IAA/CD,EAAA,CAAAW,UAAA,mBAAAC,oDAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,UAAA,CAAAG,MAAA,EAAgB;IAAA,EAAC;IAChDlB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IAETH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzCH,EAAA,CAAAmB,SAAA,cAA4B;IAG1BnB,EADF,CAAAC,cAAA,gBAAsE,eAC1D;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvFH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAC7B;IAILH,EAFJ,CAAAC,cAAA,yBAA8B,kBACgB,gBAChC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACdF,EADc,CAAAG,YAAA,EAAO,EACZ;IACTH,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAW,UAAA,mBAAAS,qDAAA;MAAApB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAASI,MAAA,CAAAE,MAAA,EAAQ;IAAA,EAAC;IACtCvB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAW;IAGvBF,EAHuB,CAAAG,YAAA,EAAO,EACjB,EACA,EACC;IAKVH,EAFJ,CAAAC,cAAA,iCAAqD,0BACU,oBAC7C;IACZD,EAAA,CAAAwB,UAAA,KAAAC,gCAAA,gBAGsC;IAK1CzB,EADE,CAAAG,YAAA,EAAe,EACH;IAIZH,EADF,CAAAC,cAAA,+BAA8C,eACnB;IACvBD,EAAA,CAAAmB,SAAA,qBAA+B;IAIvCnB,EAHM,CAAAG,YAAA,EAAM,EACc,EACA,EACpB;;;;;IAvCiBH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAI,UAAA,sBAAAsB,WAAA,CAA8B;IAEvB1B,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAA2B,kBAAA,KAAAN,MAAA,CAAAO,WAAA,kBAAAP,MAAA,CAAAO,WAAA,CAAAC,SAAA,OAAAR,MAAA,CAAAO,WAAA,kBAAAP,MAAA,CAAAO,WAAA,CAAAE,QAAA,KAAwD;IAqB1D9B,EAAA,CAAAO,SAAA,IAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAU,SAAA,CAAY;;;;;IAmBtC/B,EAAA,CAAAmB,SAAA,oBAA+B;;;ADtBjC,OAAM,MAAOa,YAAY;EAfzBC,YAAA;IAgBE,KAAAC,KAAK,GAAG,4CAA4C;;;;uBADzCF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArC,EAAA,CAAAsC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCzB5C,EAAA,CAAAC,cAAA,aAA2B;UAqD3BD,EApDE,CAAAwB,UAAA,IAAAsB,2BAAA,kBAA2F,IAAAC,mCAAA,gCAAA/C,EAAA,CAAAgD,sBAAA,CAoD3D;UAGlChD,EAAA,CAAAG,YAAA,EAAM;;;;UAvDwBH,EAAA,CAAAO,SAAA,EAAqC;UAAAP,EAArC,CAAAI,UAAA,SAAAyC,GAAA,CAAAI,WAAA,CAAAC,eAAA,GAAqC,aAAAC,sBAAA,CAAwB;;;qBDoBvF1D,YAAY,EAAA2D,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ5D,YAAY,EACZC,gBAAgB,EAAA4D,EAAA,CAAAC,UAAA,EAChB5D,eAAe,EAAA6D,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf9D,aAAa,EAAA+D,EAAA,CAAAC,OAAA,EACb/D,gBAAgB,EAAAgE,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChBlE,aAAa,EAAAmE,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}