{"ast": null, "code": "import { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\nexport var NotificationKind;\n(function (NotificationKind) {\n  NotificationKind[\"NEXT\"] = \"N\";\n  NotificationKind[\"ERROR\"] = \"E\";\n  NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind || (NotificationKind = {}));\nexport class Notification {\n  constructor(kind, value, error) {\n    this.kind = kind;\n    this.value = value;\n    this.error = error;\n    this.hasValue = kind === 'N';\n  }\n  observe(observer) {\n    return observeNotification(this, observer);\n  }\n  do(nextHandler, errorHandler, completeHandler) {\n    const {\n      kind,\n      value,\n      error\n    } = this;\n    return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n  }\n  accept(nextOrObserver, error, complete) {\n    var _a;\n    return isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next) ? this.observe(nextOrObserver) : this.do(nextOrObserver, error, complete);\n  }\n  toObservable() {\n    const {\n      kind,\n      value,\n      error\n    } = this;\n    const result = kind === 'N' ? of(value) : kind === 'E' ? throwError(() => error) : kind === 'C' ? EMPTY : 0;\n    if (!result) {\n      throw new TypeError(`Unexpected notification kind ${kind}`);\n    }\n    return result;\n  }\n  static createNext(value) {\n    return new Notification('N', value);\n  }\n  static createError(err) {\n    return new Notification('E', undefined, err);\n  }\n  static createComplete() {\n    return Notification.completeNotification;\n  }\n}\nNotification.completeNotification = new Notification('C');\nexport function observeNotification(notification, observer) {\n  var _a, _b, _c;\n  const {\n    kind,\n    value,\n    error\n  } = notification;\n  if (typeof kind !== 'string') {\n    throw new TypeError('Invalid notification, missing \"kind\"');\n  }\n  kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}", "map": {"version": 3, "names": ["EMPTY", "of", "throwError", "isFunction", "NotificationKind", "Notification", "constructor", "kind", "value", "error", "hasValue", "observe", "observer", "observeNotification", "do", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "completeHandler", "accept", "nextOrObserver", "complete", "_a", "next", "toObservable", "result", "TypeError", "createNext", "createError", "err", "undefined", "createComplete", "completeNotification", "notification", "_b", "_c", "call"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/rxjs/dist/esm/internal/Notification.js"], "sourcesContent": ["import { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\nexport var NotificationKind;\n(function (NotificationKind) {\n    NotificationKind[\"NEXT\"] = \"N\";\n    NotificationKind[\"ERROR\"] = \"E\";\n    NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind || (NotificationKind = {}));\nexport class Notification {\n    constructor(kind, value, error) {\n        this.kind = kind;\n        this.value = value;\n        this.error = error;\n        this.hasValue = kind === 'N';\n    }\n    observe(observer) {\n        return observeNotification(this, observer);\n    }\n    do(nextHandler, errorHandler, completeHandler) {\n        const { kind, value, error } = this;\n        return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n    }\n    accept(nextOrObserver, error, complete) {\n        var _a;\n        return isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next)\n            ? this.observe(nextOrObserver)\n            : this.do(nextOrObserver, error, complete);\n    }\n    toObservable() {\n        const { kind, value, error } = this;\n        const result = kind === 'N'\n            ?\n                of(value)\n            :\n                kind === 'E'\n                    ?\n                        throwError(() => error)\n                    :\n                        kind === 'C'\n                            ?\n                                EMPTY\n                            :\n                                0;\n        if (!result) {\n            throw new TypeError(`Unexpected notification kind ${kind}`);\n        }\n        return result;\n    }\n    static createNext(value) {\n        return new Notification('N', value);\n    }\n    static createError(err) {\n        return new Notification('E', undefined, err);\n    }\n    static createComplete() {\n        return Notification.completeNotification;\n    }\n}\nNotification.completeNotification = new Notification('C');\nexport function observeNotification(notification, observer) {\n    var _a, _b, _c;\n    const { kind, value, error } = notification;\n    if (typeof kind !== 'string') {\n        throw new TypeError('Invalid notification, missing \"kind\"');\n    }\n    kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,IAAIC,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,MAAM,CAAC,GAAG,GAAG;EAC9BA,gBAAgB,CAAC,OAAO,CAAC,GAAG,GAAG;EAC/BA,gBAAgB,CAAC,UAAU,CAAC,GAAG,GAAG;AACtC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGH,IAAI,KAAK,GAAG;EAChC;EACAI,OAAOA,CAACC,QAAQ,EAAE;IACd,OAAOC,mBAAmB,CAAC,IAAI,EAAED,QAAQ,CAAC;EAC9C;EACAE,EAAEA,CAACC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAE;IAC3C,MAAM;MAAEV,IAAI;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAG,IAAI;IACnC,OAAOF,IAAI,KAAK,GAAG,GAAGQ,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACP,KAAK,CAAC,GAAGD,IAAI,KAAK,GAAG,GAAGS,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACP,KAAK,CAAC,GAAGQ,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC,CAAC;EAC7R;EACAC,MAAMA,CAACC,cAAc,EAAEV,KAAK,EAAEW,QAAQ,EAAE;IACpC,IAAIC,EAAE;IACN,OAAOlB,UAAU,CAAC,CAACkB,EAAE,GAAGF,cAAc,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC,GAC/E,IAAI,CAACX,OAAO,CAACQ,cAAc,CAAC,GAC5B,IAAI,CAACL,EAAE,CAACK,cAAc,EAAEV,KAAK,EAAEW,QAAQ,CAAC;EAClD;EACAG,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEhB,IAAI;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAG,IAAI;IACnC,MAAMe,MAAM,GAAGjB,IAAI,KAAK,GAAG,GAEnBN,EAAE,CAACO,KAAK,CAAC,GAETD,IAAI,KAAK,GAAG,GAEJL,UAAU,CAAC,MAAMO,KAAK,CAAC,GAEvBF,IAAI,KAAK,GAAG,GAEJP,KAAK,GAEL,CAAC;IACzB,IAAI,CAACwB,MAAM,EAAE;MACT,MAAM,IAAIC,SAAS,CAAC,gCAAgClB,IAAI,EAAE,CAAC;IAC/D;IACA,OAAOiB,MAAM;EACjB;EACA,OAAOE,UAAUA,CAAClB,KAAK,EAAE;IACrB,OAAO,IAAIH,YAAY,CAAC,GAAG,EAAEG,KAAK,CAAC;EACvC;EACA,OAAOmB,WAAWA,CAACC,GAAG,EAAE;IACpB,OAAO,IAAIvB,YAAY,CAAC,GAAG,EAAEwB,SAAS,EAAED,GAAG,CAAC;EAChD;EACA,OAAOE,cAAcA,CAAA,EAAG;IACpB,OAAOzB,YAAY,CAAC0B,oBAAoB;EAC5C;AACJ;AACA1B,YAAY,CAAC0B,oBAAoB,GAAG,IAAI1B,YAAY,CAAC,GAAG,CAAC;AACzD,OAAO,SAASQ,mBAAmBA,CAACmB,YAAY,EAAEpB,QAAQ,EAAE;EACxD,IAAIS,EAAE,EAAEY,EAAE,EAAEC,EAAE;EACd,MAAM;IAAE3B,IAAI;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGuB,YAAY;EAC3C,IAAI,OAAOzB,IAAI,KAAK,QAAQ,EAAE;IAC1B,MAAM,IAAIkB,SAAS,CAAC,sCAAsC,CAAC;EAC/D;EACAlB,IAAI,KAAK,GAAG,GAAG,CAACc,EAAE,GAAGT,QAAQ,CAACU,IAAI,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACvB,QAAQ,EAAEJ,KAAK,CAAC,GAAGD,IAAI,KAAK,GAAG,GAAG,CAAC0B,EAAE,GAAGrB,QAAQ,CAACH,KAAK,MAAM,IAAI,IAAIwB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACvB,QAAQ,EAAEH,KAAK,CAAC,GAAG,CAACyB,EAAE,GAAGtB,QAAQ,CAACQ,QAAQ,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACvB,QAAQ,CAAC;AAC5R", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}