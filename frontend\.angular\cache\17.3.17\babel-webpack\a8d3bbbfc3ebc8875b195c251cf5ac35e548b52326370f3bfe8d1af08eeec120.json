{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/project.service\";\nimport * as i3 from \"../../../services/client.service\";\nimport * as i4 from \"../../../services/auth.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/select\";\nimport * as i14 from \"@angular/material/core\";\nimport * as i15 from \"@angular/material/datepicker\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nfunction ProjectFormComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement du projet...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectFormComponent_form_10_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", client_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", client_r3.name, \" \");\n  }\n}\nfunction ProjectFormComponent_form_10_mat_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const mode_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", mode_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", mode_r4.label, \" \");\n  }\n}\nfunction ProjectFormComponent_form_10_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-form-field\", 20)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Jours t\\u00E9l\\u00E9travail/mois\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-form-field\", 20)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Jours sur site/mois\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 38);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectFormComponent_form_10_mat_option_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rating_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", rating_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", rating_r5.label, \" \");\n  }\n}\nfunction ProjectFormComponent_form_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 12);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectFormComponent_form_10_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"h3\");\n    i0.ɵɵtext(4, \"Informations g\\u00E9n\\u00E9rales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 15)(6, \"mat-form-field\", 16)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"R\\u00F4le / Poste *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 17);\n    i0.ɵɵelementStart(10, \"mat-error\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 15)(13, \"mat-form-field\", 16)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"textarea\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 15)(18, \"mat-form-field\", 16)(19, \"mat-label\");\n    i0.ɵɵtext(20, \"Stack technique\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 15)(23, \"mat-form-field\", 20)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-select\", 21);\n    i0.ɵɵtemplate(27, ProjectFormComponent_form_10_mat_option_27_Template, 2, 2, \"mat-option\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"mat-form-field\", 20)(29, \"mat-label\");\n    i0.ɵɵtext(30, \"TJM *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 23);\n    i0.ɵɵelementStart(32, \"span\", 24);\n    i0.ɵɵtext(33, \"\\u20AC\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"mat-error\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 14)(37, \"h3\");\n    i0.ɵɵtext(38, \"Mode de travail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 15)(40, \"mat-form-field\", 16)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"Mode de travail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-select\", 25);\n    i0.ɵɵtemplate(44, ProjectFormComponent_form_10_mat_option_44_Template, 2, 2, \"mat-option\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(45, ProjectFormComponent_form_10_div_45_Template, 9, 0, \"div\", 26);\n    i0.ɵɵelementStart(46, \"div\", 15)(47, \"mat-form-field\", 16)(48, \"mat-label\");\n    i0.ɵɵtext(49, \"Avantages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(50, \"textarea\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 14)(52, \"h3\");\n    i0.ɵɵtext(53, \"Planning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 15)(55, \"mat-form-field\", 20)(56, \"mat-label\");\n    i0.ɵɵtext(57, \"Date de d\\u00E9but\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(58, \"input\", 28)(59, \"mat-datepicker-toggle\", 29)(60, \"mat-datepicker\", null, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"mat-form-field\", 20)(63, \"mat-label\");\n    i0.ɵɵtext(64, \"Dur\\u00E9e (mois)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"input\", 30);\n    i0.ɵɵelementStart(66, \"mat-error\");\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 15)(69, \"mat-form-field\", 20)(70, \"mat-label\");\n    i0.ɵɵtext(71, \"Renouvellement (mois)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(72, \"input\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"mat-form-field\", 20)(74, \"mat-label\");\n    i0.ɵɵtext(75, \"Jours travaill\\u00E9s/an\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(76, \"input\", 32);\n    i0.ɵɵelementStart(77, \"mat-error\");\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(79, \"div\", 14)(80, \"h3\");\n    i0.ɵɵtext(81, \"Informations compl\\u00E9mentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"div\", 15)(83, \"mat-form-field\", 16)(84, \"mat-label\");\n    i0.ɵɵtext(85, \"Lien du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(86, \"input\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(87, \"div\", 15)(88, \"mat-form-field\", 16)(89, \"mat-label\");\n    i0.ɵɵtext(90, \"\\u00C9valuation personnelle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"mat-select\", 34);\n    i0.ɵɵtemplate(92, ProjectFormComponent_form_10_mat_option_92_Template, 2, 2, \"mat-option\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(93, \"div\", 15)(94, \"mat-form-field\", 16)(95, \"mat-label\");\n    i0.ɵɵtext(96, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(97, \"textarea\", 35);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const startDatePicker_r6 = i0.ɵɵreference(61);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.projectForm);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"role\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.clients);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"dailyRate\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.workModeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r1.projectForm.get(\"workMode\")) == null ? null : tmp_7_0.value) === \"HYBRID\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matDatepicker\", startDatePicker_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", startDatePicker_r6);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"durationInMonths\"));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"daysPerYear\"));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.ratingOptions);\n  }\n}\nfunction ProjectFormComponent_mat_spinner_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 39);\n  }\n}\nfunction ProjectFormComponent_mat_icon_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"save\" : \"add\");\n  }\n}\nexport let ProjectFormComponent = /*#__PURE__*/(() => {\n  class ProjectFormComponent {\n    constructor(fb, projectService, clientService, authService, router, route, snackBar) {\n      this.fb = fb;\n      this.projectService = projectService;\n      this.clientService = clientService;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.clients = [];\n      this.isLoading = false;\n      this.isSubmitting = false;\n      this.isEditMode = false;\n      this.workModeOptions = [{\n        value: 'REMOTE',\n        label: 'Télétravail'\n      }, {\n        value: 'ONSITE',\n        label: 'Sur site'\n      }, {\n        value: 'HYBRID',\n        label: 'Hybride'\n      }];\n      this.ratingOptions = [{\n        value: 1,\n        label: '1 - Très mauvais'\n      }, {\n        value: 2,\n        label: '2 - Mauvais'\n      }, {\n        value: 3,\n        label: '3 - Moyen'\n      }, {\n        value: 4,\n        label: '4 - Bon'\n      }, {\n        value: 5,\n        label: '5 - Excellent'\n      }];\n      this.destroy$ = new Subject();\n      this.projectForm = this.createForm();\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getUser();\n      this.loadClients();\n      this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        if (params['id']) {\n          this.projectId = +params['id'];\n          this.isEditMode = true;\n          this.loadProject();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    createForm() {\n      return this.fb.group({\n        role: ['', [Validators.required, Validators.minLength(2)]],\n        description: [''],\n        techStack: [''],\n        dailyRate: ['', [Validators.required, Validators.min(1)]],\n        workMode: [''],\n        remoteDaysPerMonth: [''],\n        onsiteDaysPerMonth: [''],\n        advantages: [''],\n        startDate: [''],\n        durationInMonths: ['', [Validators.min(1)]],\n        orderRenewalInMonths: [''],\n        daysPerYear: ['', [Validators.min(1), Validators.max(365)]],\n        link: [''],\n        personalRating: [''],\n        notes: [''],\n        clientId: ['']\n      });\n    }\n    loadClients() {\n      this.clientService.getClients().pipe(takeUntil(this.destroy$)).subscribe({\n        next: clients => {\n          this.clients = clients.filter(client => client.status === 'ACTIVE');\n        },\n        error: error => {\n          console.error('Error loading clients:', error);\n          this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    loadProject() {\n      if (!this.projectId) return;\n      this.isLoading = true;\n      this.projectService.getById(this.projectId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: project => {\n          if (project) {\n            this.projectForm.patchValue({\n              role: project.role,\n              description: project.description,\n              techStack: project.techStack,\n              dailyRate: project.dailyRate,\n              workMode: project.workMode,\n              remoteDaysPerMonth: project.remoteDaysPerMonth,\n              onsiteDaysPerMonth: project.onsiteDaysPerMonth,\n              advantages: project.advantages,\n              startDate: project.startDate ? new Date(project.startDate) : null,\n              durationInMonths: project.durationInMonths,\n              orderRenewalInMonths: project.orderRenewalInMonths,\n              daysPerYear: project.daysPerYear,\n              link: project.link,\n              personalRating: project.personalRating,\n              notes: project.notes,\n              clientId: project.clientId\n            });\n          } else {\n            this.snackBar.open('Projet non trouvé', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/projects']);\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading project:', error);\n          this.snackBar.open('Erreur lors du chargement du projet', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    onSubmit() {\n      if (this.projectForm.valid && !this.isSubmitting) {\n        this.isSubmitting = true;\n        const formValue = this.projectForm.value;\n        // Find client name for the project\n        const selectedClient = this.clients.find(client => client.id === formValue.clientId);\n        const projectData = {\n          ...formValue,\n          clientName: selectedClient?.name || '',\n          freelanceId: this.currentUser?.id,\n          startDate: formValue.startDate ? formValue.startDate.toISOString().split('T')[0] : undefined\n        };\n        const operation = this.isEditMode ? this.projectService.update(this.projectId, projectData) : this.projectService.create(projectData);\n        operation.pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            const message = this.isEditMode ? 'Projet modifié avec succès' : 'Projet créé avec succès';\n            this.snackBar.open(message, 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/projects']);\n          },\n          error: error => {\n            console.error('Error saving project:', error);\n            const message = this.isEditMode ? 'Erreur lors de la modification du projet' : 'Erreur lors de la création du projet';\n            this.snackBar.open(message, 'Fermer', {\n              duration: 3000\n            });\n            this.isSubmitting = false;\n          }\n        });\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    onCancel() {\n      this.router.navigate(['/projects']);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.projectForm.controls).forEach(key => {\n        const control = this.projectForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFieldError(fieldName) {\n      const control = this.projectForm.get(fieldName);\n      if (control?.errors && control.touched) {\n        if (control.errors['required']) {\n          return 'Ce champ est requis';\n        }\n        if (control.errors['minlength']) {\n          return `Minimum ${control.errors['minlength'].requiredLength} caractères`;\n        }\n        if (control.errors['min']) {\n          return `La valeur doit être supérieure à ${control.errors['min'].min}`;\n        }\n        if (control.errors['max']) {\n          return `La valeur doit être inférieure à ${control.errors['max'].max}`;\n        }\n      }\n      return '';\n    }\n    get pageTitle() {\n      return this.isEditMode ? 'Modifier le projet' : 'Nouveau projet';\n    }\n    get submitButtonText() {\n      return this.isEditMode ? 'Modifier' : 'Créer';\n    }\n    static {\n      this.ɵfac = function ProjectFormComponent_Factory(t) {\n        return new (t || ProjectFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.ClientService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectFormComponent,\n        selectors: [[\"app-project-form\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 20,\n        vars: 8,\n        consts: [[\"startDatePicker\", \"\"], [1, \"project-form-container\"], [1, \"form-card\"], [1, \"header-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-container\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-grid\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"role\", \"placeholder\", \"Ex: D\\u00E9veloppeur Full Stack\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"Description d\\u00E9taill\\u00E9e du projet...\"], [\"matInput\", \"\", \"formControlName\", \"techStack\", \"placeholder\", \"Ex: React, Node.js, PostgreSQL\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"formControlName\", \"clientId\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"dailyRate\", \"placeholder\", \"600\"], [\"matSuffix\", \"\"], [\"formControlName\", \"workMode\"], [\"class\", \"form-row\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"advantages\", \"rows\", \"2\", \"placeholder\", \"Ex: \\u00C9quipe dynamique, technologies modernes...\"], [\"matInput\", \"\", \"formControlName\", \"startDate\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"durationInMonths\", \"placeholder\", \"6\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"orderRenewalInMonths\", \"placeholder\", \"3\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"daysPerYear\", \"placeholder\", \"220\"], [\"matInput\", \"\", \"formControlName\", \"link\", \"placeholder\", \"https://...\"], [\"formControlName\", \"personalRating\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"4\", \"placeholder\", \"Notes personnelles sur le projet...\"], [3, \"value\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"remoteDaysPerMonth\", \"placeholder\", \"15\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"onsiteDaysPerMonth\", \"placeholder\", \"5\"], [\"diameter\", \"20\"]],\n        template: function ProjectFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"mat-card\", 2)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"div\", 3)(5, \"mat-icon\");\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(8, \"mat-card-content\");\n            i0.ɵɵtemplate(9, ProjectFormComponent_div_9_Template, 4, 0, \"div\", 4)(10, ProjectFormComponent_form_10_Template, 98, 11, \"form\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"mat-card-actions\", 6)(12, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function ProjectFormComponent_Template_button_click_12_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelementStart(13, \"mat-icon\");\n            i0.ɵɵtext(14, \"cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function ProjectFormComponent_Template_button_click_16_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(17, ProjectFormComponent_mat_spinner_17_Template, 1, 0, \"mat-spinner\", 9)(18, ProjectFormComponent_mat_icon_18_Template, 2, 1, \"mat-icon\", 10);\n            i0.ɵɵtext(19);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add\");\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.pageTitle, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.projectForm.invalid || ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.submitButtonText, \" \");\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, RouterModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, MatButtonModule, i9.MatButton, MatIconModule, i10.MatIcon, MatInputModule, i11.MatInput, i12.MatFormField, i12.MatLabel, i12.MatError, i12.MatSuffix, MatFormFieldModule, MatSelectModule, i13.MatSelect, i14.MatOption, MatDatepickerModule, i15.MatDatepicker, i15.MatDatepickerInput, i15.MatDatepickerToggle, MatNativeDateModule, MatProgressSpinnerModule, i16.MatProgressSpinner],\n        styles: [\".project-form-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:1.5rem;font-weight:500}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:1.5rem;height:1.5rem;color:var(--mdc-theme-primary, #1976d2)}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding:24px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:32px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 20px;font-size:1.1rem;font-weight:500;color:#000c;border-bottom:2px solid var(--mdc-theme-primary, #1976d2);padding-bottom:8px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]:last-child{margin-bottom:0}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{flex:1;width:100%}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{flex:1;min-width:0}@media (max-width: 600px){.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{width:100%}}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:12px;padding:16px 24px;border-top:1px solid rgba(0,0,0,.12)}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;min-width:120px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.1rem;width:1.1rem;height:1.1rem}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-right:8px}@media (max-width: 600px){.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{flex-direction:column-reverse}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}}.mat-mdc-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-mdc-form-field-flex[_ngcontent-%COMP%]{align-items:center}.mat-mdc-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%]{color:#0000001f}.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2)}.mat-mdc-form-field.mat-form-field-appearance-outline.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%]{color:#f44336}.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%]{color:#0009}.mat-mdc-form-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2)}.mat-mdc-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%]{color:#f44336}.mat-mdc-input-element[_ngcontent-%COMP%]{color:#000000de}.mat-mdc-input-element[_ngcontent-%COMP%]::placeholder{color:#0006}.mat-mdc-select[_ngcontent-%COMP%]   .mat-mdc-select-value[_ngcontent-%COMP%]{color:#000000de}.mat-mdc-select[_ngcontent-%COMP%]   .mat-mdc-select-placeholder[_ngcontent-%COMP%]{color:#0006}.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]{background-color:var(--mdc-theme-primary, #1976d2);color:#fff}.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]:disabled{background-color:#0000001f;color:#00000042}.mat-mdc-button[_ngcontent-%COMP%]{color:#0009}.mat-mdc-button[_ngcontent-%COMP%]:hover{background-color:#0000000a}.mat-mdc-form-field-error[_ngcontent-%COMP%]{color:#f44336;font-size:.75rem;margin-top:4px}.mat-datepicker-toggle[_ngcontent-%COMP%]{color:#0000008a}@media (max-width: 768px){.project-form-container[_ngcontent-%COMP%]{padding:16px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding:16px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]{gap:24px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:16px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{margin-bottom:12px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{padding:12px 16px}}@media (max-width: 480px){.project-form-container[_ngcontent-%COMP%]{padding:8px}.project-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{font-size:1.3rem}}\"]\n      });\n    }\n  }\n  return ProjectFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}