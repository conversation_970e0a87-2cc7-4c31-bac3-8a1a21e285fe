{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nlet DashboardComponent = class DashboardComponent {\n  constructor(authService, projectService, freelanceService) {\n    this.authService = authService;\n    this.projectService = projectService;\n    this.freelanceService = freelanceService;\n    this.freelanceProfile = null;\n    this.recentProjects = [];\n    this.stats = {\n      totalProjects: 0,\n      averageDailyRate: 0,\n      totalRevenue: 0,\n      activeProjects: 0\n    };\n    this.menuItems = [{\n      icon: 'dashboard',\n      label: 'Tableau de bord',\n      route: '/dashboard'\n    }, {\n      icon: 'work',\n      label: 'Projets',\n      route: '/projects'\n    }, {\n      icon: 'business',\n      label: 'Clients',\n      route: '/clients'\n    }, {\n      icon: 'contacts',\n      label: 'Contacts',\n      route: '/contacts'\n    }, {\n      icon: 'source',\n      label: 'Sources',\n      route: '/sources'\n    }, {\n      icon: 'person',\n      label: 'Profil',\n      route: '/profile'\n    }];\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getUser();\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    if (this.currentUser?.id) {\n      // Load freelance profile\n      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n        next: profile => {\n          this.freelanceProfile = profile;\n          this.updateStats(profile);\n        },\n        error: error => console.error('Error loading profile:', error)\n      });\n      // Load recent projects\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: projects => {\n          this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n        },\n        error: error => console.error('Error loading projects:', error)\n      });\n    }\n  }\n  updateStats(profile) {\n    this.stats = {\n      totalProjects: profile.totalProjects || 0,\n      averageDailyRate: profile.averageDailyRate || 0,\n      totalRevenue: this.calculateTotalRevenue(),\n      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n    };\n  }\n  calculateTotalRevenue() {\n    return this.recentProjects.reduce((total, project) => {\n      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n        const monthlyDays = project.daysPerYear / 12;\n        return total + project.dailyRate * monthlyDays * project.durationInMonths;\n      }\n      return total;\n    }, 0);\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [CommonModule, RouterModule, MatToolbarModule, MatSidenavModule, MatListModule, MatIconModule, MatButtonModule, MatCardModule, MatMenuModule],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})], DashboardComponent);\nexport { DashboardComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "MatIconModule", "MatButtonModule", "MatCardModule", "DashboardComponent", "constructor", "authService", "projectService", "freelanceService", "freelanceProfile", "recentProjects", "stats", "totalProjects", "averageDailyRate", "totalRevenue", "activeProjects", "menuItems", "icon", "label", "route", "ngOnInit", "currentUser", "getUser", "loadDashboardData", "id", "getByIdWithProjects", "subscribe", "next", "profile", "updateStats", "error", "console", "getByFreelanceId", "projects", "slice", "calculateTotalRevenue", "filter", "p", "startDate", "Date", "length", "reduce", "total", "project", "dailyRate", "durationInMonths", "daysPerYear", "monthlyDays", "logout", "getGreeting", "hour", "getHours", "__decorate", "selector", "standalone", "imports", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatMenuModule", "templateUrl", "styleUrls"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { AuthService } from '../../services/auth.service';\nimport { ProjectService, ProjectDto } from '../../services/project.service';\nimport { FreelanceService, FreelanceDto } from '../../services/freelance.service';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatCardModule,\n    MatMenuModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any;\n  freelanceProfile: FreelanceDto | null = null;\n  recentProjects: ProjectDto[] = [];\n  stats = {\n    totalProjects: 0,\n    averageDailyRate: 0,\n    totalRevenue: 0,\n    activeProjects: 0\n  };\n\n  menuItems = [\n    { icon: 'dashboard', label: 'Tableau de bord', route: '/dashboard' },\n    { icon: 'work', label: 'Projets', route: '/projects' },\n    { icon: 'business', label: 'Clients', route: '/clients' },\n    { icon: 'contacts', label: 'Contacts', route: '/contacts' },\n    { icon: 'source', label: 'Sources', route: '/sources' },\n    { icon: 'person', label: 'Profil', route: '/profile' }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private projectService: ProjectService,\n    private freelanceService: FreelanceService\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getUser();\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    if (this.currentUser?.id) {\n      // Load freelance profile\n      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n        next: (profile) => {\n          this.freelanceProfile = profile;\n          this.updateStats(profile);\n        },\n        error: (error) => console.error('Error loading profile:', error)\n      });\n\n      // Load recent projects\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: (projects) => {\n          this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n        },\n        error: (error) => console.error('Error loading projects:', error)\n      });\n    }\n  }\n\n  updateStats(profile: FreelanceDto): void {\n    this.stats = {\n      totalProjects: profile.totalProjects || 0,\n      averageDailyRate: profile.averageDailyRate || 0,\n      totalRevenue: this.calculateTotalRevenue(),\n      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n    };\n  }\n\n  calculateTotalRevenue(): number {\n    return this.recentProjects.reduce((total, project) => {\n      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n        const monthlyDays = project.daysPerYear / 12;\n        return total + (project.dailyRate * monthlyDays * project.durationInMonths);\n      }\n      return total;\n    }, 0);\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AAsB/C,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAoB7BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,gBAAkC;IAFlC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IArB1B,KAAAC,gBAAgB,GAAwB,IAAI;IAC5C,KAAAC,cAAc,GAAiB,EAAE;IACjC,KAAAC,KAAK,GAAG;MACNC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE;KACjB;IAED,KAAAC,SAAS,GAAG,CACV;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAY,CAAE,EACpE;MAAEF,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtD;MAAEF,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAE,EACzD;MAAEF,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC3D;MAAEF,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAE,EACvD;MAAEF,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAU,CAAE,CACvD;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACf,WAAW,CAACgB,OAAO,EAAE;IAC7C,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACF,WAAW,EAAEG,EAAE,EAAE;MACxB;MACA,IAAI,CAAChB,gBAAgB,CAACiB,mBAAmB,CAAC,IAAI,CAACJ,WAAW,CAACG,EAAE,CAAC,CAACE,SAAS,CAAC;QACvEC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAI,CAACnB,gBAAgB,GAAGmB,OAAO;UAC/B,IAAI,CAACC,WAAW,CAACD,OAAO,CAAC;QAC3B,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK;OAChE,CAAC;MAEF;MACA,IAAI,CAACvB,cAAc,CAACyB,gBAAgB,CAAC,IAAI,CAACX,WAAW,CAACG,EAAE,CAAC,CAACE,SAAS,CAAC;QAClEC,IAAI,EAAGM,QAAQ,IAAI;UACjB,IAAI,CAACvB,cAAc,GAAGuB,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACDJ,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;OACjE,CAAC;;EAEN;EAEAD,WAAWA,CAACD,OAAqB;IAC/B,IAAI,CAACjB,KAAK,GAAG;MACXC,aAAa,EAAEgB,OAAO,CAAChB,aAAa,IAAI,CAAC;MACzCC,gBAAgB,EAAEe,OAAO,CAACf,gBAAgB,IAAI,CAAC;MAC/CC,YAAY,EAAE,IAAI,CAACqB,qBAAqB,EAAE;MAC1CpB,cAAc,EAAE,IAAI,CAACL,cAAc,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,IAAI,IAAIC,IAAI,CAACF,CAAC,CAACC,SAAS,CAAC,IAAI,IAAIC,IAAI,EAAE,CAAC,CAACC;KACrG;EACH;EAEAL,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACzB,cAAc,CAAC+B,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAI;MACnD,IAAIA,OAAO,CAACC,SAAS,IAAID,OAAO,CAACE,gBAAgB,IAAIF,OAAO,CAACG,WAAW,EAAE;QACxE,MAAMC,WAAW,GAAGJ,OAAO,CAACG,WAAW,GAAG,EAAE;QAC5C,OAAOJ,KAAK,GAAIC,OAAO,CAACC,SAAS,GAAGG,WAAW,GAAGJ,OAAO,CAACE,gBAAiB;;MAE7E,OAAOH,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;EACP;EAEAM,MAAMA,CAAA;IACJ,IAAI,CAAC1C,WAAW,CAAC0C,MAAM,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAG,IAAIX,IAAI,EAAE,CAACY,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;CACD;AAjFY9C,kBAAkB,GAAAgD,UAAA,EAjB9BtD,SAAS,CAAC;EACTuD,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPxD,YAAY,EACZC,YAAY,EACZwD,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbzD,aAAa,EACbC,eAAe,EACfC,aAAa,EACbwD,aAAa,CACd;EACDC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACWzD,kBAAkB,CAiF9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}