{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { routes } from './app.routes';\nimport { authInterceptor } from './interceptors/auth.interceptor';\nimport { errorInterceptor } from './interceptors/error.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])), provideAnimations(), importProvidersFrom(MatSnackBarModule, MatDialogModule)]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}