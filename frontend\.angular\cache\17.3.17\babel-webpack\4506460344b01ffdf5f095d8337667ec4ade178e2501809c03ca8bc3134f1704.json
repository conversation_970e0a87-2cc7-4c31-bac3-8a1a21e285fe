{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/client.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/table\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/chips\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nconst _c0 = a0 => [\"/clients\", a0];\nconst _c1 = a0 => [\"/clients\", a0, \"edit\"];\nfunction ClientListComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des clients...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientListComponent_div_37_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" Essayez de modifier vos crit\\u00E8res de recherche ou de filtrage. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_37_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" Commencez par ajouter votre premier client. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"business_center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Aucun client trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ClientListComponent_div_37_p_5_Template, 2, 0, \"p\", 20)(6, ClientListComponent_div_37_p_6_Template, 2, 0, \"p\", 20);\n    i0.ɵɵelementStart(7, \"button\", 13)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Ajouter un Client \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchQuery || ctx_r0.statusFilter !== \"ALL\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.searchQuery && ctx_r0.statusFilter === \"ALL\");\n  }\n}\nfunction ClientListComponent_div_38_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"Nom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_4_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\")(1, \"a\", 37)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"link\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const client_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", client_r2.website, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ClientListComponent_div_38_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 35)(1, \"div\", 36)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClientListComponent_div_38_td_4_small_4_Template, 4, 1, \"small\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(client_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", client_r2.website);\n  }\n}\nfunction ClientListComponent_div_38_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"Contact\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(client_r3.contactPerson);\n  }\n}\nfunction ClientListComponent_div_38_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 35)(1, \"a\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"mailto:\" + client_r4.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(client_r4.email);\n  }\n}\nfunction ClientListComponent_div_38_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 35)(1, \"a\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"tel:\" + client_r5.phone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(client_r5.phone);\n  }\n}\nfunction ClientListComponent_div_38_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"Secteur\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(client_r6.industry);\n  }\n}\nfunction ClientListComponent_div_38_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"Statut\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 35)(1, \"mat-chip\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r0.getStatusColor(client_r7.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getStatusLabel(client_r7.status), \" \");\n  }\n}\nfunction ClientListComponent_div_38_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 34);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientListComponent_div_38_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 35)(1, \"div\", 41)(2, \"button\", 42)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 43)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ClientListComponent_div_38_td_22_Template_button_click_8_listener() {\n      const client_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.deleteClient(client_r9));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const client_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c0, client_r9.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, client_r9.id));\n  }\n}\nfunction ClientListComponent_div_38_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 45);\n  }\n}\nfunction ClientListComponent_div_38_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 46);\n  }\n}\nfunction ClientListComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"table\", 22);\n    i0.ɵɵelementContainerStart(2, 23);\n    i0.ɵɵtemplate(3, ClientListComponent_div_38_th_3_Template, 2, 0, \"th\", 24)(4, ClientListComponent_div_38_td_4_Template, 5, 2, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 26);\n    i0.ɵɵtemplate(6, ClientListComponent_div_38_th_6_Template, 2, 0, \"th\", 24)(7, ClientListComponent_div_38_td_7_Template, 2, 1, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 27);\n    i0.ɵɵtemplate(9, ClientListComponent_div_38_th_9_Template, 2, 0, \"th\", 24)(10, ClientListComponent_div_38_td_10_Template, 3, 2, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 28);\n    i0.ɵɵtemplate(12, ClientListComponent_div_38_th_12_Template, 2, 0, \"th\", 24)(13, ClientListComponent_div_38_td_13_Template, 3, 2, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 29);\n    i0.ɵɵtemplate(15, ClientListComponent_div_38_th_15_Template, 2, 0, \"th\", 24)(16, ClientListComponent_div_38_td_16_Template, 2, 1, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 30);\n    i0.ɵɵtemplate(18, ClientListComponent_div_38_th_18_Template, 2, 0, \"th\", 24)(19, ClientListComponent_div_38_td_19_Template, 3, 2, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 31);\n    i0.ɵɵtemplate(21, ClientListComponent_div_38_th_21_Template, 2, 0, \"th\", 24)(22, ClientListComponent_div_38_td_22_Template, 11, 6, \"td\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(23, ClientListComponent_div_38_tr_23_Template, 1, 0, \"tr\", 32)(24, ClientListComponent_div_38_tr_24_Template, 1, 0, \"tr\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r0.filteredClients);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r0.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r0.displayedColumns);\n  }\n}\nexport class ClientListComponent {\n  constructor(clientService, dialog, snackBar) {\n    this.clientService = clientService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.clients = [];\n    this.filteredClients = [];\n    this.displayedColumns = ['name', 'contactPerson', 'email', 'phone', 'industry', 'status', 'actions'];\n    this.searchQuery = '';\n    this.statusFilter = 'ALL';\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n    this.searchSubject = new Subject();\n    // Setup search debouncing\n    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(query => {\n      this.performSearch(query);\n    });\n  }\n  ngOnInit() {\n    this.loadClients();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadClients() {\n    this.isLoading = true;\n    this.clientService.getClients().pipe(takeUntil(this.destroy$)).subscribe({\n      next: clients => {\n        this.clients = clients;\n        this.applyFilters();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading clients:', error);\n        this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onSearchChange(query) {\n    this.searchQuery = query;\n    this.searchSubject.next(query);\n  }\n  onStatusFilterChange() {\n    this.applyFilters();\n  }\n  performSearch(query) {\n    if (query.trim()) {\n      this.clientService.searchClients(query).pipe(takeUntil(this.destroy$)).subscribe(clients => {\n        this.filteredClients = this.filterByStatus(clients);\n      });\n    } else {\n      this.applyFilters();\n    }\n  }\n  applyFilters() {\n    this.filteredClients = this.filterByStatus(this.clients);\n  }\n  filterByStatus(clients) {\n    if (this.statusFilter === 'ALL') {\n      return clients;\n    }\n    return clients.filter(client => client.status === this.statusFilter);\n  }\n  deleteClient(client) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.name}\" ?`)) {\n      this.clientService.deleteClient(client.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.loadClients();\n        },\n        error: error => {\n          console.error('Error deleting client:', error);\n          this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n  getStatusLabel(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n  static {\n    this.ɵfac = function ClientListComponent_Factory(t) {\n      return new (t || ClientListComponent)(i0.ɵɵdirectiveInject(i1.ClientService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClientListComponent,\n      selectors: [[\"app-client-list\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 39,\n      vars: 5,\n      consts: [[1, \"client-list-container\"], [1, \"header-card\"], [1, \"actions-row\"], [1, \"search-filters\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Nom, email, contact, secteur...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"status-filter\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [\"value\", \"ALL\"], [\"value\", \"ACTIVE\"], [\"value\", \"INACTIVE\"], [\"value\", \"PROSPECT\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/clients/create\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"no-data\"], [4, \"ngIf\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"clients-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"contactPerson\"], [\"matColumnDef\", \"email\"], [\"matColumnDef\", \"phone\"], [\"matColumnDef\", \"industry\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"client-name\"], [\"target\", \"_blank\", 1, \"website-link\", 3, \"href\"], [1, \"email-link\", 3, \"href\"], [1, \"phone-link\", 3, \"href\"], [\"selected\", \"\", 3, \"color\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Voir les d\\u00E9tails\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Modifier\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Supprimer\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function ClientListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Gestion des Clients \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" G\\u00E9rez vos clients et prospects \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 2)(11, \"div\", 3)(12, \"mat-form-field\", 4)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Rechercher un client\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ClientListComponent_Template_input_ngModelChange_15_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function ClientListComponent_Template_input_input_15_listener($event) {\n            return ctx.onSearchChange(($event.target == null ? null : $event.target.value) || \"\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"mat-icon\", 6);\n          i0.ɵɵtext(17, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 7)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ClientListComponent_Template_mat_select_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.statusFilter, $event) || (ctx.statusFilter = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function ClientListComponent_Template_mat_select_selectionChange_21_listener() {\n            return ctx.onStatusFilterChange();\n          });\n          i0.ɵɵelementStart(22, \"mat-option\", 9);\n          i0.ɵɵtext(23, \"Tous\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-option\", 10);\n          i0.ɵɵtext(25, \"Actifs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-option\", 11);\n          i0.ɵɵtext(27, \"Inactifs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-option\", 12);\n          i0.ɵɵtext(29, \"Prospects\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"button\", 13)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" Nouveau Client \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(34, \"mat-card\", 14)(35, \"mat-card-content\");\n          i0.ɵɵtemplate(36, ClientListComponent_div_36_Template, 4, 0, \"div\", 15)(37, ClientListComponent_div_37_Template, 11, 2, \"div\", 16)(38, ClientListComponent_div_38_Template, 25, 3, \"div\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.statusFilter);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredClients.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredClients.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgIf, RouterModule, i5.RouterLink, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatCardModule, i10.MatCard, i10.MatCardContent, i10.MatCardHeader, i10.MatCardSubtitle, i10.MatCardTitle, MatChipsModule, i11.MatChip, MatInputModule, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, MatFormFieldModule, MatSelectModule, i14.MatSelect, i15.MatOption, MatDialogModule, MatProgressSpinnerModule, i16.MatProgressSpinner],\n      styles: [\".client-list-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 1.5rem;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-end;\\n  gap: 20px;\\n  margin-top: 16px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  flex: 1;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 400px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .status-filter[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n}\\n@media (max-width: 768px) {\\n  .client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%], .client-list-container[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   .actions-row[_ngcontent-%COMP%]   .search-filters[_ngcontent-%COMP%]   .status-filter[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: rgba(0, 0, 0, 0.3);\\n  margin-bottom: 16px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n  text-decoration: none;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-name[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]:hover {\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .email-link[_ngcontent-%COMP%], .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .phone-link[_ngcontent-%COMP%] {\\n  color: var(--mdc-theme-primary, #1976d2);\\n  text-decoration: none;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .email-link[_ngcontent-%COMP%]:hover, .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .phone-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 40px;\\n  width: 40px;\\n  height: 40px;\\n}\\n.client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n@media (max-width: 768px) {\\n  .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .mat-column-phone[_ngcontent-%COMP%], .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .mat-column-industry[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .client-list-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .mat-column-contactPerson[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.87);\\n}\\n.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n\\n.mat-mdc-chip[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  min-height: 24px;\\n}\\n.mat-mdc-chip.mat-primary[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n.mat-mdc-chip.mat-accent[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #f57c00;\\n}\\n.mat-mdc-chip.mat-warn[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatTableModule", "MatButtonModule", "MatIconModule", "MatCardModule", "MatChipsModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDialogModule", "MatProgressSpinnerModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ClientListComponent_div_37_p_5_Template", "ClientListComponent_div_37_p_6_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "searchQuery", "statusFilter", "client_r2", "website", "ɵɵsanitizeUrl", "ClientListComponent_div_38_td_4_small_4_Template", "ɵɵtextInterpolate", "name", "client_r3", "<PERSON><PERSON><PERSON>", "client_r4", "email", "client_r5", "phone", "client_r6", "industry", "getStatusColor", "client_r7", "status", "ɵɵtextInterpolate1", "getStatusLabel", "ɵɵlistener", "ClientListComponent_div_38_td_22_Template_button_click_8_listener", "client_r9", "ɵɵrestoreView", "_r8", "$implicit", "ɵɵnextContext", "ɵɵresetView", "deleteClient", "ɵɵpureFunction1", "_c0", "id", "_c1", "ɵɵelementContainerStart", "ClientListComponent_div_38_th_3_Template", "ClientListComponent_div_38_td_4_Template", "ClientListComponent_div_38_th_6_Template", "ClientListComponent_div_38_td_7_Template", "ClientListComponent_div_38_th_9_Template", "ClientListComponent_div_38_td_10_Template", "ClientListComponent_div_38_th_12_Template", "ClientListComponent_div_38_td_13_Template", "ClientListComponent_div_38_th_15_Template", "ClientListComponent_div_38_td_16_Template", "ClientListComponent_div_38_th_18_Template", "ClientListComponent_div_38_td_19_Template", "ClientListComponent_div_38_th_21_Template", "ClientListComponent_div_38_td_22_Template", "ClientListComponent_div_38_tr_23_Template", "ClientListComponent_div_38_tr_24_Template", "filteredClients", "displayedColumns", "ClientListComponent", "constructor", "clientService", "dialog", "snackBar", "clients", "isLoading", "destroy$", "searchSubject", "pipe", "subscribe", "query", "performSearch", "ngOnInit", "loadClients", "ngOnDestroy", "next", "complete", "getClients", "applyFilters", "error", "console", "open", "duration", "onSearchChange", "onStatusFilterChange", "trim", "searchClients", "filterByStatus", "filter", "client", "confirm", "ɵɵdirectiveInject", "i1", "ClientService", "i2", "MatDialog", "i3", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ClientListComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ClientListComponent_Template_input_ngModelChange_15_listener", "$event", "ɵɵtwoWayBindingSet", "ClientListComponent_Template_input_input_15_listener", "target", "value", "ClientListComponent_Template_mat_select_ngModelChange_21_listener", "ClientListComponent_Template_mat_select_selectionChange_21_listener", "ClientListComponent_div_36_Template", "ClientListComponent_div_37_Template", "ClientListComponent_div_38_Template", "ɵɵtwoWayProperty", "length", "i4", "NgIf", "i5", "RouterLink", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i7", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i11", "MatChip", "i12", "MatInput", "i13", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i14", "MatSelect", "i15", "MatOption", "i16", "MatProgressSpinner", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-list\\client-list.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-list\\client-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\n\nimport { ClientService, ClientDto } from '../../../services/client.service';\n\n@Component({\n  selector: 'app-client-list',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    FormsModule,\n    MatTableModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatChipsModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDialogModule,\n\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './client-list.component.html',\n  styleUrls: ['./client-list.component.scss']\n})\nexport class ClientListComponent implements OnInit, OnDestroy {\n  clients: ClientDto[] = [];\n  filteredClients: ClientDto[] = [];\n  displayedColumns: string[] = ['name', 'contactPerson', 'email', 'phone', 'industry', 'status', 'actions'];\n  \n  searchQuery = '';\n  statusFilter = 'ALL';\n  isLoading = false;\n  \n  private destroy$ = new Subject<void>();\n  private searchSubject = new Subject<string>();\n\n  constructor(\n    private clientService: ClientService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {\n    // Setup search debouncing\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      takeUntil(this.destroy$)\n    ).subscribe(query => {\n      this.performSearch(query);\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadClients();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadClients(): void {\n    this.isLoading = true;\n    this.clientService.getClients()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (clients) => {\n          this.clients = clients;\n          this.applyFilters();\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading clients:', error);\n          this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onSearchChange(query: string): void {\n    this.searchQuery = query;\n    this.searchSubject.next(query);\n  }\n\n  onStatusFilterChange(): void {\n    this.applyFilters();\n  }\n\n  private performSearch(query: string): void {\n    if (query.trim()) {\n      this.clientService.searchClients(query)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(clients => {\n          this.filteredClients = this.filterByStatus(clients);\n        });\n    } else {\n      this.applyFilters();\n    }\n  }\n\n  private applyFilters(): void {\n    this.filteredClients = this.filterByStatus(this.clients);\n  }\n\n  private filterByStatus(clients: ClientDto[]): ClientDto[] {\n    if (this.statusFilter === 'ALL') {\n      return clients;\n    }\n    return clients.filter(client => client.status === this.statusFilter);\n  }\n\n  deleteClient(client: ClientDto): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.name}\" ?`)) {\n      this.clientService.deleteClient(client.id)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.loadClients();\n          },\n          error: (error) => {\n            console.error('Error deleting client:', error);\n            this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n              duration: 3000\n            });\n          }\n        });\n    }\n  }\n\n  getStatusColor(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n\n  getStatusLabel(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n}\n", "<div class=\"client-list-container\">\n  <mat-card class=\"header-card\">\n    <mat-card-header>\n      <mat-card-title>\n        <mat-icon>business</mat-icon>\n        Gestion des Clients\n      </mat-card-title>\n      <mat-card-subtitle>\n        Gérez vos clients et prospects\n      </mat-card-subtitle>\n    </mat-card-header>\n    \n    <mat-card-content>\n      <div class=\"actions-row\">\n        <div class=\"search-filters\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>Rechercher un client</mat-label>\n            <input matInput \n                   [(ngModel)]=\"searchQuery\"\n                   (input)=\"onSearchChange($event.target?.value || '')\"\n                   placeholder=\"Nom, email, contact, secteur...\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n          \n          <mat-form-field appearance=\"outline\" class=\"status-filter\">\n            <mat-label>Statut</mat-label>\n            <mat-select [(ngModel)]=\"statusFilter\" (selectionChange)=\"onStatusFilterChange()\">\n              <mat-option value=\"ALL\">Tous</mat-option>\n              <mat-option value=\"ACTIVE\">Actifs</mat-option>\n              <mat-option value=\"INACTIVE\">Inactifs</mat-option>\n              <mat-option value=\"PROSPECT\">Prospects</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n        \n        <button mat-raised-button color=\"primary\" routerLink=\"/clients/create\">\n          <mat-icon>add</mat-icon>\n          Nouveau Client\n        </button>\n      </div>\n    </mat-card-content>\n  </mat-card>\n\n  <mat-card class=\"table-card\">\n    <mat-card-content>\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner></mat-spinner>\n        <p>Chargement des clients...</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && filteredClients.length === 0\" class=\"no-data\">\n        <mat-icon>business_center</mat-icon>\n        <h3>Aucun client trouvé</h3>\n        <p *ngIf=\"searchQuery || statusFilter !== 'ALL'\">\n          Essayez de modifier vos critères de recherche ou de filtrage.\n        </p>\n        <p *ngIf=\"!searchQuery && statusFilter === 'ALL'\">\n          Commencez par ajouter votre premier client.\n        </p>\n        <button mat-raised-button color=\"primary\" routerLink=\"/clients/create\">\n          <mat-icon>add</mat-icon>\n          Ajouter un Client\n        </button>\n      </div>\n\n      <div *ngIf=\"!isLoading && filteredClients.length > 0\" class=\"table-container\">\n        <table mat-table [dataSource]=\"filteredClients\" class=\"clients-table\">\n          <!-- Name Column -->\n          <ng-container matColumnDef=\"name\">\n            <th mat-header-cell *matHeaderCellDef>Nom</th>\n            <td mat-cell *matCellDef=\"let client\">\n              <div class=\"client-name\">\n                <strong>{{ client.name }}</strong>\n                <small *ngIf=\"client.website\">\n                  <a [href]=\"client.website\" target=\"_blank\" class=\"website-link\">\n                    <mat-icon>link</mat-icon>\n                  </a>\n                </small>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Contact Person Column -->\n          <ng-container matColumnDef=\"contactPerson\">\n            <th mat-header-cell *matHeaderCellDef>Contact</th>\n            <td mat-cell *matCellDef=\"let client\">{{ client.contactPerson }}</td>\n          </ng-container>\n\n          <!-- Email Column -->\n          <ng-container matColumnDef=\"email\">\n            <th mat-header-cell *matHeaderCellDef>Email</th>\n            <td mat-cell *matCellDef=\"let client\">\n              <a [href]=\"'mailto:' + client.email\" class=\"email-link\">{{ client.email }}</a>\n            </td>\n          </ng-container>\n\n          <!-- Phone Column -->\n          <ng-container matColumnDef=\"phone\">\n            <th mat-header-cell *matHeaderCellDef>Téléphone</th>\n            <td mat-cell *matCellDef=\"let client\">\n              <a [href]=\"'tel:' + client.phone\" class=\"phone-link\">{{ client.phone }}</a>\n            </td>\n          </ng-container>\n\n          <!-- Industry Column -->\n          <ng-container matColumnDef=\"industry\">\n            <th mat-header-cell *matHeaderCellDef>Secteur</th>\n            <td mat-cell *matCellDef=\"let client\">{{ client.industry }}</td>\n          </ng-container>\n\n          <!-- Status Column -->\n          <ng-container matColumnDef=\"status\">\n            <th mat-header-cell *matHeaderCellDef>Statut</th>\n            <td mat-cell *matCellDef=\"let client\">\n              <mat-chip [color]=\"getStatusColor(client.status)\" selected>\n                {{ getStatusLabel(client.status) }}\n              </mat-chip>\n            </td>\n          </ng-container>\n\n          <!-- Actions Column -->\n          <ng-container matColumnDef=\"actions\">\n            <th mat-header-cell *matHeaderCellDef>Actions</th>\n            <td mat-cell *matCellDef=\"let client\">\n              <div class=\"action-buttons\">\n                <button mat-icon-button \n                        [routerLink]=\"['/clients', client.id]\"\n                        matTooltip=\"Voir les détails\">\n                  <mat-icon>visibility</mat-icon>\n                </button>\n                <button mat-icon-button \n                        [routerLink]=\"['/clients', client.id, 'edit']\"\n                        matTooltip=\"Modifier\">\n                  <mat-icon>edit</mat-icon>\n                </button>\n                <button mat-icon-button \n                        color=\"warn\"\n                        (click)=\"deleteClient(client)\"\n                        matTooltip=\"Supprimer\">\n                  <mat-icon>delete</mat-icon>\n                </button>\n              </div>\n            </td>\n          </ng-container>\n\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n        </table>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAmB,0BAA0B;AAErE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;IC8BvEC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;IAKJJ,EAAA,CAAAC,cAAA,QAAiD;IAC/CD,EAAA,CAAAG,MAAA,2EACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IACJJ,EAAA,CAAAC,cAAA,QAAkD;IAChDD,EAAA,CAAAG,MAAA,oDACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAPJJ,EADF,CAAAC,cAAA,cAAwE,eAC5D;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,+BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI5BJ,EAHA,CAAAK,UAAA,IAAAC,uCAAA,gBAAiD,IAAAC,uCAAA,gBAGC;IAIhDP,EADF,CAAAC,cAAA,iBAAuE,eAC3D;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACxBJ,EAAA,CAAAG,MAAA,2BACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IAVAJ,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,YAAA,WAA2C;IAG3CZ,EAAA,CAAAQ,SAAA,EAA4C;IAA5CR,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,YAAA,WAA4C;;;;;IAa5CZ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAMtCJ,EAFJ,CAAAC,cAAA,YAA8B,YACoC,eACpD;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAElBH,EAFkB,CAAAI,YAAA,EAAW,EACvB,EACE;;;;IAHHJ,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAS,UAAA,SAAAI,SAAA,CAAAC,OAAA,EAAAd,EAAA,CAAAe,aAAA,CAAuB;;;;;IAF5Bf,EAFJ,CAAAC,cAAA,aAAsC,cACX,aACf;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClCJ,EAAA,CAAAK,UAAA,IAAAW,gDAAA,oBAA8B;IAMlChB,EADE,CAAAI,YAAA,EAAM,EACH;;;;IAPOJ,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAiB,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,CAAiB;IACjBlB,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAS,UAAA,SAAAI,SAAA,CAAAC,OAAA,CAAoB;;;;;IAWhCd,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAClDJ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA/BJ,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAiB,iBAAA,CAAAE,SAAA,CAAAC,aAAA,CAA0B;;;;;IAKhEpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAE9CJ,EADF,CAAAC,cAAA,aAAsC,YACoB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAC5EH,EAD4E,CAAAI,YAAA,EAAI,EAC3E;;;;IADAJ,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAS,UAAA,qBAAAY,SAAA,CAAAC,KAAA,EAAAtB,EAAA,CAAAe,aAAA,CAAiC;IAAoBf,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAiB,iBAAA,CAAAI,SAAA,CAAAC,KAAA,CAAkB;;;;;IAM5EtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,0BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAElDJ,EADF,CAAAC,cAAA,aAAsC,YACiB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IACzEH,EADyE,CAAAI,YAAA,EAAI,EACxE;;;;IADAJ,EAAA,CAAAQ,SAAA,EAA8B;IAA9BR,EAAA,CAAAS,UAAA,kBAAAc,SAAA,CAAAC,KAAA,EAAAxB,EAAA,CAAAe,aAAA,CAA8B;IAAoBf,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAiB,iBAAA,CAAAM,SAAA,CAAAC,KAAA,CAAkB;;;;;IAMzExB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAClDJ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA1BJ,EAAA,CAAAQ,SAAA,EAAqB;IAArBR,EAAA,CAAAiB,iBAAA,CAAAQ,SAAA,CAAAC,QAAA,CAAqB;;;;;IAK3D1B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAE/CJ,EADF,CAAAC,cAAA,aAAsC,mBACuB;IACzDD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAW,EACR;;;;;IAHOJ,EAAA,CAAAQ,SAAA,EAAuC;IAAvCR,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAiB,cAAA,CAAAC,SAAA,CAAAC,MAAA,EAAuC;IAC/C7B,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA8B,kBAAA,MAAApB,MAAA,CAAAqB,cAAA,CAAAH,SAAA,CAAAC,MAAA,OACF;;;;;IAMF7B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAM5CJ,EALN,CAAAC,cAAA,aAAsC,cACR,iBAGY,eAC1B;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IACtBH,EADsB,CAAAI,YAAA,EAAW,EACxB;IAIPJ,EAHF,CAAAC,cAAA,iBAE8B,eAClB;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAChBH,EADgB,CAAAI,YAAA,EAAW,EAClB;IACTJ,EAAA,CAAAC,cAAA,iBAG+B;IADvBD,EAAA,CAAAgC,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,SAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAV,EAAA,CAAAsC,aAAA;MAAA,OAAAtC,EAAA,CAAAuC,WAAA,CAAS7B,MAAA,CAAA8B,YAAA,CAAAN,SAAA,CAAoB;IAAA,EAAC;IAEpClC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAGtBH,EAHsB,CAAAI,YAAA,EAAW,EACpB,EACL,EACH;;;;IAhBOJ,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAS,UAAA,eAAAT,EAAA,CAAAyC,eAAA,IAAAC,GAAA,EAAAR,SAAA,CAAAS,EAAA,EAAsC;IAKtC3C,EAAA,CAAAQ,SAAA,GAA8C;IAA9CR,EAAA,CAAAS,UAAA,eAAAT,EAAA,CAAAyC,eAAA,IAAAG,GAAA,EAAAV,SAAA,CAAAS,EAAA,EAA8C;;;;;IAc5D3C,EAAA,CAAAE,SAAA,aAA4D;;;;;IAC5DF,EAAA,CAAAE,SAAA,aAAkE;;;;;IAhFpEF,EADF,CAAAC,cAAA,cAA8E,gBACN;IAEpED,EAAA,CAAA6C,uBAAA,OAAkC;IAEhC7C,EADA,CAAAK,UAAA,IAAAyC,wCAAA,iBAAsC,IAAAC,wCAAA,iBACA;;IAaxC/C,EAAA,CAAA6C,uBAAA,OAA2C;IAEzC7C,EADA,CAAAK,UAAA,IAAA2C,wCAAA,iBAAsC,IAAAC,wCAAA,iBACA;;IAIxCjD,EAAA,CAAA6C,uBAAA,OAAmC;IAEjC7C,EADA,CAAAK,UAAA,IAAA6C,wCAAA,iBAAsC,KAAAC,yCAAA,iBACA;;IAMxCnD,EAAA,CAAA6C,uBAAA,QAAmC;IAEjC7C,EADA,CAAAK,UAAA,KAAA+C,yCAAA,iBAAsC,KAAAC,yCAAA,iBACA;;IAMxCrD,EAAA,CAAA6C,uBAAA,QAAsC;IAEpC7C,EADA,CAAAK,UAAA,KAAAiD,yCAAA,iBAAsC,KAAAC,yCAAA,iBACA;;IAIxCvD,EAAA,CAAA6C,uBAAA,QAAoC;IAElC7C,EADA,CAAAK,UAAA,KAAAmD,yCAAA,iBAAsC,KAAAC,yCAAA,iBACA;;IAQxCzD,EAAA,CAAA6C,uBAAA,QAAqC;IAEnC7C,EADA,CAAAK,UAAA,KAAAqD,yCAAA,iBAAsC,KAAAC,yCAAA,kBACA;;IAuBxC3D,EADA,CAAAK,UAAA,KAAAuD,yCAAA,iBAAuD,KAAAC,yCAAA,iBACM;IAEjE7D,EADE,CAAAI,YAAA,EAAQ,EACJ;;;;IAlFaJ,EAAA,CAAAQ,SAAA,EAA8B;IAA9BR,EAAA,CAAAS,UAAA,eAAAC,MAAA,CAAAoD,eAAA,CAA8B;IA+EzB9D,EAAA,CAAAQ,SAAA,IAAiC;IAAjCR,EAAA,CAAAS,UAAA,oBAAAC,MAAA,CAAAqD,gBAAA,CAAiC;IACpB/D,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAS,UAAA,qBAAAC,MAAA,CAAAqD,gBAAA,CAA0B;;;ADzGrE,OAAM,MAAOC,mBAAmB;EAY9BC,YACUC,aAA4B,EAC5BC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,OAAO,GAAgB,EAAE;IACzB,KAAAP,eAAe,GAAgB,EAAE;IACjC,KAAAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC;IAEzG,KAAApD,WAAW,GAAG,EAAE;IAChB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAA0D,SAAS,GAAG,KAAK;IAET,KAAAC,QAAQ,GAAG,IAAI3E,OAAO,EAAQ;IAC9B,KAAA4E,aAAa,GAAG,IAAI5E,OAAO,EAAU;IAO3C;IACA,IAAI,CAAC4E,aAAa,CAACC,IAAI,CACrB3E,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CACzB,CAACG,SAAS,CAACC,KAAK,IAAG;MAClB,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEAH,WAAWA,CAAA;IACT,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,aAAa,CAACgB,UAAU,EAAE,CAC5BT,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAC;MACTM,IAAI,EAAGX,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACc,YAAY,EAAE;QACnB,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,QAAQ,CAACkB,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;UACpEC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACjB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAkB,cAAcA,CAACb,KAAa;IAC1B,IAAI,CAAChE,WAAW,GAAGgE,KAAK;IACxB,IAAI,CAACH,aAAa,CAACQ,IAAI,CAACL,KAAK,CAAC;EAChC;EAEAc,oBAAoBA,CAAA;IAClB,IAAI,CAACN,YAAY,EAAE;EACrB;EAEQP,aAAaA,CAACD,KAAa;IACjC,IAAIA,KAAK,CAACe,IAAI,EAAE,EAAE;MAChB,IAAI,CAACxB,aAAa,CAACyB,aAAa,CAAChB,KAAK,CAAC,CACpCF,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACL,OAAO,IAAG;QACnB,IAAI,CAACP,eAAe,GAAG,IAAI,CAAC8B,cAAc,CAACvB,OAAO,CAAC;MACrD,CAAC,CAAC;KACL,MAAM;MACL,IAAI,CAACc,YAAY,EAAE;;EAEvB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACrB,eAAe,GAAG,IAAI,CAAC8B,cAAc,CAAC,IAAI,CAACvB,OAAO,CAAC;EAC1D;EAEQuB,cAAcA,CAACvB,OAAoB;IACzC,IAAI,IAAI,CAACzD,YAAY,KAAK,KAAK,EAAE;MAC/B,OAAOyD,OAAO;;IAEhB,OAAOA,OAAO,CAACwB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACjE,MAAM,KAAK,IAAI,CAACjB,YAAY,CAAC;EACtE;EAEA4B,YAAYA,CAACsD,MAAiB;IAC5B,IAAIC,OAAO,CAAC,iDAAiDD,MAAM,CAAC5E,IAAI,KAAK,CAAC,EAAE;MAC9E,IAAI,CAACgD,aAAa,CAAC1B,YAAY,CAACsD,MAAM,CAACnD,EAAE,CAAC,CACvC8B,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAC;QACTM,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACZ,QAAQ,CAACkB,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;YAC1DC,QAAQ,EAAE;WACX,CAAC;UACF,IAAI,CAACT,WAAW,EAAE;QACpB,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAAChB,QAAQ,CAACkB,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;YACtEC,QAAQ,EAAE;WACX,CAAC;QACJ;OACD,CAAC;;EAER;EAEA5D,cAAcA,CAACE,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,UAAU;QACb,OAAO,QAAQ;MACjB;QACE,OAAO,EAAE;;EAEf;EAEAE,cAAcA,CAACF,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB;QACE,OAAOA,MAAM;;EAEnB;;;uBArIWmC,mBAAmB,EAAAhE,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAApG,EAAA,CAAAgG,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBtC,mBAAmB;MAAAuC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzG,EAAA,CAAA0G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrCxBhH,EAJR,CAAAC,cAAA,aAAmC,kBACH,sBACX,qBACC,eACJ;UAAAD,EAAA,CAAAG,MAAA,eAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC7BJ,EAAA,CAAAG,MAAA,4BACF;UAAAH,EAAA,CAAAI,YAAA,EAAiB;UACjBJ,EAAA,CAAAC,cAAA,wBAAmB;UACjBD,EAAA,CAAAG,MAAA,4CACF;UACFH,EADE,CAAAI,YAAA,EAAoB,EACJ;UAMVJ,EAJR,CAAAC,cAAA,uBAAkB,cACS,cACK,yBACgC,iBAC7C;UAAAD,EAAA,CAAAG,MAAA,4BAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAY;UAC3CJ,EAAA,CAAAC,cAAA,gBAGqD;UAF9CD,EAAA,CAAAkH,gBAAA,2BAAAC,6DAAAC,MAAA;YAAApH,EAAA,CAAAqH,kBAAA,CAAAJ,GAAA,CAAAtG,WAAA,EAAAyG,MAAA,MAAAH,GAAA,CAAAtG,WAAA,GAAAyG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UACzBpH,EAAA,CAAAgC,UAAA,mBAAAsF,qDAAAF,MAAA;YAAA,OAASH,GAAA,CAAAzB,cAAA,EAAA4B,MAAA,CAAAG,MAAA,kBAAAH,MAAA,CAAAG,MAAA,CAAAC,KAAA,KAAuC,EAAE,CAAC;UAAA,EAAC;UAF3DxH,EAAA,CAAAI,YAAA,EAGqD;UACrDJ,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAC5BH,EAD4B,CAAAI,YAAA,EAAW,EACtB;UAGfJ,EADF,CAAAC,cAAA,yBAA2D,iBAC9C;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAY;UAC7BJ,EAAA,CAAAC,cAAA,qBAAkF;UAAtED,EAAA,CAAAkH,gBAAA,2BAAAO,kEAAAL,MAAA;YAAApH,EAAA,CAAAqH,kBAAA,CAAAJ,GAAA,CAAArG,YAAA,EAAAwG,MAAA,MAAAH,GAAA,CAAArG,YAAA,GAAAwG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAACpH,EAAA,CAAAgC,UAAA,6BAAA0F,oEAAA;YAAA,OAAmBT,GAAA,CAAAxB,oBAAA,EAAsB;UAAA,EAAC;UAC/EzF,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAa;UACzCJ,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAC9CJ,EAAA,CAAAC,cAAA,sBAA6B;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAClDJ,EAAA,CAAAC,cAAA,sBAA6B;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAG5CH,EAH4C,CAAAI,YAAA,EAAa,EACxC,EACE,EACb;UAGJJ,EADF,CAAAC,cAAA,kBAAuE,gBAC3D;UAAAD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACxBJ,EAAA,CAAAG,MAAA,wBACF;UAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACW,EACV;UAGTJ,EADF,CAAAC,cAAA,oBAA6B,wBACT;UAqBhBD,EApBA,CAAAK,UAAA,KAAAsH,mCAAA,kBAAiD,KAAAC,mCAAA,mBAKuB,KAAAC,mCAAA,mBAeM;UAsFpF7H,EAFI,CAAAI,YAAA,EAAmB,EACV,EACP;;;UArIaJ,EAAA,CAAAQ,SAAA,IAAyB;UAAzBR,EAAA,CAAA8H,gBAAA,YAAAb,GAAA,CAAAtG,WAAA,CAAyB;UAQpBX,EAAA,CAAAQ,SAAA,GAA0B;UAA1BR,EAAA,CAAA8H,gBAAA,YAAAb,GAAA,CAAArG,YAAA,CAA0B;UAmBtCZ,EAAA,CAAAQ,SAAA,IAAe;UAAfR,EAAA,CAAAS,UAAA,SAAAwG,GAAA,CAAA3C,SAAA,CAAe;UAKftE,EAAA,CAAAQ,SAAA,EAAgD;UAAhDR,EAAA,CAAAS,UAAA,UAAAwG,GAAA,CAAA3C,SAAA,IAAA2C,GAAA,CAAAnD,eAAA,CAAAiE,MAAA,OAAgD;UAehD/H,EAAA,CAAAQ,SAAA,EAA8C;UAA9CR,EAAA,CAAAS,UAAA,UAAAwG,GAAA,CAAA3C,SAAA,IAAA2C,GAAA,CAAAnD,eAAA,CAAAiE,MAAA,KAA8C;;;qBD1CtDhJ,YAAY,EAAAiJ,EAAA,CAAAC,IAAA,EACZjJ,YAAY,EAAAkJ,EAAA,CAAAC,UAAA,EACZxI,WAAW,EAAAyI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXtJ,cAAc,EAAAuJ,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACdhK,eAAe,EAAAiK,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflK,aAAa,EAAAmK,EAAA,CAAAC,OAAA,EACbnK,aAAa,EAAAoK,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,eAAA,EAAAJ,GAAA,CAAAK,YAAA,EACbxK,cAAc,EAAAyK,GAAA,CAAAC,OAAA,EACdzK,cAAc,EAAA0K,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EACd9K,kBAAkB,EAClBC,eAAe,EAAA8K,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfhL,eAAe,EAEfC,wBAAwB,EAAAgL,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}