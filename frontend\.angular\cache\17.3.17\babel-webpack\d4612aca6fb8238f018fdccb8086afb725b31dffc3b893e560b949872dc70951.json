{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/project.service\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/chips\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nconst _c0 = a0 => [\"/projects\", a0];\nconst _c1 = a0 => [\"/projects\", a0, \"edit\"];\nfunction ProjectListComponent_mat_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const mode_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", mode_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", mode_r1.label, \" \");\n  }\n}\nfunction ProjectListComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des projets...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", project_r3.dailyRate, \"\\u20AC/jour\");\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.getWorkModeLabel(project_r3.workMode));\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", project_r3.durationInMonths, \" mois\");\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, project_r3.startDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(project_r3.techStack);\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r3.description, \" \");\n  }\n}\nfunction ProjectListComponent_div_46_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 24)(1, \"mat-card-header\")(2, \"mat-card-title\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"mat-chip\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 29);\n    i0.ɵɵtemplate(11, ProjectListComponent_div_46_mat_card_1_div_11_Template, 5, 1, \"div\", 30)(12, ProjectListComponent_div_46_mat_card_1_div_12_Template, 5, 1, \"div\", 30)(13, ProjectListComponent_div_46_mat_card_1_div_13_Template, 5, 1, \"div\", 30)(14, ProjectListComponent_div_46_mat_card_1_div_14_Template, 6, 4, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ProjectListComponent_div_46_mat_card_1_div_15_Template, 5, 1, \"div\", 31)(16, ProjectListComponent_div_46_mat_card_1_p_16_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-actions\", 33)(18, \"button\", 34)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Voir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 34)(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_46_mat_card_1_Template_button_click_26_listener() {\n      const project_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.deleteProject(project_r3.id));\n    });\n    i0.ɵɵelementStart(27, \"mat-icon\");\n    i0.ɵɵtext(28, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", project_r3.role, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", project_r3.clientName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r3.getProjectStatusColor(project_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getProjectStatusText(project_r3), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", project_r3.dailyRate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.workMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.durationInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.startDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.techStack);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r3.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c0, project_r3.id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(14, _c1, project_r3.id));\n  }\n}\nfunction ProjectListComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProjectListComponent_div_46_mat_card_1_Template, 30, 16, \"mat-card\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredProjects);\n  }\n}\nfunction ProjectListComponent_div_47_h3_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1, \"Aucun projet trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_h3_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1, \"Aucun projet ne correspond aux filtres\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Commencez par ajouter votre premier projet !\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Essayez de modifier vos crit\\u00E8res de recherche.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 4)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Ajouter un projet \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_47_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.clearFilters());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Effacer les filtres \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 40)(4, \"mat-icon\", 41);\n    i0.ɵɵtext(5, \"work_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ProjectListComponent_div_47_h3_6_Template, 2, 0, \"h3\", 42)(7, ProjectListComponent_div_47_h3_7_Template, 2, 0, \"h3\", 42)(8, ProjectListComponent_div_47_p_8_Template, 2, 0, \"p\", 42)(9, ProjectListComponent_div_47_p_9_Template, 2, 0, \"p\", 42)(10, ProjectListComponent_div_47_button_10_Template, 4, 0, \"button\", 43)(11, ProjectListComponent_div_47_button_11_Template, 4, 0, \"button\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.projects.length > 0);\n  }\n}\nexport class ProjectListComponent {\n  constructor(projectService, authService, fb, snackBar) {\n    this.projectService = projectService;\n    this.authService = authService;\n    this.fb = fb;\n    this.snackBar = snackBar;\n    this.projects = [];\n    this.filteredProjects = [];\n    this.isLoading = false;\n    this.workModeOptions = [{\n      value: 'REMOTE',\n      label: 'Télétravail'\n    }, {\n      value: 'ONSITE',\n      label: 'Sur site'\n    }, {\n      value: 'HYBRID',\n      label: 'Hybride'\n    }];\n    this.filterForm = this.fb.group({\n      minRate: [''],\n      maxRate: [''],\n      workMode: [''],\n      techStack: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getUser();\n    this.loadProjects();\n    this.setupFilters();\n  }\n  loadProjects() {\n    if (!this.currentUser?.id) return;\n    this.isLoading = true;\n    this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n      next: projects => {\n        this.projects = projects;\n        this.filteredProjects = projects;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Erreur lors du chargement des projets', 'Fermer', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        console.error('Error loading projects:', error);\n      }\n    });\n  }\n  setupFilters() {\n    this.filterForm.valueChanges.subscribe(() => {\n      this.applyFilters();\n    });\n  }\n  applyFilters() {\n    const filters = this.filterForm.value;\n    this.filteredProjects = this.projects.filter(project => {\n      // Rate filter\n      if (filters.minRate && project.dailyRate && project.dailyRate < filters.minRate) {\n        return false;\n      }\n      if (filters.maxRate && project.dailyRate && project.dailyRate > filters.maxRate) {\n        return false;\n      }\n      // Work mode filter\n      if (filters.workMode && project.workMode !== filters.workMode) {\n        return false;\n      }\n      // Tech stack filter\n      if (filters.techStack && project.techStack && !project.techStack.toLowerCase().includes(filters.techStack.toLowerCase())) {\n        return false;\n      }\n      return true;\n    });\n  }\n  clearFilters() {\n    this.filterForm.reset();\n    this.filteredProjects = this.projects;\n  }\n  deleteProject(projectId) {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.delete(projectId).subscribe({\n        next: () => {\n          this.projects = this.projects.filter(p => p.id !== projectId);\n          this.applyFilters();\n          this.snackBar.open('Projet supprimé avec succès', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        },\n        error: error => {\n          this.snackBar.open('Erreur lors de la suppression', 'Fermer', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          console.error('Error deleting project:', error);\n        }\n      });\n    }\n  }\n  getWorkModeLabel(workMode) {\n    const option = this.workModeOptions.find(opt => opt.value === workMode);\n    return option ? option.label : workMode;\n  }\n  getProjectStatusColor(project) {\n    if (project.startDate) {\n      const startDate = new Date(project.startDate);\n      const now = new Date();\n      if (startDate > now) {\n        return 'accent'; // Future project\n      } else if (project.durationInMonths) {\n        const endDate = new Date(startDate);\n        endDate.setMonth(endDate.getMonth() + project.durationInMonths);\n        if (endDate > now) {\n          return 'primary'; // Active project\n        } else {\n          return 'warn'; // Completed project\n        }\n      }\n    }\n    return 'primary';\n  }\n  getProjectStatusText(project) {\n    if (project.startDate) {\n      const startDate = new Date(project.startDate);\n      const now = new Date();\n      if (startDate > now) {\n        return 'À venir';\n      } else if (project.durationInMonths) {\n        const endDate = new Date(startDate);\n        endDate.setMonth(endDate.getMonth() + project.durationInMonths);\n        if (endDate > now) {\n          return 'En cours';\n        } else {\n          return 'Terminé';\n        }\n      }\n    }\n    return 'Statut inconnu';\n  }\n  static {\n    this.ɵfac = function ProjectListComponent_Factory(t) {\n      return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectListComponent,\n      selectors: [[\"app-project-list\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 48,\n      vars: 5,\n      consts: [[1, \"project-list-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\"], [1, \"filters-card\"], [1, \"filters-form\", 3, \"formGroup\"], [1, \"filter-row\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"minRate\", \"placeholder\", \"400\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"maxRate\", \"placeholder\", \"800\"], [\"formControlName\", \"workMode\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"techStack\", \"placeholder\", \"React, Angular...\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"clear-filters-btn\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"projects-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [1, \"projects-grid\"], [\"class\", \"project-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"project-card\"], [1, \"project-title\"], [1, \"project-client\"], [1, \"project-status\"], [\"selected\", \"\", 3, \"color\"], [1, \"project-details\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"class\", \"project-tech\", 4, \"ngIf\"], [\"class\", \"project-description\", 4, \"ngIf\"], [1, \"project-actions\"], [\"mat-button\", \"\", 3, \"routerLink\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"detail-item\"], [1, \"project-tech\"], [1, \"project-description\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"empty-icon\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\"]],\n      template: function ProjectListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Mes Projets \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 4)(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Nouveau projet \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"mat-card\", 5)(12, \"mat-card-header\")(13, \"mat-card-title\");\n          i0.ɵɵtext(14, \"Filtres\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"form\", 6)(17, \"div\", 7)(18, \"mat-form-field\", 8)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"TJM minimum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 9);\n          i0.ɵɵelementStart(22, \"span\", 10);\n          i0.ɵɵtext(23, \"\\u20AC\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"mat-form-field\", 8)(25, \"mat-label\");\n          i0.ɵɵtext(26, \"TJM maximum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"input\", 11);\n          i0.ɵɵelementStart(28, \"span\", 10);\n          i0.ɵɵtext(29, \"\\u20AC\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 8)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Mode de travail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-select\", 12)(34, \"mat-option\", 13);\n          i0.ɵɵtext(35, \"Tous\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, ProjectListComponent_mat_option_36_Template, 2, 2, \"mat-option\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 8)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"Technologies\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function ProjectListComponent_Template_button_click_41_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵelementStart(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" Effacer \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(45, ProjectListComponent_div_45_Template, 4, 0, \"div\", 17)(46, ProjectListComponent_div_46_Template, 2, 1, \"div\", 18)(47, ProjectListComponent_div_47_Template, 12, 6, \"div\", 19);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.workModeOptions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredProjects.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredProjects.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DatePipe, RouterModule, i6.RouterLink, ReactiveFormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, MatCardModule, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, MatButtonModule, i8.MatButton, MatIconModule, i9.MatIcon, MatFormFieldModule, i10.MatFormField, i10.MatLabel, i10.MatSuffix, MatInputModule, i11.MatInput, MatSelectModule, i12.MatSelect, i13.MatOption, MatChipsModule, i14.MatChip, MatProgressSpinnerModule, i15.MatProgressSpinner, MatSnackBarModule],\n      styles: [\".project-list-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin: 0;\\n  color: #333;\\n}\\n.page-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #667eea;\\n}\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filters-form[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.filter-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: flex-end;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n  flex: 1;\\n}\\n\\n.clear-filters-btn[_ngcontent-%COMP%] {\\n  height: 56px;\\n  min-width: 120px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n}\\n\\n.projects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n\\n.project-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.project-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.project-client[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-weight: 500;\\n}\\n\\n.project-status[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.project-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.project-tech[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n  padding: 8px 12px;\\n  background-color: #f5f5f5;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  color: #555;\\n}\\n.project-tech[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #667eea;\\n}\\n\\n.project-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n  margin: 0;\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n}\\n\\n.project-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  margin-top: 40px;\\n}\\n.empty-state[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.empty-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0 0 24px 0;\\n}\\n.empty-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .project-list-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .filter-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .filter-field[_ngcontent-%COMP%] {\\n    min-width: unset;\\n  }\\n  .projects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .project-details[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatChipsModule", "MatProgressSpinnerModule", "MatSnackBarModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "mode_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "project_r3", "dailyRate", "ɵɵtextInterpolate", "ctx_r3", "getWorkModeLabel", "workMode", "durationInMonths", "ɵɵpipeBind2", "startDate", "techStack", "description", "ɵɵtemplate", "ProjectListComponent_div_46_mat_card_1_div_11_Template", "ProjectListComponent_div_46_mat_card_1_div_12_Template", "ProjectListComponent_div_46_mat_card_1_div_13_Template", "ProjectListComponent_div_46_mat_card_1_div_14_Template", "ProjectListComponent_div_46_mat_card_1_div_15_Template", "ProjectListComponent_div_46_mat_card_1_p_16_Template", "ɵɵlistener", "ProjectListComponent_div_46_mat_card_1_Template_button_click_26_listener", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "deleteProject", "id", "role", "clientName", "getProjectStatusColor", "getProjectStatusText", "ɵɵpureFunction1", "_c0", "_c1", "ProjectListComponent_div_46_mat_card_1_Template", "filteredProjects", "ProjectListComponent_div_47_button_11_Template_button_click_0_listener", "_r5", "clearFilters", "ProjectListComponent_div_47_h3_6_Template", "ProjectListComponent_div_47_h3_7_Template", "ProjectListComponent_div_47_p_8_Template", "ProjectListComponent_div_47_p_9_Template", "ProjectListComponent_div_47_button_10_Template", "ProjectListComponent_div_47_button_11_Template", "projects", "length", "ProjectListComponent", "constructor", "projectService", "authService", "fb", "snackBar", "isLoading", "workModeOptions", "filterForm", "group", "minRate", "maxRate", "ngOnInit", "currentUser", "getUser", "loadProjects", "setupFilters", "getByFreelanceId", "subscribe", "next", "error", "open", "duration", "panelClass", "console", "valueChanges", "applyFilters", "filters", "filter", "project", "toLowerCase", "includes", "reset", "projectId", "confirm", "delete", "p", "option", "find", "opt", "Date", "now", "endDate", "setMonth", "getMonth", "ɵɵdirectiveInject", "i1", "ProjectService", "i2", "AuthService", "i3", "FormBuilder", "i4", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProjectListComponent_Template", "rf", "ctx", "ProjectListComponent_mat_option_36_Template", "ProjectListComponent_Template_button_click_41_listener", "ProjectListComponent_div_45_Template", "ProjectListComponent_div_46_Template", "ProjectListComponent_div_47_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i6", "RouterLink", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i7", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i8", "MatButton", "i9", "MatIcon", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatInput", "i12", "MatSelect", "i13", "MatOption", "i14", "MatChip", "i15", "MatProgressSpinner", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\project-list\\project-list.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\project-list\\project-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { ProjectService, ProjectDto } from '../../../services/project.service';\nimport { AuthService } from '../../../services/auth.service';\n\n@Component({\n  selector: 'app-project-list',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule\n  ],\n  templateUrl: './project-list.component.html',\n  styleUrls: ['./project-list.component.scss']\n})\nexport class ProjectListComponent implements OnInit {\n  projects: ProjectDto[] = [];\n  filteredProjects: ProjectDto[] = [];\n  isLoading = false;\n  filterForm: FormGroup;\n  currentUser: any;\n\n  workModeOptions = [\n    { value: 'REMOTE', label: 'Télétravail' },\n    { value: 'ONSITE', label: 'Sur site' },\n    { value: 'HYBRID', label: 'Hybride' }\n  ];\n\n  constructor(\n    private projectService: ProjectService,\n    private authService: AuthService,\n    private fb: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {\n    this.filterForm = this.fb.group({\n      minRate: [''],\n      maxRate: [''],\n      workMode: [''],\n      techStack: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getUser();\n    this.loadProjects();\n    this.setupFilters();\n  }\n\n  loadProjects(): void {\n    if (!this.currentUser?.id) return;\n\n    this.isLoading = true;\n    this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n      next: (projects) => {\n        this.projects = projects;\n        this.filteredProjects = projects;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.snackBar.open('Erreur lors du chargement des projets', 'Fermer', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        console.error('Error loading projects:', error);\n      }\n    });\n  }\n\n  setupFilters(): void {\n    this.filterForm.valueChanges.subscribe(() => {\n      this.applyFilters();\n    });\n  }\n\n  applyFilters(): void {\n    const filters = this.filterForm.value;\n    \n    this.filteredProjects = this.projects.filter(project => {\n      // Rate filter\n      if (filters.minRate && project.dailyRate && project.dailyRate < filters.minRate) {\n        return false;\n      }\n      if (filters.maxRate && project.dailyRate && project.dailyRate > filters.maxRate) {\n        return false;\n      }\n      \n      // Work mode filter\n      if (filters.workMode && project.workMode !== filters.workMode) {\n        return false;\n      }\n      \n      // Tech stack filter\n      if (filters.techStack && project.techStack && \n          !project.techStack.toLowerCase().includes(filters.techStack.toLowerCase())) {\n        return false;\n      }\n      \n      return true;\n    });\n  }\n\n  clearFilters(): void {\n    this.filterForm.reset();\n    this.filteredProjects = this.projects;\n  }\n\n  deleteProject(projectId: number): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.delete(projectId).subscribe({\n        next: () => {\n          this.projects = this.projects.filter(p => p.id !== projectId);\n          this.applyFilters();\n          this.snackBar.open('Projet supprimé avec succès', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        },\n        error: (error) => {\n          this.snackBar.open('Erreur lors de la suppression', 'Fermer', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          console.error('Error deleting project:', error);\n        }\n      });\n    }\n  }\n\n  getWorkModeLabel(workMode: string): string {\n    const option = this.workModeOptions.find(opt => opt.value === workMode);\n    return option ? option.label : workMode;\n  }\n\n  getProjectStatusColor(project: ProjectDto): string {\n    if (project.startDate) {\n      const startDate = new Date(project.startDate);\n      const now = new Date();\n      \n      if (startDate > now) {\n        return 'accent'; // Future project\n      } else if (project.durationInMonths) {\n        const endDate = new Date(startDate);\n        endDate.setMonth(endDate.getMonth() + project.durationInMonths);\n        \n        if (endDate > now) {\n          return 'primary'; // Active project\n        } else {\n          return 'warn'; // Completed project\n        }\n      }\n    }\n    return 'primary';\n  }\n\n  getProjectStatusText(project: ProjectDto): string {\n    if (project.startDate) {\n      const startDate = new Date(project.startDate);\n      const now = new Date();\n      \n      if (startDate > now) {\n        return 'À venir';\n      } else if (project.durationInMonths) {\n        const endDate = new Date(startDate);\n        endDate.setMonth(endDate.getMonth() + project.durationInMonths);\n        \n        if (endDate > now) {\n          return 'En cours';\n        } else {\n          return 'Terminé';\n        }\n      }\n    }\n    return 'Statut inconnu';\n  }\n}\n", "<div class=\"project-list-container\">\n  <!-- Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">\n        <mat-icon>work</mat-icon>\n        Mes Projets\n      </h1>\n      <button mat-raised-button color=\"primary\" routerLink=\"/projects/new\">\n        <mat-icon>add</mat-icon>\n        Nouveau projet\n      </button>\n    </div>\n  </div>\n\n  <!-- Filters -->\n  <mat-card class=\"filters-card\">\n    <mat-card-header>\n      <mat-card-title>Filtres</mat-card-title>\n    </mat-card-header>\n    <mat-card-content>\n      <form [formGroup]=\"filterForm\" class=\"filters-form\">\n        <div class=\"filter-row\">\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>TJM minimum</mat-label>\n            <input matInput type=\"number\" formControlName=\"minRate\" placeholder=\"400\">\n            <span matSuffix>€</span>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>TJM maximum</mat-label>\n            <input matInput type=\"number\" formControlName=\"maxRate\" placeholder=\"800\">\n            <span matSuffix>€</span>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Mode de travail</mat-label>\n            <mat-select formControlName=\"workMode\">\n              <mat-option value=\"\">Tous</mat-option>\n              <mat-option *ngFor=\"let mode of workModeOptions\" [value]=\"mode.value\">\n                {{ mode.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Technologies</mat-label>\n            <input matInput formControlName=\"techStack\" placeholder=\"React, Angular...\">\n          </mat-form-field>\n\n          <button mat-stroked-button type=\"button\" (click)=\"clearFilters()\" class=\"clear-filters-btn\">\n            <mat-icon>clear</mat-icon>\n            Effacer\n          </button>\n        </div>\n      </form>\n    </mat-card-content>\n  </mat-card>\n\n  <!-- Loading -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Chargement des projets...</p>\n  </div>\n\n  <!-- Projects Grid -->\n  <div *ngIf=\"!isLoading && filteredProjects.length > 0\" class=\"projects-grid\">\n    <mat-card class=\"project-card\" *ngFor=\"let project of filteredProjects\">\n      <mat-card-header>\n        <mat-card-title class=\"project-title\">\n          {{ project.role }}\n        </mat-card-title>\n        <mat-card-subtitle class=\"project-client\">\n          {{ project.clientName }}\n        </mat-card-subtitle>\n        <div class=\"project-status\">\n          <mat-chip [color]=\"getProjectStatusColor(project)\" selected>\n            {{ getProjectStatusText(project) }}\n          </mat-chip>\n        </div>\n      </mat-card-header>\n\n      <mat-card-content>\n        <div class=\"project-details\">\n          <div class=\"detail-item\" *ngIf=\"project.dailyRate\">\n            <mat-icon>euro</mat-icon>\n            <span>{{ project.dailyRate }}€/jour</span>\n          </div>\n\n          <div class=\"detail-item\" *ngIf=\"project.workMode\">\n            <mat-icon>location_on</mat-icon>\n            <span>{{ getWorkModeLabel(project.workMode) }}</span>\n          </div>\n\n          <div class=\"detail-item\" *ngIf=\"project.durationInMonths\">\n            <mat-icon>schedule</mat-icon>\n            <span>{{ project.durationInMonths }} mois</span>\n          </div>\n\n          <div class=\"detail-item\" *ngIf=\"project.startDate\">\n            <mat-icon>event</mat-icon>\n            <span>{{ project.startDate | date:'dd/MM/yyyy' }}</span>\n          </div>\n        </div>\n\n        <div class=\"project-tech\" *ngIf=\"project.techStack\">\n          <mat-icon>code</mat-icon>\n          <span>{{ project.techStack }}</span>\n        </div>\n\n        <p class=\"project-description\" *ngIf=\"project.description\">\n          {{ project.description }}\n        </p>\n      </mat-card-content>\n\n      <mat-card-actions class=\"project-actions\">\n        <button mat-button [routerLink]=\"['/projects', project.id]\">\n          <mat-icon>visibility</mat-icon>\n          Voir\n        </button>\n        <button mat-button [routerLink]=\"['/projects', project.id, 'edit']\">\n          <mat-icon>edit</mat-icon>\n          Modifier\n        </button>\n        <button mat-button color=\"warn\" (click)=\"deleteProject(project.id!)\">\n          <mat-icon>delete</mat-icon>\n          Supprimer\n        </button>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && filteredProjects.length === 0\" class=\"empty-state\">\n    <mat-card>\n      <mat-card-content>\n        <div class=\"empty-content\">\n          <mat-icon class=\"empty-icon\">work_off</mat-icon>\n          <h3 *ngIf=\"projects.length === 0\">Aucun projet trouvé</h3>\n          <h3 *ngIf=\"projects.length > 0\">Aucun projet ne correspond aux filtres</h3>\n          <p *ngIf=\"projects.length === 0\">Commencez par ajouter votre premier projet !</p>\n          <p *ngIf=\"projects.length > 0\">Essayez de modifier vos critères de recherche.</p>\n          <button *ngIf=\"projects.length === 0\" mat-raised-button color=\"primary\" routerLink=\"/projects/new\">\n            <mat-icon>add</mat-icon>\n            Ajouter un projet\n          </button>\n          <button *ngIf=\"projects.length > 0\" mat-stroked-button (click)=\"clearFilters()\">\n            <mat-icon>clear</mat-icon>\n            Effacer les filtres\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAqB,6BAA6B;;;;;;;;;;;;;;;;;;;;;IC2B9DC,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,KAAA,CAAoB;IACnEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,KAAA,MACF;;;;;IAmBZT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,kBAA2B;IAC3BV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;;;;;IAsBIH,EADF,CAAAC,cAAA,cAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IACrCF,EADqC,CAAAG,YAAA,EAAO,EACtC;;;;IADEH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,kBAAA,KAAAG,UAAA,CAAAC,SAAA,gBAA6B;;;;;IAInCZ,EADF,CAAAC,cAAA,cAAkD,eACtC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;;;;;IADEH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAa,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAJ,UAAA,CAAAK,QAAA,EAAwC;;;;;IAI9ChB,EADF,CAAAC,cAAA,cAA0D,eAC9C;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;;;;IADEH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,kBAAA,KAAAG,UAAA,CAAAM,gBAAA,UAAmC;;;;;IAIzCjB,EADF,CAAAC,cAAA,cAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA2C;;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;;;;IADEH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAkB,WAAA,OAAAP,UAAA,CAAAQ,SAAA,gBAA2C;;;;;IAKnDnB,EADF,CAAAC,cAAA,cAAoD,eACxC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAChC;;;;IADEH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAa,iBAAA,CAAAF,UAAA,CAAAS,SAAA,CAAuB;;;;;IAG/BpB,EAAA,CAAAC,cAAA,YAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,UAAA,CAAAU,WAAA,MACF;;;;;;IA3CArB,EAFJ,CAAAC,cAAA,mBAAwE,sBACrD,yBACuB;IACpCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,4BAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAElBH,EADF,CAAAC,cAAA,cAA4B,mBACkC;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAW,EACP,EACU;IAGhBH,EADF,CAAAC,cAAA,uBAAkB,eACa;IAgB3BD,EAfA,CAAAsB,UAAA,KAAAC,sDAAA,kBAAmD,KAAAC,sDAAA,kBAKD,KAAAC,sDAAA,kBAKQ,KAAAC,sDAAA,kBAKP;IAIrD1B,EAAA,CAAAG,YAAA,EAAM;IAONH,EALA,CAAAsB,UAAA,KAAAK,sDAAA,kBAAoD,KAAAC,oDAAA,gBAKO;IAG7D5B,EAAA,CAAAG,YAAA,EAAmB;IAIfH,EAFJ,CAAAC,cAAA,4BAA0C,kBACoB,gBAChD;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,kBAAoE,gBACxD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqE;IAArCD,EAAA,CAAA6B,UAAA,mBAAAC,yEAAA;MAAA,MAAAnB,UAAA,GAAAX,EAAA,CAAA+B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAd,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAASrB,MAAA,CAAAsB,aAAA,CAAAzB,UAAA,CAAA0B,EAAA,CAA0B;IAAA,EAAC;IAClErC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,mBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;;IA3DLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,UAAA,CAAA2B,IAAA,MACF;IAEEtC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,UAAA,CAAA4B,UAAA,MACF;IAEYvC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAA0B,qBAAA,CAAA7B,UAAA,EAAwC;IAChDX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAA2B,oBAAA,CAAA9B,UAAA,OACF;IAM0BX,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAO,UAAA,CAAAC,SAAA,CAAuB;IAKvBZ,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAO,UAAA,CAAAK,QAAA,CAAsB;IAKtBhB,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAI,UAAA,SAAAO,UAAA,CAAAM,gBAAA,CAA8B;IAK9BjB,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAO,UAAA,CAAAQ,SAAA,CAAuB;IAMxBnB,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAO,UAAA,CAAAS,SAAA,CAAuB;IAKlBpB,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAO,UAAA,CAAAU,WAAA,CAAyB;IAMtCrB,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAA0C,eAAA,KAAAC,GAAA,EAAAhC,UAAA,CAAA0B,EAAA,EAAwC;IAIxCrC,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAA0C,eAAA,KAAAE,GAAA,EAAAjC,UAAA,CAAA0B,EAAA,EAAgD;;;;;IAtDzErC,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAsB,UAAA,IAAAuB,+CAAA,yBAAwE;IA+D1E7C,EAAA,CAAAG,YAAA,EAAM;;;;IA/D+CH,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAgC,gBAAA,CAAmB;;;;;IAuEhE9C,EAAA,CAAAC,cAAA,SAAkC;IAAAD,EAAA,CAAAE,MAAA,+BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC1DH,EAAA,CAAAC,cAAA,SAAgC;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC3EH,EAAA,CAAAC,cAAA,QAAiC;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACjFH,EAAA,CAAAC,cAAA,QAA+B;IAAAD,EAAA,CAAAE,MAAA,0DAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAE/EH,EADF,CAAAC,cAAA,gBAAmG,eACvF;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgF;IAAzBD,EAAA,CAAA6B,UAAA,mBAAAkB,uEAAA;MAAA/C,EAAA,CAAA+B,aAAA,CAAAiB,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAASrB,MAAA,CAAAmC,YAAA,EAAc;IAAA,EAAC;IAC7EjD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAZTH,EAJR,CAAAC,cAAA,cAA6E,eACjE,uBACU,cACW,mBACI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAShDH,EARA,CAAAsB,UAAA,IAAA4B,yCAAA,iBAAkC,IAAAC,yCAAA,iBACF,IAAAC,wCAAA,gBACC,IAAAC,wCAAA,gBACF,KAAAC,8CAAA,qBACoE,KAAAC,8CAAA,qBAInB;IAOxFvD,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;;;;IAfOH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0C,QAAA,CAAAC,MAAA,OAA2B;IAC3BzD,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0C,QAAA,CAAAC,MAAA,KAAyB;IAC1BzD,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0C,QAAA,CAAAC,MAAA,OAA2B;IAC3BzD,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0C,QAAA,CAAAC,MAAA,KAAyB;IACpBzD,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0C,QAAA,CAAAC,MAAA,OAA2B;IAI3BzD,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0C,QAAA,CAAAC,MAAA,KAAyB;;;AD9G5C,OAAM,MAAOC,oBAAoB;EAa/BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,EAAe,EACfC,QAAqB;IAHrB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAhBlB,KAAAP,QAAQ,GAAiB,EAAE;IAC3B,KAAAV,gBAAgB,GAAiB,EAAE;IACnC,KAAAkB,SAAS,GAAG,KAAK;IAIjB,KAAAC,eAAe,GAAG,CAChB;MAAE3D,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAa,CAAE,EACzC;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAU,CAAE,EACtC;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAS,CAAE,CACtC;IAQC,IAAI,CAACyD,UAAU,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC9BC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbrD,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdI,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;EACJ;EAEAkD,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,OAAO,EAAE;IAC7C,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAD,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACF,WAAW,EAAElC,EAAE,EAAE;IAE3B,IAAI,CAAC2B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,cAAc,CAACe,gBAAgB,CAAC,IAAI,CAACJ,WAAW,CAAClC,EAAE,CAAC,CAACuC,SAAS,CAAC;MAClEC,IAAI,EAAGrB,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACV,gBAAgB,GAAGU,QAAQ;QAChC,IAAI,CAACQ,SAAS,GAAG,KAAK;MACxB,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACd,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,QAAQ,CAACgB,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;UACpEC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACFC,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAACR,UAAU,CAACiB,YAAY,CAACP,SAAS,CAAC,MAAK;MAC1C,IAAI,CAACQ,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV,MAAMC,OAAO,GAAG,IAAI,CAACnB,UAAU,CAAC5D,KAAK;IAErC,IAAI,CAACwC,gBAAgB,GAAG,IAAI,CAACU,QAAQ,CAAC8B,MAAM,CAACC,OAAO,IAAG;MACrD;MACA,IAAIF,OAAO,CAACjB,OAAO,IAAImB,OAAO,CAAC3E,SAAS,IAAI2E,OAAO,CAAC3E,SAAS,GAAGyE,OAAO,CAACjB,OAAO,EAAE;QAC/E,OAAO,KAAK;;MAEd,IAAIiB,OAAO,CAAChB,OAAO,IAAIkB,OAAO,CAAC3E,SAAS,IAAI2E,OAAO,CAAC3E,SAAS,GAAGyE,OAAO,CAAChB,OAAO,EAAE;QAC/E,OAAO,KAAK;;MAGd;MACA,IAAIgB,OAAO,CAACrE,QAAQ,IAAIuE,OAAO,CAACvE,QAAQ,KAAKqE,OAAO,CAACrE,QAAQ,EAAE;QAC7D,OAAO,KAAK;;MAGd;MACA,IAAIqE,OAAO,CAACjE,SAAS,IAAImE,OAAO,CAACnE,SAAS,IACtC,CAACmE,OAAO,CAACnE,SAAS,CAACoE,WAAW,EAAE,CAACC,QAAQ,CAACJ,OAAO,CAACjE,SAAS,CAACoE,WAAW,EAAE,CAAC,EAAE;QAC9E,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEAvC,YAAYA,CAAA;IACV,IAAI,CAACiB,UAAU,CAACwB,KAAK,EAAE;IACvB,IAAI,CAAC5C,gBAAgB,GAAG,IAAI,CAACU,QAAQ;EACvC;EAEApB,aAAaA,CAACuD,SAAiB;IAC7B,IAAIC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAAChC,cAAc,CAACiC,MAAM,CAACF,SAAS,CAAC,CAACf,SAAS,CAAC;QAC9CC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACrB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8B,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACzD,EAAE,KAAKsD,SAAS,CAAC;UAC7D,IAAI,CAACP,YAAY,EAAE;UACnB,IAAI,CAACrB,QAAQ,CAACgB,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;YAC1DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;QACJ,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YAC5DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;UACFC,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QACjD;OACD,CAAC;;EAEN;EAEA/D,gBAAgBA,CAACC,QAAgB;IAC/B,MAAM+E,MAAM,GAAG,IAAI,CAAC9B,eAAe,CAAC+B,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3F,KAAK,KAAKU,QAAQ,CAAC;IACvE,OAAO+E,MAAM,GAAGA,MAAM,CAACtF,KAAK,GAAGO,QAAQ;EACzC;EAEAwB,qBAAqBA,CAAC+C,OAAmB;IACvC,IAAIA,OAAO,CAACpE,SAAS,EAAE;MACrB,MAAMA,SAAS,GAAG,IAAI+E,IAAI,CAACX,OAAO,CAACpE,SAAS,CAAC;MAC7C,MAAMgF,GAAG,GAAG,IAAID,IAAI,EAAE;MAEtB,IAAI/E,SAAS,GAAGgF,GAAG,EAAE;QACnB,OAAO,QAAQ,CAAC,CAAC;OAClB,MAAM,IAAIZ,OAAO,CAACtE,gBAAgB,EAAE;QACnC,MAAMmF,OAAO,GAAG,IAAIF,IAAI,CAAC/E,SAAS,CAAC;QACnCiF,OAAO,CAACC,QAAQ,CAACD,OAAO,CAACE,QAAQ,EAAE,GAAGf,OAAO,CAACtE,gBAAgB,CAAC;QAE/D,IAAImF,OAAO,GAAGD,GAAG,EAAE;UACjB,OAAO,SAAS,CAAC,CAAC;SACnB,MAAM;UACL,OAAO,MAAM,CAAC,CAAC;;;;IAIrB,OAAO,SAAS;EAClB;EAEA1D,oBAAoBA,CAAC8C,OAAmB;IACtC,IAAIA,OAAO,CAACpE,SAAS,EAAE;MACrB,MAAMA,SAAS,GAAG,IAAI+E,IAAI,CAACX,OAAO,CAACpE,SAAS,CAAC;MAC7C,MAAMgF,GAAG,GAAG,IAAID,IAAI,EAAE;MAEtB,IAAI/E,SAAS,GAAGgF,GAAG,EAAE;QACnB,OAAO,SAAS;OACjB,MAAM,IAAIZ,OAAO,CAACtE,gBAAgB,EAAE;QACnC,MAAMmF,OAAO,GAAG,IAAIF,IAAI,CAAC/E,SAAS,CAAC;QACnCiF,OAAO,CAACC,QAAQ,CAACD,OAAO,CAACE,QAAQ,EAAE,GAAGf,OAAO,CAACtE,gBAAgB,CAAC;QAE/D,IAAImF,OAAO,GAAGD,GAAG,EAAE;UACjB,OAAO,UAAU;SAClB,MAAM;UACL,OAAO,SAAS;;;;IAItB,OAAO,gBAAgB;EACzB;;;uBA/JWzC,oBAAoB,EAAA1D,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3G,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAAuG,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBrD,oBAAoB;MAAAsD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlH,EAAA,CAAAmH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/BzBzH,EALR,CAAAC,cAAA,aAAoC,aAET,aACK,YACH,eACX;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,gBAAqE,eACzD;UAAAD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,wBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAKFH,EAFJ,CAAAC,cAAA,mBAA+B,uBACZ,sBACC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACzBF,EADyB,CAAAG,YAAA,EAAiB,EACxB;UAKVH,EAJR,CAAAC,cAAA,wBAAkB,eACoC,cAC1B,yBACoC,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAU,SAAA,gBAA0E;UAC1EV,EAAA,CAAAC,cAAA,gBAAgB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UACnBF,EADmB,CAAAG,YAAA,EAAO,EACT;UAGfH,EADF,CAAAC,cAAA,yBAA0D,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAU,SAAA,iBAA0E;UAC1EV,EAAA,CAAAC,cAAA,gBAAgB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UACnBF,EADmB,CAAAG,YAAA,EAAO,EACT;UAGfH,EADF,CAAAC,cAAA,yBAA0D,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEpCH,EADF,CAAAC,cAAA,sBAAuC,sBAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAsB,UAAA,KAAAqG,2CAAA,yBAAsE;UAI1E3H,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,yBAA0D,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAU,SAAA,iBAA4E;UAC9EV,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,kBAA4F;UAAnDD,EAAA,CAAA6B,UAAA,mBAAA+F,uDAAA;YAAA,OAASF,GAAA,CAAAzE,YAAA,EAAc;UAAA,EAAC;UAC/DjD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,iBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV;UA4EXH,EAzEA,CAAAsB,UAAA,KAAAuG,oCAAA,kBAAiD,KAAAC,oCAAA,kBAM4B,KAAAC,oCAAA,mBAmEA;UAqB/E/H,EAAA,CAAAG,YAAA,EAAM;;;UArIMH,EAAA,CAAAO,SAAA,IAAwB;UAAxBP,EAAA,CAAAI,UAAA,cAAAsH,GAAA,CAAAxD,UAAA,CAAwB;UAkBOlE,EAAA,CAAAO,SAAA,IAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAAsH,GAAA,CAAAzD,eAAA,CAAkB;UAqBrDjE,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAsH,GAAA,CAAA1D,SAAA,CAAe;UAMfhE,EAAA,CAAAO,SAAA,EAA+C;UAA/CP,EAAA,CAAAI,UAAA,UAAAsH,GAAA,CAAA1D,SAAA,IAAA0D,GAAA,CAAA5E,gBAAA,CAAAW,MAAA,KAA+C;UAmE/CzD,EAAA,CAAAO,SAAA,EAAiD;UAAjDP,EAAA,CAAAI,UAAA,UAAAsH,GAAA,CAAA1D,SAAA,IAAA0D,GAAA,CAAA5E,gBAAA,CAAAW,MAAA,OAAiD;;;qBDjHrDrE,YAAY,EAAA4I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ9I,YAAY,EAAA+I,EAAA,CAAAC,UAAA,EACZ/I,mBAAmB,EAAAsH,EAAA,CAAA0B,aAAA,EAAA1B,EAAA,CAAA2B,oBAAA,EAAA3B,EAAA,CAAA4B,mBAAA,EAAA5B,EAAA,CAAA6B,eAAA,EAAA7B,EAAA,CAAA8B,oBAAA,EAAA9B,EAAA,CAAA+B,kBAAA,EAAA/B,EAAA,CAAAgC,eAAA,EACnBrJ,aAAa,EAAAsJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACb3J,eAAe,EAAA4J,EAAA,CAAAC,SAAA,EACf5J,aAAa,EAAA6J,EAAA,CAAAC,OAAA,EACb7J,kBAAkB,EAAA8J,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EAClBhK,cAAc,EAAAiK,GAAA,CAAAC,QAAA,EACdjK,eAAe,EAAAkK,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfpK,cAAc,EAAAqK,GAAA,CAAAC,OAAA,EACdrK,wBAAwB,EAAAsK,GAAA,CAAAC,kBAAA,EACxBtK,iBAAiB;MAAAuK,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}