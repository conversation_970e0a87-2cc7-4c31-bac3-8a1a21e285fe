{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080/api',\n  oauth: {\n    google: {\n      clientId: 'your-google-client-id'\n    },\n    github: {\n      clientId: 'your-github-client-id'\n    },\n    microsoft: {\n      clientId: 'your-microsoft-client-id'\n    }\n  }\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "o<PERSON>h", "google", "clientId", "github", "microsoft"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080/api',\n  oauth: {\n    google: {\n      clientId: 'your-google-client-id'\n    },\n    github: {\n      clientId: 'your-github-client-id'\n    },\n    microsoft: {\n      clientId: 'your-microsoft-client-id'\n    }\n  }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,2BAA2B;EACnCC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,QAAQ,EAAE;KACX;IACDC,MAAM,EAAE;MACND,QAAQ,EAAE;KACX;IACDE,SAAS,EAAE;MACTF,QAAQ,EAAE;;;CAGf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}