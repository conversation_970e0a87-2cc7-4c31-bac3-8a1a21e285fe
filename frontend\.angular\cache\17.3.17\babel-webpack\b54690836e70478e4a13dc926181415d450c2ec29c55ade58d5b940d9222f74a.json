{"ast": null, "code": "import { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nexport class Subject extends Observable {\n  constructor() {\n    super();\n    this.closed = false;\n    this.currentObservers = null;\n    this.observers = [];\n    this.isStopped = false;\n    this.hasError = false;\n    this.thrownError = null;\n  }\n  lift(operator) {\n    const subject = new AnonymousSubject(this, this);\n    subject.operator = operator;\n    return subject;\n  }\n  _throwIfClosed() {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n  }\n  next(value) {\n    errorContext(() => {\n      this._throwIfClosed();\n      if (!this.isStopped) {\n        if (!this.currentObservers) {\n          this.currentObservers = Array.from(this.observers);\n        }\n        for (const observer of this.currentObservers) {\n          observer.next(value);\n        }\n      }\n    });\n  }\n  error(err) {\n    errorContext(() => {\n      this._throwIfClosed();\n      if (!this.isStopped) {\n        this.hasError = this.isStopped = true;\n        this.thrownError = err;\n        const {\n          observers\n        } = this;\n        while (observers.length) {\n          observers.shift().error(err);\n        }\n      }\n    });\n  }\n  complete() {\n    errorContext(() => {\n      this._throwIfClosed();\n      if (!this.isStopped) {\n        this.isStopped = true;\n        const {\n          observers\n        } = this;\n        while (observers.length) {\n          observers.shift().complete();\n        }\n      }\n    });\n  }\n  unsubscribe() {\n    this.isStopped = this.closed = true;\n    this.observers = this.currentObservers = null;\n  }\n  get observed() {\n    var _a;\n    return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n  }\n  _trySubscribe(subscriber) {\n    this._throwIfClosed();\n    return super._trySubscribe(subscriber);\n  }\n  _subscribe(subscriber) {\n    this._throwIfClosed();\n    this._checkFinalizedStatuses(subscriber);\n    return this._innerSubscribe(subscriber);\n  }\n  _innerSubscribe(subscriber) {\n    const {\n      hasError,\n      isStopped,\n      observers\n    } = this;\n    if (hasError || isStopped) {\n      return EMPTY_SUBSCRIPTION;\n    }\n    this.currentObservers = null;\n    observers.push(subscriber);\n    return new Subscription(() => {\n      this.currentObservers = null;\n      arrRemove(observers, subscriber);\n    });\n  }\n  _checkFinalizedStatuses(subscriber) {\n    const {\n      hasError,\n      thrownError,\n      isStopped\n    } = this;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped) {\n      subscriber.complete();\n    }\n  }\n  asObservable() {\n    const observable = new Observable();\n    observable.source = this;\n    return observable;\n  }\n}\nSubject.create = (destination, source) => {\n  return new AnonymousSubject(destination, source);\n};\nexport class AnonymousSubject extends Subject {\n  constructor(destination, source) {\n    super();\n    this.destination = destination;\n    this.source = source;\n  }\n  next(value) {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n  }\n  error(err) {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n  }\n  complete() {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n  }\n  _subscribe(subscriber) {\n    var _a, _b;\n    return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n  }\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "EMPTY_SUBSCRIPTION", "ObjectUnsubscribedError", "arr<PERSON><PERSON><PERSON>", "errorContext", "Subject", "constructor", "closed", "currentObservers", "observers", "isStopped", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "lift", "operator", "subject", "AnonymousSubject", "_throwIfClosed", "next", "value", "Array", "from", "observer", "error", "err", "length", "shift", "complete", "unsubscribe", "observed", "_a", "_trySubscribe", "subscriber", "_subscribe", "_checkFinalizedStatuses", "_innerSubscribe", "push", "asObservable", "observable", "source", "create", "destination", "_b", "call", "subscribe"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/rxjs/dist/esm/internal/Subject.js"], "sourcesContent": ["import { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nexport class Subject extends Observable {\n    constructor() {\n        super();\n        this.closed = false;\n        this.currentObservers = null;\n        this.observers = [];\n        this.isStopped = false;\n        this.hasError = false;\n        this.thrownError = null;\n    }\n    lift(operator) {\n        const subject = new AnonymousSubject(this, this);\n        subject.operator = operator;\n        return subject;\n    }\n    _throwIfClosed() {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n    }\n    next(value) {\n        errorContext(() => {\n            this._throwIfClosed();\n            if (!this.isStopped) {\n                if (!this.currentObservers) {\n                    this.currentObservers = Array.from(this.observers);\n                }\n                for (const observer of this.currentObservers) {\n                    observer.next(value);\n                }\n            }\n        });\n    }\n    error(err) {\n        errorContext(() => {\n            this._throwIfClosed();\n            if (!this.isStopped) {\n                this.hasError = this.isStopped = true;\n                this.thrownError = err;\n                const { observers } = this;\n                while (observers.length) {\n                    observers.shift().error(err);\n                }\n            }\n        });\n    }\n    complete() {\n        errorContext(() => {\n            this._throwIfClosed();\n            if (!this.isStopped) {\n                this.isStopped = true;\n                const { observers } = this;\n                while (observers.length) {\n                    observers.shift().complete();\n                }\n            }\n        });\n    }\n    unsubscribe() {\n        this.isStopped = this.closed = true;\n        this.observers = this.currentObservers = null;\n    }\n    get observed() {\n        var _a;\n        return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n    }\n    _trySubscribe(subscriber) {\n        this._throwIfClosed();\n        return super._trySubscribe(subscriber);\n    }\n    _subscribe(subscriber) {\n        this._throwIfClosed();\n        this._checkFinalizedStatuses(subscriber);\n        return this._innerSubscribe(subscriber);\n    }\n    _innerSubscribe(subscriber) {\n        const { hasError, isStopped, observers } = this;\n        if (hasError || isStopped) {\n            return EMPTY_SUBSCRIPTION;\n        }\n        this.currentObservers = null;\n        observers.push(subscriber);\n        return new Subscription(() => {\n            this.currentObservers = null;\n            arrRemove(observers, subscriber);\n        });\n    }\n    _checkFinalizedStatuses(subscriber) {\n        const { hasError, thrownError, isStopped } = this;\n        if (hasError) {\n            subscriber.error(thrownError);\n        }\n        else if (isStopped) {\n            subscriber.complete();\n        }\n    }\n    asObservable() {\n        const observable = new Observable();\n        observable.source = this;\n        return observable;\n    }\n}\nSubject.create = (destination, source) => {\n    return new AnonymousSubject(destination, source);\n};\nexport class AnonymousSubject extends Subject {\n    constructor(destination, source) {\n        super();\n        this.destination = destination;\n        this.source = source;\n    }\n    next(value) {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n    }\n    error(err) {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n    }\n    complete() {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    _subscribe(subscriber) {\n        var _a, _b;\n        return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,gBAAgB;AACjE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAO,MAAMC,OAAO,SAASN,UAAU,CAAC;EACpCO,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,OAAO,GAAG,IAAIC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IAChDD,OAAO,CAACD,QAAQ,GAAGA,QAAQ;IAC3B,OAAOC,OAAO;EAClB;EACAE,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACV,MAAM,EAAE;MACb,MAAM,IAAIL,uBAAuB,CAAC,CAAC;IACvC;EACJ;EACAgB,IAAIA,CAACC,KAAK,EAAE;IACRf,YAAY,CAAC,MAAM;MACf,IAAI,CAACa,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;QACjB,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;UACxB,IAAI,CAACA,gBAAgB,GAAGY,KAAK,CAACC,IAAI,CAAC,IAAI,CAACZ,SAAS,CAAC;QACtD;QACA,KAAK,MAAMa,QAAQ,IAAI,IAAI,CAACd,gBAAgB,EAAE;UAC1Cc,QAAQ,CAACJ,IAAI,CAACC,KAAK,CAAC;QACxB;MACJ;IACJ,CAAC,CAAC;EACN;EACAI,KAAKA,CAACC,GAAG,EAAE;IACPpB,YAAY,CAAC,MAAM;MACf,IAAI,CAACa,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;QACjB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACD,SAAS,GAAG,IAAI;QACrC,IAAI,CAACE,WAAW,GAAGY,GAAG;QACtB,MAAM;UAAEf;QAAU,CAAC,GAAG,IAAI;QAC1B,OAAOA,SAAS,CAACgB,MAAM,EAAE;UACrBhB,SAAS,CAACiB,KAAK,CAAC,CAAC,CAACH,KAAK,CAACC,GAAG,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC;EACN;EACAG,QAAQA,CAAA,EAAG;IACPvB,YAAY,CAAC,MAAM;MACf,IAAI,CAACa,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;QACjB,IAAI,CAACA,SAAS,GAAG,IAAI;QACrB,MAAM;UAAED;QAAU,CAAC,GAAG,IAAI;QAC1B,OAAOA,SAAS,CAACgB,MAAM,EAAE;UACrBhB,SAAS,CAACiB,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACH,MAAM,GAAG,IAAI;IACnC,IAAI,CAACE,SAAS,GAAG,IAAI,CAACD,gBAAgB,GAAG,IAAI;EACjD;EACA,IAAIqB,QAAQA,CAAA,EAAG;IACX,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAG,IAAI,CAACrB,SAAS,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACL,MAAM,IAAI,CAAC;EACrF;EACAM,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACf,cAAc,CAAC,CAAC;IACrB,OAAO,KAAK,CAACc,aAAa,CAACC,UAAU,CAAC;EAC1C;EACAC,UAAUA,CAACD,UAAU,EAAE;IACnB,IAAI,CAACf,cAAc,CAAC,CAAC;IACrB,IAAI,CAACiB,uBAAuB,CAACF,UAAU,CAAC;IACxC,OAAO,IAAI,CAACG,eAAe,CAACH,UAAU,CAAC;EAC3C;EACAG,eAAeA,CAACH,UAAU,EAAE;IACxB,MAAM;MAAErB,QAAQ;MAAED,SAAS;MAAED;IAAU,CAAC,GAAG,IAAI;IAC/C,IAAIE,QAAQ,IAAID,SAAS,EAAE;MACvB,OAAOT,kBAAkB;IAC7B;IACA,IAAI,CAACO,gBAAgB,GAAG,IAAI;IAC5BC,SAAS,CAAC2B,IAAI,CAACJ,UAAU,CAAC;IAC1B,OAAO,IAAIhC,YAAY,CAAC,MAAM;MAC1B,IAAI,CAACQ,gBAAgB,GAAG,IAAI;MAC5BL,SAAS,CAACM,SAAS,EAAEuB,UAAU,CAAC;IACpC,CAAC,CAAC;EACN;EACAE,uBAAuBA,CAACF,UAAU,EAAE;IAChC,MAAM;MAAErB,QAAQ;MAAEC,WAAW;MAAEF;IAAU,CAAC,GAAG,IAAI;IACjD,IAAIC,QAAQ,EAAE;MACVqB,UAAU,CAACT,KAAK,CAACX,WAAW,CAAC;IACjC,CAAC,MACI,IAAIF,SAAS,EAAE;MAChBsB,UAAU,CAACL,QAAQ,CAAC,CAAC;IACzB;EACJ;EACAU,YAAYA,CAAA,EAAG;IACX,MAAMC,UAAU,GAAG,IAAIvC,UAAU,CAAC,CAAC;IACnCuC,UAAU,CAACC,MAAM,GAAG,IAAI;IACxB,OAAOD,UAAU;EACrB;AACJ;AACAjC,OAAO,CAACmC,MAAM,GAAG,CAACC,WAAW,EAAEF,MAAM,KAAK;EACtC,OAAO,IAAIvB,gBAAgB,CAACyB,WAAW,EAAEF,MAAM,CAAC;AACpD,CAAC;AACD,OAAO,MAAMvB,gBAAgB,SAASX,OAAO,CAAC;EAC1CC,WAAWA,CAACmC,WAAW,EAAEF,MAAM,EAAE;IAC7B,KAAK,CAAC,CAAC;IACP,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACArB,IAAIA,CAACC,KAAK,EAAE;IACR,IAAIW,EAAE,EAAEY,EAAE;IACV,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,IAAI,CAACW,WAAW,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACZ,IAAI,MAAM,IAAI,IAAIwB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,EAAE,EAAEX,KAAK,CAAC;EACvI;EACAI,KAAKA,CAACC,GAAG,EAAE;IACP,IAAIM,EAAE,EAAEY,EAAE;IACV,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,IAAI,CAACW,WAAW,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,KAAK,MAAM,IAAI,IAAImB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,EAAE,EAAEN,GAAG,CAAC;EACtI;EACAG,QAAQA,CAAA,EAAG;IACP,IAAIG,EAAE,EAAEY,EAAE;IACV,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,IAAI,CAACW,WAAW,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACH,QAAQ,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,EAAE,CAAC;EACpI;EACAG,UAAUA,CAACD,UAAU,EAAE;IACnB,IAAIF,EAAE,EAAEY,EAAE;IACV,OAAO,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,IAAI,CAACS,MAAM,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,SAAS,CAACZ,UAAU,CAAC,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGzC,kBAAkB;EACtJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}