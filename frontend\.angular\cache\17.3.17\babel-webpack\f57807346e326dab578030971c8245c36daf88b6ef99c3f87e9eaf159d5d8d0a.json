{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = e => {\n  if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n    console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n  }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n  constructor(/** The box type to observe for resizes. */\n  _box) {\n    this._box = _box;\n    /** Stream that emits when the shared observer is destroyed. */\n    this._destroyed = new Subject();\n    /** Stream of all events from the ResizeObserver. */\n    this._resizeSubject = new Subject();\n    /** A map of elements to streams of their resize events. */\n    this._elementObservables = new Map();\n    if (typeof ResizeObserver !== 'undefined') {\n      this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n    }\n  }\n  /**\n   * Gets a stream of resize events for the given element.\n   * @param target The element to observe.\n   * @return The stream of resize events for the element.\n   */\n  observe(target) {\n    if (!this._elementObservables.has(target)) {\n      this._elementObservables.set(target, new Observable(observer => {\n        const subscription = this._resizeSubject.subscribe(observer);\n        this._resizeObserver?.observe(target, {\n          box: this._box\n        });\n        return () => {\n          this._resizeObserver?.unobserve(target);\n          subscription.unsubscribe();\n          this._elementObservables.delete(target);\n        };\n      }).pipe(filter(entries => entries.some(entry => entry.target === target)),\n      // Share a replay of the last event so that subsequent calls to observe the same element\n      // receive initial sizing info like the first one. Also enable ref counting so the\n      // element will be automatically unobserved when there are no more subscriptions.\n      shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }), takeUntil(this._destroyed)));\n    }\n    return this._elementObservables.get(target);\n  }\n  /** Destroys this instance. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._resizeSubject.complete();\n    this._elementObservables.clear();\n  }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n  constructor() {\n    /** Map of box type to shared resize observer. */\n    this._observers = new Map();\n    /** The Angular zone. */\n    this._ngZone = inject(NgZone);\n    if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      this._ngZone.runOutsideAngular(() => {\n        window.addEventListener('error', loopLimitExceededErrorHandler);\n      });\n    }\n  }\n  ngOnDestroy() {\n    for (const [, observer] of this._observers) {\n      observer.destroy();\n    }\n    this._observers.clear();\n    if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      window.removeEventListener('error', loopLimitExceededErrorHandler);\n    }\n  }\n  /**\n   * Gets a stream of resize events for the given target element and box type.\n   * @param target The element to observe for resizes.\n   * @param options Options to pass to the `ResizeObserver`\n   * @return The stream of resize events for the element.\n   */\n  observe(target, options) {\n    const box = options?.box || 'content-box';\n    if (!this._observers.has(box)) {\n      this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n    }\n    return this._observers.get(box).observe(target);\n  }\n  static {\n    this.ɵfac = function SharedResizeObserver_Factory(t) {\n      return new (t || SharedResizeObserver)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SharedResizeObserver,\n      factory: SharedResizeObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SharedResizeObserver };", "map": {"version": 3, "names": ["i0", "inject", "NgZone", "Injectable", "Subject", "Observable", "filter", "shareReplay", "takeUntil", "loopLimitExceededErrorHandler", "e", "ErrorEvent", "message", "console", "error", "SingleBoxSharedResizeObserver", "constructor", "_box", "_destroyed", "_resizeSubject", "_elementObservables", "Map", "ResizeObserver", "_resizeObserver", "entries", "next", "observe", "target", "has", "set", "observer", "subscription", "subscribe", "box", "unobserve", "unsubscribe", "delete", "pipe", "some", "entry", "bufferSize", "refCount", "get", "destroy", "complete", "clear", "SharedResizeObserver", "_observers", "_ngZone", "ngDevMode", "runOutsideAngular", "window", "addEventListener", "ngOnDestroy", "removeEventListener", "options", "ɵfac", "SharedResizeObserver_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args"], "sources": ["C:/dev/workspace/indezy/frontend/node_modules/@angular/cdk/fesm2022/observers/private.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * Hand<PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = (e) => {\n    if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n        console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n    }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n    constructor(\n    /** The box type to observe for resizes. */\n    _box) {\n        this._box = _box;\n        /** Stream that emits when the shared observer is destroyed. */\n        this._destroyed = new Subject();\n        /** Stream of all events from the ResizeObserver. */\n        this._resizeSubject = new Subject();\n        /** A map of elements to streams of their resize events. */\n        this._elementObservables = new Map();\n        if (typeof ResizeObserver !== 'undefined') {\n            this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n        }\n    }\n    /**\n     * Gets a stream of resize events for the given element.\n     * @param target The element to observe.\n     * @return The stream of resize events for the element.\n     */\n    observe(target) {\n        if (!this._elementObservables.has(target)) {\n            this._elementObservables.set(target, new Observable(observer => {\n                const subscription = this._resizeSubject.subscribe(observer);\n                this._resizeObserver?.observe(target, { box: this._box });\n                return () => {\n                    this._resizeObserver?.unobserve(target);\n                    subscription.unsubscribe();\n                    this._elementObservables.delete(target);\n                };\n            }).pipe(filter(entries => entries.some(entry => entry.target === target)), \n            // Share a replay of the last event so that subsequent calls to observe the same element\n            // receive initial sizing info like the first one. Also enable ref counting so the\n            // element will be automatically unobserved when there are no more subscriptions.\n            shareReplay({ bufferSize: 1, refCount: true }), takeUntil(this._destroyed)));\n        }\n        return this._elementObservables.get(target);\n    }\n    /** Destroys this instance. */\n    destroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._resizeSubject.complete();\n        this._elementObservables.clear();\n    }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n    constructor() {\n        /** Map of box type to shared resize observer. */\n        this._observers = new Map();\n        /** The Angular zone. */\n        this._ngZone = inject(NgZone);\n        if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            this._ngZone.runOutsideAngular(() => {\n                window.addEventListener('error', loopLimitExceededErrorHandler);\n            });\n        }\n    }\n    ngOnDestroy() {\n        for (const [, observer] of this._observers) {\n            observer.destroy();\n        }\n        this._observers.clear();\n        if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            window.removeEventListener('error', loopLimitExceededErrorHandler);\n        }\n    }\n    /**\n     * Gets a stream of resize events for the given target element and box type.\n     * @param target The element to observe for resizes.\n     * @param options Options to pass to the `ResizeObserver`\n     * @return The stream of resize events for the element.\n     */\n    observe(target, options) {\n        const box = options?.box || 'content-box';\n        if (!this._observers.has(box)) {\n            this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n        }\n        return this._observers.get(box).observe(target);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SharedResizeObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SharedResizeObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SharedResizeObserver, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SharedResizeObserver };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAC1D,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;;AAE/D;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAIC,CAAC,IAAK;EACzC,IAAIA,CAAC,YAAYC,UAAU,IAAID,CAAC,CAACE,OAAO,KAAK,oCAAoC,EAAE;IAC/EC,OAAO,CAACC,KAAK,CAAC,GAAGJ,CAAC,CAACE,OAAO,8IAA8I,CAAC;EAC7K;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,6BAA6B,CAAC;EAChCC,WAAWA,CACX;EACAC,IAAI,EAAE;IACF,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACC,UAAU,GAAG,IAAId,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACe,cAAc,GAAG,IAAIf,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAACgB,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;MACvC,IAAI,CAACC,eAAe,GAAG,IAAID,cAAc,CAACE,OAAO,IAAI,IAAI,CAACL,cAAc,CAACM,IAAI,CAACD,OAAO,CAAC,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAAC,IAAI,CAACP,mBAAmB,CAACQ,GAAG,CAACD,MAAM,CAAC,EAAE;MACvC,IAAI,CAACP,mBAAmB,CAACS,GAAG,CAACF,MAAM,EAAE,IAAItB,UAAU,CAACyB,QAAQ,IAAI;QAC5D,MAAMC,YAAY,GAAG,IAAI,CAACZ,cAAc,CAACa,SAAS,CAACF,QAAQ,CAAC;QAC5D,IAAI,CAACP,eAAe,EAAEG,OAAO,CAACC,MAAM,EAAE;UAAEM,GAAG,EAAE,IAAI,CAAChB;QAAK,CAAC,CAAC;QACzD,OAAO,MAAM;UACT,IAAI,CAACM,eAAe,EAAEW,SAAS,CAACP,MAAM,CAAC;UACvCI,YAAY,CAACI,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACf,mBAAmB,CAACgB,MAAM,CAACT,MAAM,CAAC;QAC3C,CAAC;MACL,CAAC,CAAC,CAACU,IAAI,CAAC/B,MAAM,CAACkB,OAAO,IAAIA,OAAO,CAACc,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACZ,MAAM,KAAKA,MAAM,CAAC,CAAC;MACzE;MACA;MACA;MACApB,WAAW,CAAC;QAAEiC,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,EAAEjC,SAAS,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAAC;IAChF;IACA,OAAO,IAAI,CAACE,mBAAmB,CAACsB,GAAG,CAACf,MAAM,CAAC;EAC/C;EACA;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACzB,UAAU,CAACO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACP,UAAU,CAAC0B,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACzB,cAAc,CAACyB,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACxB,mBAAmB,CAACyB,KAAK,CAAC,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvB9B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC+B,UAAU,GAAG,IAAI1B,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC2B,OAAO,GAAG/C,MAAM,CAACC,MAAM,CAAC;IAC7B,IAAI,OAAOoB,cAAc,KAAK,WAAW,KAAK,OAAO2B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1F,IAAI,CAACD,OAAO,CAACE,iBAAiB,CAAC,MAAM;QACjCC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE3C,6BAA6B,CAAC;MACnE,CAAC,CAAC;IACN;EACJ;EACA4C,WAAWA,CAAA,EAAG;IACV,KAAK,MAAM,GAAGvB,QAAQ,CAAC,IAAI,IAAI,CAACiB,UAAU,EAAE;MACxCjB,QAAQ,CAACa,OAAO,CAAC,CAAC;IACtB;IACA,IAAI,CAACI,UAAU,CAACF,KAAK,CAAC,CAAC;IACvB,IAAI,OAAOvB,cAAc,KAAK,WAAW,KAAK,OAAO2B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1FE,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAE7C,6BAA6B,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,OAAOA,CAACC,MAAM,EAAE4B,OAAO,EAAE;IACrB,MAAMtB,GAAG,GAAGsB,OAAO,EAAEtB,GAAG,IAAI,aAAa;IACzC,IAAI,CAAC,IAAI,CAACc,UAAU,CAACnB,GAAG,CAACK,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACc,UAAU,CAAClB,GAAG,CAACI,GAAG,EAAE,IAAIlB,6BAA6B,CAACkB,GAAG,CAAC,CAAC;IACpE;IACA,OAAO,IAAI,CAACc,UAAU,CAACL,GAAG,CAACT,GAAG,CAAC,CAACP,OAAO,CAACC,MAAM,CAAC;EACnD;EACA;IAAS,IAAI,CAAC6B,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFZ,oBAAoB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAACa,KAAK,kBAD6E3D,EAAE,CAAA4D,kBAAA;MAAAC,KAAA,EACYf,oBAAoB;MAAAgB,OAAA,EAApBhB,oBAAoB,CAAAU,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAHoGjD,EAAE,CAAAgE,iBAAA,CAGXlB,oBAAoB,EAAc,CAAC;IAClHmB,IAAI,EAAE9D,UAAU;IAChB+D,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;;AAEA,SAASjB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}