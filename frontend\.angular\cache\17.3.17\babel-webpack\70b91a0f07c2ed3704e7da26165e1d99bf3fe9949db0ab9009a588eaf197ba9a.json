{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"firstName\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"lastName\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"confirmPassword\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_spinner_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction RegisterComponent_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9er mon compte\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9ation...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(fb, authService, router, snackBar) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.hidePassword = true;\n      this.hideConfirmPassword = true;\n      this.registerForm = this.fb.group({\n        firstName: ['', [Validators.required, Validators.minLength(2)]],\n        lastName: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        confirmPassword: ['', [Validators.required]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    ngOnInit() {\n      // Redirect if already authenticated\n      if (this.authService.isAuthenticated()) {\n        this.router.navigate(['/dashboard']);\n      }\n    }\n    passwordMatchValidator(form) {\n      const password = form.get('password');\n      const confirmPassword = form.get('confirmPassword');\n      if (password && confirmPassword && password.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n      } else if (confirmPassword?.hasError('passwordMismatch')) {\n        confirmPassword.setErrors(null);\n      }\n      return null;\n    }\n    onSubmit() {\n      if (this.registerForm.valid) {\n        this.isLoading = true;\n        const userData = {\n          firstName: this.registerForm.value.firstName,\n          lastName: this.registerForm.value.lastName,\n          email: this.registerForm.value.email,\n          password: this.registerForm.value.password\n        };\n        this.authService.register(userData).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.snackBar.open('Compte créé avec succès!', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate(['/dashboard']);\n          },\n          error: error => {\n            this.isLoading = false;\n            let errorMessage = 'Erreur lors de la création du compte. Veuillez réessayer.';\n            if (error.status === 409) {\n              errorMessage = 'Un compte existe déjà avec cet email.';\n            } else if (error.status === 0) {\n              errorMessage = 'Impossible de se connecter au serveur.';\n            }\n            this.snackBar.open(errorMessage, 'Fermer', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      }\n    }\n    getErrorMessage(field) {\n      const control = this.registerForm.get(field);\n      if (control?.hasError('required')) {\n        const fieldNames = {\n          firstName: 'Prénom',\n          lastName: 'Nom',\n          email: 'Email',\n          password: 'Mot de passe',\n          confirmPassword: 'Confirmation du mot de passe'\n        };\n        return `${fieldNames[field]} requis`;\n      }\n      if (control?.hasError('email')) {\n        return 'Email invalide';\n      }\n      if (control?.hasError('minlength')) {\n        const minLength = control.errors?.['minlength'].requiredLength;\n        return `Minimum ${minLength} caractères requis`;\n      }\n      if (control?.hasError('passwordMismatch')) {\n        return 'Les mots de passe ne correspondent pas';\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function RegisterComponent_Factory(t) {\n        return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegisterComponent,\n        selectors: [[\"app-register\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 57,\n        vars: 18,\n        consts: [[1, \"register-container\"], [1, \"register-card-container\"], [1, \"register-card\"], [1, \"register-title\"], [1, \"register-icon\"], [1, \"register-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Jean\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Dupont\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"register-button\", \"full-width\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"register-spinner\", 4, \"ngIf\"], [1, \"register-actions\"], [1, \"login-link\"], [\"routerLink\", \"/login\", 1, \"login-link-text\"], [\"diameter\", \"20\", 1, \"register-spinner\"]],\n        template: function RegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\")(4, \"mat-card-title\", 3)(5, \"mat-icon\", 4);\n            i0.ɵɵtext(6, \"person_add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7, \" Cr\\u00E9er un compte Indezy \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n            i0.ɵɵtext(9, \" Rejoignez la communaut\\u00E9 des freelances organis\\u00E9s \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_11_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(12, \"div\", 6)(13, \"mat-form-field\", 7)(14, \"mat-label\");\n            i0.ɵɵtext(15, \"Pr\\u00E9nom\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"input\", 8);\n            i0.ɵɵelementStart(17, \"mat-icon\", 9);\n            i0.ɵɵtext(18, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, RegisterComponent_mat_error_19_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"mat-form-field\", 7)(21, \"mat-label\");\n            i0.ɵɵtext(22, \"Nom\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(23, \"input\", 11);\n            i0.ɵɵtemplate(24, RegisterComponent_mat_error_24_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"mat-form-field\", 12)(26, \"mat-label\");\n            i0.ɵɵtext(27, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(28, \"input\", 13);\n            i0.ɵɵelementStart(29, \"mat-icon\", 9);\n            i0.ɵɵtext(30, \"email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(31, RegisterComponent_mat_error_31_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"mat-form-field\", 12)(33, \"mat-label\");\n            i0.ɵɵtext(34, \"Mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 14);\n            i0.ɵɵelementStart(36, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_36_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(37, \"mat-icon\");\n            i0.ɵɵtext(38);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(39, RegisterComponent_mat_error_39_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"mat-form-field\", 12)(41, \"mat-label\");\n            i0.ɵɵtext(42, \"Confirmer le mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(43, \"input\", 16);\n            i0.ɵɵelementStart(44, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_44_listener() {\n              return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n            });\n            i0.ɵɵelementStart(45, \"mat-icon\");\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(47, RegisterComponent_mat_error_47_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"button\", 17);\n            i0.ɵɵtemplate(49, RegisterComponent_mat_spinner_49_Template, 1, 0, \"mat-spinner\", 18)(50, RegisterComponent_span_50_Template, 2, 0, \"span\", 10)(51, RegisterComponent_span_51_Template, 2, 0, \"span\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(52, \"mat-card-actions\", 19)(53, \"p\", 20);\n            i0.ɵɵtext(54, \" D\\u00E9j\\u00E0 un compte ? \");\n            i0.ɵɵelementStart(55, \"a\", 21);\n            i0.ɵɵtext(56, \"Se connecter\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_8_0;\n            let tmp_13_0;\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance();\n            i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n            i0.ɵɵadvance();\n            i0.ɵɵattribute(\"aria-label\", \"Hide confirm password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatSnackBarModule, MatProgressSpinnerModule, i11.MatProgressSpinner],\n        styles: [\".register-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.register-card-container[_ngcontent-%COMP%]{width:100%;max-width:500px}.register-card[_ngcontent-%COMP%]{padding:20px;border-radius:12px;box-shadow:0 8px 32px #0000001a}.register-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:24px;font-weight:600;color:#333;margin-bottom:8px}.register-icon[_ngcontent-%COMP%]{font-size:28px;color:#667eea}.register-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;margin-top:20px}.name-row[_ngcontent-%COMP%]{display:flex;gap:16px}.full-width[_ngcontent-%COMP%]{width:100%}.half-width[_ngcontent-%COMP%]{flex:1}.register-button[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:8px;position:relative}.register-spinner[_ngcontent-%COMP%]{margin-right:8px}.register-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;padding-top:16px}.login-link[_ngcontent-%COMP%]{margin:0;text-align:center;color:#666}.login-link-text[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.login-link-text[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 600px){.register-container[_ngcontent-%COMP%]{padding:10px}.register-card[_ngcontent-%COMP%]{padding:16px}.register-title[_ngcontent-%COMP%]{font-size:20px}.name-row[_ngcontent-%COMP%]{flex-direction:column;gap:16px}.half-width[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}