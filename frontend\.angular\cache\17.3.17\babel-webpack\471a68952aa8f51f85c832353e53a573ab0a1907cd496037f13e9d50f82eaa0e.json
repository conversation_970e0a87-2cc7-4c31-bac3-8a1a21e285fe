{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBarModule } from '@angular/material/snackbar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nlet ClientDetailComponent = class ClientDetailComponent {\n  constructor(clientService, router, route, snackBar) {\n    this.clientService = clientService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.clientId = +params['id'];\n        this.loadClient();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadClient() {\n    if (!this.clientId) return;\n    this.isLoading = true;\n    this.clientService.getClient(this.clientId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: client => {\n        if (client) {\n          this.client = client;\n        } else {\n          this.snackBar.open('Client non trouvé', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/clients']);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading client:', error);\n        this.snackBar.open('Erreur lors du chargement du client', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onEdit() {\n    if (this.client) {\n      this.router.navigate(['/clients', this.client.id, 'edit']);\n    }\n  }\n  onDelete() {\n    if (!this.client) return;\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${this.client.name}\" ?`)) {\n      this.clientService.deleteClient(this.client.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/clients']);\n        },\n        error: error => {\n          console.error('Error deleting client:', error);\n          this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  onBack() {\n    this.router.navigate(['/clients']);\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n  getStatusLabel(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  openWebsite() {\n    if (this.client?.website) {\n      window.open(this.client.website, '_blank');\n    }\n  }\n  sendEmail() {\n    if (this.client?.email) {\n      window.location.href = `mailto:${this.client.email}`;\n    }\n  }\n  callPhone() {\n    if (this.client?.phone) {\n      window.location.href = `tel:${this.client.phone}`;\n    }\n  }\n};\nClientDetailComponent = __decorate([Component({\n  selector: 'app-client-detail',\n  standalone: true,\n  imports: [CommonModule, RouterModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatDividerModule, MatSnackBarModule, MatProgressSpinnerModule],\n  templateUrl: './client-detail.component.html',\n  styleUrls: ['./client-detail.component.scss']\n})], ClientDetailComponent);\nexport { ClientDetailComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatDividerModule", "MatSnackBarModule", "MatProgressSpinnerModule", "Subject", "takeUntil", "ClientDetailComponent", "constructor", "clientService", "router", "route", "snackBar", "isLoading", "destroy$", "ngOnInit", "params", "pipe", "subscribe", "clientId", "loadClient", "ngOnDestroy", "next", "complete", "getClient", "client", "open", "duration", "navigate", "error", "console", "onEdit", "id", "onDelete", "confirm", "name", "deleteClient", "onBack", "getStatusColor", "status", "getStatusLabel", "formatDate", "date", "Date", "toLocaleDateString", "year", "month", "day", "openWebsite", "website", "window", "sendEmail", "email", "location", "href", "callPhone", "phone", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-detail\\client-detail.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snackbar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { ClientService, ClientDto } from '../../../services/client.service';\n\n@Component({\n  selector: 'app-client-detail',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatDividerModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './client-detail.component.html',\n  styleUrls: ['./client-detail.component.scss']\n})\nexport class ClientDetailComponent implements OnInit, OnDestroy {\n  client?: ClientDto;\n  isLoading = false;\n  clientId?: number;\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private clientService: ClientService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.clientId = +params['id'];\n        this.loadClient();\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private loadClient(): void {\n    if (!this.clientId) return;\n    \n    this.isLoading = true;\n    this.clientService.getClient(this.clientId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (client) => {\n          if (client) {\n            this.client = client;\n          } else {\n            this.snackBar.open('Client non trouvé', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/clients']);\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading client:', error);\n          this.snackBar.open('Erreur lors du chargement du client', 'Fermer', { duration: 3000 });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onEdit(): void {\n    if (this.client) {\n      this.router.navigate(['/clients', this.client.id, 'edit']);\n    }\n  }\n\n  onDelete(): void {\n    if (!this.client) return;\n    \n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${this.client.name}\" ?`)) {\n      this.clientService.deleteClient(this.client.id)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Client supprimé avec succès', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/clients']);\n          },\n          error: (error) => {\n            console.error('Error deleting client:', error);\n            this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', { duration: 3000 });\n          }\n        });\n    }\n  }\n\n  onBack(): void {\n    this.router.navigate(['/clients']);\n  }\n\n  getStatusColor(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n\n  getStatusLabel(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  openWebsite(): void {\n    if (this.client?.website) {\n      window.open(this.client.website, '_blank');\n    }\n  }\n\n  sendEmail(): void {\n    if (this.client?.email) {\n      window.location.href = `mailto:${this.client.email}`;\n    }\n  }\n\n  callPhone(): void {\n    if (this.client?.phone) {\n      window.location.href = `tel:${this.client.phone}`;\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgC,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAqB,4BAA4B;AAC3E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAqBlC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAOhCC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAHrB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,SAAS,GAAG,KAAK;IAGT,KAAAC,QAAQ,GAAG,IAAIT,OAAO,EAAQ;EAOnC;EAEHU,QAAQA,CAAA;IACN,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,IAAI,CAACX,SAAS,CAAC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAG;MAClE,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACG,QAAQ,GAAG,CAACH,MAAM,CAAC,IAAI,CAAC;QAC7B,IAAI,CAACI,UAAU,EAAE;;IAErB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACpB,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE;EAC1B;EAEQH,UAAUA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;IAEpB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,aAAa,CAACe,SAAS,CAAC,IAAI,CAACL,QAAQ,CAAC,CACxCF,IAAI,CAACX,SAAS,CAAC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTI,IAAI,EAAGG,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACA,MAAM,GAAGA,MAAM;SACrB,MAAM;UACL,IAAI,CAACb,QAAQ,CAACc,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACrE,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;QAEpC,IAAI,CAACf,SAAS,GAAG,KAAK;MACxB,CAAC;MACDgB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACjB,QAAQ,CAACc,IAAI,CAAC,qCAAqC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAACd,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAkB,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAI,CAACf,MAAM,CAACkB,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACH,MAAM,CAACO,EAAE,EAAE,MAAM,CAAC,CAAC;;EAE9D;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE;IAElB,IAAIS,OAAO,CAAC,iDAAiD,IAAI,CAACT,MAAM,CAACU,IAAI,KAAK,CAAC,EAAE;MACnF,IAAI,CAAC1B,aAAa,CAAC2B,YAAY,CAAC,IAAI,CAACX,MAAM,CAACO,EAAE,CAAC,CAC5Cf,IAAI,CAACX,SAAS,CAAC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;QACTI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACV,QAAQ,CAACc,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAC/E,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACjB,QAAQ,CAACc,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC7F;OACD,CAAC;;EAER;EAEAU,MAAMA,CAAA;IACJ,IAAI,CAAC3B,MAAM,CAACkB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAU,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,UAAU;QACb,OAAO,QAAQ;MACjB;QACE,OAAO,EAAE;;EAEf;EAEAC,cAAcA,CAACD,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB;QACE,OAAOA,MAAM;;EAEnB;EAEAE,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvB,MAAM,EAAEwB,OAAO,EAAE;MACxBC,MAAM,CAACxB,IAAI,CAAC,IAAI,CAACD,MAAM,CAACwB,OAAO,EAAE,QAAQ,CAAC;;EAE9C;EAEAE,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1B,MAAM,EAAE2B,KAAK,EAAE;MACtBF,MAAM,CAACG,QAAQ,CAACC,IAAI,GAAG,UAAU,IAAI,CAAC7B,MAAM,CAAC2B,KAAK,EAAE;;EAExD;EAEAG,SAASA,CAAA;IACP,IAAI,IAAI,CAAC9B,MAAM,EAAE+B,KAAK,EAAE;MACtBN,MAAM,CAACG,QAAQ,CAACC,IAAI,GAAG,OAAO,IAAI,CAAC7B,MAAM,CAAC+B,KAAK,EAAE;;EAErD;CACD;AApIYjD,qBAAqB,GAAAkD,UAAA,EAjBjC9D,SAAS,CAAC;EACT+D,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhE,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,wBAAwB,CACzB;EACDyD,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC;CAC7C,CAAC,C,EACWvD,qBAAqB,CAoIjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}