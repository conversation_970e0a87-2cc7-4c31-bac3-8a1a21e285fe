{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatMenuModule } from '@angular/material/menu';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/freelance.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/sidenav\";\nimport * as i8 from \"@angular/material/list\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/card\";\nimport * as i12 from \"@angular/material/menu\";\nconst _c0 = a0 => [\"/projects\", a0];\nfunction DashboardComponent_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 34)(1, \"mat-icon\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r3.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.label);\n  }\n}\nfunction DashboardComponent_div_92_mat_card_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, project_r4.workMode));\n  }\n}\nfunction DashboardComponent_div_92_mat_card_1_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r4.techStack, \" \");\n  }\n}\nfunction DashboardComponent_div_92_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 39)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 40)(8, \"div\", 41)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_92_mat_card_1_div_13_Template, 6, 3, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, DashboardComponent_div_92_mat_card_1_p_14_Template, 2, 1, \"p\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-card-actions\")(16, \"button\", 44);\n    i0.ɵɵtext(17, \" Voir d\\u00E9tails \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r4.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r4.clientName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", project_r4.dailyRate, \"\\u20AC/jour\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r4.workMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r4.techStack);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(6, _c0, project_r4.id));\n  }\n}\nfunction DashboardComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, DashboardComponent_div_92_mat_card_1_Template, 18, 8, \"mat-card\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.recentProjects);\n  }\n}\nfunction DashboardComponent_ng_template_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 47)(1, \"mat-card-content\")(2, \"div\", 48)(3, \"mat-icon\", 49);\n    i0.ɵɵtext(4, \"work_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Aucun projet pour le moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Commencez par ajouter votre premier projet !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 50);\n    i0.ɵɵtext(10, \" Ajouter un projet \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, projectService, freelanceService) {\n    this.authService = authService;\n    this.projectService = projectService;\n    this.freelanceService = freelanceService;\n    this.freelanceProfile = null;\n    this.recentProjects = [];\n    this.stats = {\n      totalProjects: 0,\n      averageDailyRate: 0,\n      totalRevenue: 0,\n      activeProjects: 0\n    };\n    this.menuItems = [{\n      icon: 'dashboard',\n      label: 'Tableau de bord',\n      route: '/dashboard'\n    }, {\n      icon: 'work',\n      label: 'Projets',\n      route: '/projects'\n    }, {\n      icon: 'business',\n      label: 'Clients',\n      route: '/clients'\n    }, {\n      icon: 'contacts',\n      label: 'Contacts',\n      route: '/contacts'\n    }, {\n      icon: 'source',\n      label: 'Sources',\n      route: '/sources'\n    }, {\n      icon: 'person',\n      label: 'Profil',\n      route: '/profile'\n    }];\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getUser();\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    if (this.currentUser?.id) {\n      // Load freelance profile\n      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n        next: profile => {\n          this.freelanceProfile = profile;\n          this.updateStats(profile);\n        },\n        error: error => console.error('Error loading profile:', error)\n      });\n      // Load recent projects\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: projects => {\n          this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n        },\n        error: error => console.error('Error loading projects:', error)\n      });\n    }\n  }\n  updateStats(profile) {\n    this.stats = {\n      totalProjects: profile.totalProjects || 0,\n      averageDailyRate: profile.averageDailyRate || 0,\n      totalRevenue: this.calculateTotalRevenue(),\n      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n    };\n  }\n  calculateTotalRevenue() {\n    return this.recentProjects.reduce((total, project) => {\n      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n        const monthlyDays = project.daysPerYear / 12;\n        return total + project.dailyRate * monthlyDays * project.durationInMonths;\n      }\n      return total;\n    }, 0);\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.FreelanceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 95,\n      vars: 18,\n      consts: [[\"userMenu\", \"matMenu\"], [\"sidenav\", \"\"], [\"noProjects\", \"\"], [1, \"dashboard-container\"], [\"color\", \"primary\", 1, \"dashboard-toolbar\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [\"mat-button\", \"\", 1, \"user-button\", 3, \"matMenuTriggerFor\"], [1, \"user-name\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"sidenav-container\"], [\"mode\", \"side\", \"opened\", \"\", 1, \"sidenav\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"main-content\"], [1, \"dashboard-content\"], [1, \"welcome-section\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-icon\", \"projects\"], [1, \"stat-info\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-icon\", \"rate\"], [1, \"stat-icon\", \"revenue\"], [1, \"stat-icon\", \"active\"], [1, \"recent-projects-section\"], [1, \"section-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects\"], [\"class\", \"projects-grid\", 4, \"ngIf\", \"ngIfElse\"], [\"mat-list-item\", \"\", \"routerLinkActive\", \"active-nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\"], [\"matListItemTitle\", \"\"], [1, \"projects-grid\"], [\"class\", \"project-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"project-card\"], [1, \"project-info\"], [1, \"project-rate\"], [\"class\", \"project-mode\", 4, \"ngIf\"], [\"class\", \"project-tech\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"routerLink\"], [1, \"project-mode\"], [1, \"project-tech\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-toolbar\", 4)(2, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const sidenav_r2 = i0.ɵɵreference(29);\n            return i0.ɵɵresetView(sidenav_r2.toggle());\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"span\", 6);\n          i0.ɵɵtext(6, \"Indezy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 7);\n          i0.ɵɵelementStart(8, \"button\", 8)(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\", 9);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"arrow_drop_down\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-menu\", null, 0)(17, \"button\", 10)(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21, \"Profil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"D\\u00E9connexion\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"mat-sidenav-container\", 12)(28, \"mat-sidenav\", 13, 1)(30, \"mat-nav-list\");\n          i0.ɵɵtemplate(31, DashboardComponent_a_31_Template, 5, 3, \"a\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"mat-sidenav-content\", 15)(33, \"div\", 16)(34, \"div\", 17)(35, \"h1\", 18);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"p\", 19);\n          i0.ɵɵtext(38, \" Voici un aper\\u00E7u de vos missions freelance \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 20)(40, \"mat-card\", 21)(41, \"mat-card-content\")(42, \"div\", 22)(43, \"div\", 23)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"work\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 24)(47, \"div\", 25);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 26);\n          i0.ɵɵtext(50, \"Projets totaux\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(51, \"mat-card\", 21)(52, \"mat-card-content\")(53, \"div\", 22)(54, \"div\", 27)(55, \"mat-icon\");\n          i0.ɵɵtext(56, \"euro\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 24)(58, \"div\", 25);\n          i0.ɵɵtext(59);\n          i0.ɵɵpipe(60, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 26);\n          i0.ɵɵtext(62, \"TJM moyen\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(63, \"mat-card\", 21)(64, \"mat-card-content\")(65, \"div\", 22)(66, \"div\", 28)(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"trending_up\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 24)(70, \"div\", 25);\n          i0.ɵɵtext(71);\n          i0.ɵɵpipe(72, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 26);\n          i0.ɵɵtext(74, \"Revenus estim\\u00E9s\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(75, \"mat-card\", 21)(76, \"mat-card-content\")(77, \"div\", 22)(78, \"div\", 29)(79, \"mat-icon\");\n          i0.ɵɵtext(80, \"play_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 24)(82, \"div\", 25);\n          i0.ɵɵtext(83);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 26);\n          i0.ɵɵtext(85, \"Projets actifs\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(86, \"div\", 30)(87, \"div\", 31)(88, \"h2\");\n          i0.ɵɵtext(89, \"Projets r\\u00E9cents\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"button\", 32);\n          i0.ɵɵtext(91, \" Voir tous les projets \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(92, DashboardComponent_div_92_Template, 2, 1, \"div\", 33)(93, DashboardComponent_ng_template_93_Template, 11, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const userMenu_r6 = i0.ɵɵreference(16);\n          const noProjects_r7 = i0.ɵɵreference(94);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r6);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate2(\"\", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName, \"\");\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate2(\" \", ctx.getGreeting(), \", \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" ! \\uD83D\\uDC4B \");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.totalProjects);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(60, 12, ctx.stats.averageDailyRate, \"1.0-0\"), \"\\u20AC\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(72, 15, ctx.stats.totalRevenue, \"1.0-0\"), \"\\u20AC\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.activeProjects);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentProjects.length > 0)(\"ngIfElse\", noProjects_r7);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, i4.TitleCasePipe, RouterModule, i5.RouterLink, i5.RouterLinkActive, MatToolbarModule, i6.MatToolbar, MatSidenavModule, i7.MatSidenav, i7.MatSidenavContainer, i7.MatSidenavContent, MatListModule, i8.MatNavList, i8.MatListItem, i8.MatListItemIcon, i8.MatListItemTitle, MatIconModule, i9.MatIcon, MatButtonModule, i10.MatButton, i10.MatIconButton, MatCardModule, i11.MatCard, i11.MatCardActions, i11.MatCardContent, i11.MatCardHeader, i11.MatCardSubtitle, i11.MatCardTitle, MatMenuModule, i12.MatMenu, i12.MatMenuItem, i12.MatMenuTrigger],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.dashboard-toolbar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n}\\n\\n.menu-button[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n\\n.toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.user-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n}\\n\\n.sidenav-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.sidenav[_ngcontent-%COMP%] {\\n  width: 250px;\\n  border-right: 1px solid #e0e0e0;\\n}\\n\\n.active-nav-item[_ngcontent-%COMP%] {\\n  background-color: rgba(63, 81, 181, 0.1);\\n  color: #3f51b5;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.welcome-title[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.welcome-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 32px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.stat-icon.projects[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n.stat-icon.rate[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n.stat-icon.revenue[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n}\\n.stat-icon.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\\n}\\n\\n.stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  line-height: 1;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin-top: 4px;\\n}\\n\\n.recent-projects-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n  color: #333;\\n}\\n\\n.projects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n\\n.project-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.project-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.project-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n.project-info[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n.project-info[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.project-tech[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #888;\\n  margin: 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.empty-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0 0 24px 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .sidenav[_ngcontent-%COMP%] {\\n    width: 200px;\\n  }\\n  .dashboard-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .welcome-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 16px;\\n  }\\n  .projects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9kYXNoYm9hcmQvZGFzaGJvYXJkLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7RUFDQSxNQUFBO0VBQ0EsYUFBQTtBQUNGOztBQUVBO0VBQ0Usa0JBQUE7QUFDRjs7QUFFQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtBQUNGOztBQUVBO0VBQ0UsWUFBQTtFQUNBLCtCQUFBO0FBQ0Y7O0FBRUE7RUFDRSx3Q0FBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLFVBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7QUFDRjs7QUFFQTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsU0FBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxtQkFBQTtFQUNBLHdDQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFDRjtBQUNFO0VBQ0UsZUFBQTtFQUNBLFlBQUE7QUFDSjtBQUVFO0VBQ0UsNkRBQUE7QUFBSjtBQUdFO0VBQ0UsNkRBQUE7QUFESjtBQUlFO0VBQ0UsNkRBQUE7QUFGSjtBQUtFO0VBQ0UsNkRBQUE7QUFISjs7QUFPQTtFQUNFLE9BQUE7QUFKRjs7QUFPQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxjQUFBO0FBSkY7O0FBT0E7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7QUFKRjs7QUFPQTtFQUNFLG1CQUFBO0FBSkY7O0FBT0E7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBSkY7QUFNRTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0FBSko7O0FBUUE7RUFDRSxhQUFBO0VBQ0EsNERBQUE7RUFDQSxTQUFBO0FBTEY7O0FBUUE7RUFDRSxtQkFBQTtFQUNBLHdDQUFBO0VBQ0EscURBQUE7QUFMRjtBQU9FO0VBQ0UsMkJBQUE7RUFDQSwwQ0FBQTtBQUxKOztBQVNBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQU5GO0FBUUU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUFOSjtBQVFJO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBTk47O0FBV0E7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7QUFSRjs7QUFXQTtFQUNFLG1CQUFBO0VBQ0Esd0NBQUE7QUFSRjs7QUFXQTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7QUFSRjtBQVVFO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FBUko7QUFXRTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtBQVRKO0FBWUU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7QUFWSjs7QUFlQTtFQUNFO0lBQ0UsWUFBQTtFQVpGO0VBZUE7SUFDRSxhQUFBO0VBYkY7RUFnQkE7SUFDRSxlQUFBO0VBZEY7RUFpQkE7SUFDRSwyREFBQTtJQUNBLFNBQUE7RUFmRjtFQWtCQTtJQUNFLDBCQUFBO0VBaEJGO0VBbUJBO0lBQ0Usc0JBQUE7SUFDQSx1QkFBQTtJQUNBLFNBQUE7RUFqQkY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgaGVpZ2h0OiAxMDB2aDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLmRhc2hib2FyZC10b29sYmFyIHtcbiAgcG9zaXRpb246IHN0aWNreTtcbiAgdG9wOiAwO1xuICB6LWluZGV4OiAxMDAwO1xufVxuXG4ubWVudS1idXR0b24ge1xuICBtYXJnaW4tcmlnaHQ6IDE2cHg7XG59XG5cbi50b29sYmFyLXRpdGxlIHtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4uc3BhY2VyIHtcbiAgZmxleDogMSAxIGF1dG87XG59XG5cbi51c2VyLWJ1dHRvbiB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogOHB4O1xufVxuXG4udXNlci1uYW1lIHtcbiAgbWFyZ2luOiAwIDhweDtcbn1cblxuLnNpZGVuYXYtY29udGFpbmVyIHtcbiAgZmxleDogMTtcbn1cblxuLnNpZGVuYXYge1xuICB3aWR0aDogMjUwcHg7XG4gIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlMGUwZTA7XG59XG5cbi5hY3RpdmUtbmF2LWl0ZW0ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDYzLCA4MSwgMTgxLCAwLjEpO1xuICBjb2xvcjogIzNmNTFiNTtcbn1cblxuLm1haW4tY29udGVudCB7XG4gIHBhZGRpbmc6IDA7XG59XG5cbi5kYXNoYm9hcmQtY29udGVudCB7XG4gIHBhZGRpbmc6IDI0cHg7XG4gIG1heC13aWR0aDogMTIwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbn1cblxuLndlbGNvbWUtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG59XG5cbi53ZWxjb21lLXRpdGxlIHtcbiAgZm9udC1zaXplOiAzMnB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICBtYXJnaW46IDAgMCA4cHggMDtcbiAgY29sb3I6ICMzMzM7XG59XG5cbi53ZWxjb21lLXN1YnRpdGxlIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBjb2xvcjogIzY2NjtcbiAgbWFyZ2luOiAwO1xufVxuXG4uc3RhdHMtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjUwcHgsIDFmcikpO1xuICBnYXA6IDIwcHg7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG59XG5cbi5zdGF0LWNhcmQge1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4uc3RhdC1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxNnB4O1xufVxuXG4uc3RhdC1pY29uIHtcbiAgd2lkdGg6IDQ4cHg7XG4gIGhlaWdodDogNDhweDtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIFxuICBtYXQtaWNvbiB7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgfVxuICBcbiAgJi5wcm9qZWN0cyB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgfVxuICBcbiAgJi5yYXRlIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjA5M2ZiIDAlLCAjZjU1NzZjIDEwMCUpO1xuICB9XG4gIFxuICAmLnJldmVudWUge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0ZmFjZmUgMCUsICMwMGYyZmUgMTAwJSk7XG4gIH1cbiAgXG4gICYuYWN0aXZlIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDNlOTdiIDAlLCAjMzhmOWQ3IDEwMCUpO1xuICB9XG59XG5cbi5zdGF0LWluZm8ge1xuICBmbGV4OiAxO1xufVxuXG4uc3RhdC1udW1iZXIge1xuICBmb250LXNpemU6IDI0cHg7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMzMzO1xuICBsaW5lLWhlaWdodDogMTtcbn1cblxuLnN0YXQtbGFiZWwge1xuICBmb250LXNpemU6IDE0cHg7XG4gIGNvbG9yOiAjNjY2O1xuICBtYXJnaW4tdG9wOiA0cHg7XG59XG5cbi5yZWNlbnQtcHJvamVjdHMtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG59XG5cbi5zZWN0aW9uLWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgXG4gIGgyIHtcbiAgICBmb250LXNpemU6IDI0cHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBtYXJnaW46IDA7XG4gICAgY29sb3I6ICMzMzM7XG4gIH1cbn1cblxuLnByb2plY3RzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gIGdhcDogMjBweDtcbn1cblxuLnByb2plY3QtY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2UsIGJveC1zaGFkb3cgMC4ycyBlYXNlO1xuICBcbiAgJjpob3ZlciB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbiAgfVxufVxuXG4ucHJvamVjdC1pbmZvIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxNnB4O1xuICBtYXJnaW4tYm90dG9tOiAxMnB4O1xuICBcbiAgPiBkaXYge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDRweDtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY29sb3I6ICM2NjY7XG4gICAgXG4gICAgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICBoZWlnaHQ6IDE2cHg7XG4gICAgfVxuICB9XG59XG5cbi5wcm9qZWN0LXRlY2gge1xuICBmb250LXNpemU6IDE0cHg7XG4gIGNvbG9yOiAjODg4O1xuICBtYXJnaW46IDA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xufVxuXG4uZW1wdHktc3RhdGUge1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4uZW1wdHktY29udGVudCB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZzogNDBweCAyMHB4O1xuICBcbiAgLmVtcHR5LWljb24ge1xuICAgIGZvbnQtc2l6ZTogNjRweDtcbiAgICB3aWR0aDogNjRweDtcbiAgICBoZWlnaHQ6IDY0cHg7XG4gICAgY29sb3I6ICNjY2M7XG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgfVxuICBcbiAgaDMge1xuICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgIGNvbG9yOiAjMzMzO1xuICB9XG4gIFxuICBwIHtcbiAgICBjb2xvcjogIzY2NjtcbiAgICBtYXJnaW46IDAgMCAyNHB4IDA7XG4gIH1cbn1cblxuLy8gUmVzcG9uc2l2ZSBkZXNpZ25cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuc2lkZW5hdiB7XG4gICAgd2lkdGg6IDIwMHB4O1xuICB9XG4gIFxuICAuZGFzaGJvYXJkLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDE2cHg7XG4gIH1cbiAgXG4gIC53ZWxjb21lLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDI0cHg7XG4gIH1cbiAgXG4gIC5zdGF0cy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDIwMHB4LCAxZnIpKTtcbiAgICBnYXA6IDE2cHg7XG4gIH1cbiAgXG4gIC5wcm9qZWN0cy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuICBcbiAgLnNlY3Rpb24taGVhZGVyIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgIGdhcDogMTJweDtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatCardModule", "MatMenuModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r3", "route", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "ɵɵpipeBind1", "project_r4", "workMode", "ɵɵtextInterpolate1", "techStack", "ɵɵtemplate", "DashboardComponent_div_92_mat_card_1_div_13_Template", "DashboardComponent_div_92_mat_card_1_p_14_Template", "role", "clientName", "dailyRate", "ɵɵpureFunction1", "_c0", "id", "DashboardComponent_div_92_mat_card_1_Template", "ctx_r4", "recentProjects", "DashboardComponent", "constructor", "authService", "projectService", "freelanceService", "freelanceProfile", "stats", "totalProjects", "averageDailyRate", "totalRevenue", "activeProjects", "menuItems", "ngOnInit", "currentUser", "getUser", "loadDashboardData", "getByIdWithProjects", "subscribe", "next", "profile", "updateStats", "error", "console", "getByFreelanceId", "projects", "slice", "calculateTotalRevenue", "filter", "p", "startDate", "Date", "length", "reduce", "total", "project", "durationInMonths", "daysPerYear", "monthlyDays", "logout", "getGreeting", "hour", "getHours", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ProjectService", "i3", "FreelanceService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "DashboardComponent_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "sidenav_r2", "ɵɵreference", "ɵɵresetView", "toggle", "ɵɵelement", "DashboardComponent_Template_button_click_22_listener", "DashboardComponent_a_31_Template", "DashboardComponent_div_92_Template", "DashboardComponent_ng_template_93_Template", "ɵɵtemplateRefExtractor", "userMenu_r6", "ɵɵtextInterpolate2", "firstName", "lastName", "ɵɵpipeBind2", "noProjects_r7", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "TitleCasePipe", "i5", "RouterLink", "RouterLinkActive", "i6", "MatToolbar", "i7", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i8", "MatNavList", "MatListItem", "MatListItemIcon", "MatListItemTitle", "i9", "MatIcon", "i10", "MatButton", "MatIconButton", "i11", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i12", "MatMenu", "MatMenuItem", "MatMenuTrigger", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { AuthService } from '../../services/auth.service';\nimport { ProjectService, ProjectDto } from '../../services/project.service';\nimport { FreelanceService, FreelanceDto } from '../../services/freelance.service';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatCardModule,\n    MatMenuModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any;\n  freelanceProfile: FreelanceDto | null = null;\n  recentProjects: ProjectDto[] = [];\n  stats = {\n    totalProjects: 0,\n    averageDailyRate: 0,\n    totalRevenue: 0,\n    activeProjects: 0\n  };\n\n  menuItems = [\n    { icon: 'dashboard', label: 'Tableau de bord', route: '/dashboard' },\n    { icon: 'work', label: 'Projets', route: '/projects' },\n    { icon: 'business', label: 'Clients', route: '/clients' },\n    { icon: 'contacts', label: 'Contacts', route: '/contacts' },\n    { icon: 'source', label: 'Sources', route: '/sources' },\n    { icon: 'person', label: 'Profil', route: '/profile' }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private projectService: ProjectService,\n    private freelanceService: FreelanceService\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getUser();\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    if (this.currentUser?.id) {\n      // Load freelance profile\n      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n        next: (profile) => {\n          this.freelanceProfile = profile;\n          this.updateStats(profile);\n        },\n        error: (error) => console.error('Error loading profile:', error)\n      });\n\n      // Load recent projects\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: (projects) => {\n          this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n        },\n        error: (error) => console.error('Error loading projects:', error)\n      });\n    }\n  }\n\n  updateStats(profile: FreelanceDto): void {\n    this.stats = {\n      totalProjects: profile.totalProjects || 0,\n      averageDailyRate: profile.averageDailyRate || 0,\n      totalRevenue: this.calculateTotalRevenue(),\n      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n    };\n  }\n\n  calculateTotalRevenue(): number {\n    return this.recentProjects.reduce((total, project) => {\n      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n        const monthlyDays = project.daysPerYear / 12;\n        return total + (project.dailyRate * monthlyDays * project.durationInMonths);\n      }\n      return total;\n    }, 0);\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Top Navigation -->\n  <mat-toolbar color=\"primary\" class=\"dashboard-toolbar\">\n    <button mat-icon-button (click)=\"sidenav.toggle()\" class=\"menu-button\">\n      <mat-icon>menu</mat-icon>\n    </button>\n    \n    <span class=\"toolbar-title\">Indezy</span>\n    \n    <span class=\"spacer\"></span>\n    \n    <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-button\">\n      <mat-icon>account_circle</mat-icon>\n      <span class=\"user-name\">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</span>\n      <mat-icon>arrow_drop_down</mat-icon>\n    </button>\n    \n    <mat-menu #userMenu=\"matMenu\">\n      <button mat-menu-item routerLink=\"/profile\">\n        <mat-icon>person</mat-icon>\n        <span>Profil</span>\n      </button>\n      <button mat-menu-item (click)=\"logout()\">\n        <mat-icon>logout</mat-icon>\n        <span>Déconnexion</span>\n      </button>\n    </mat-menu>\n  </mat-toolbar>\n\n  <!-- Sidenav -->\n  <mat-sidenav-container class=\"sidenav-container\">\n    <mat-sidenav #sidenav mode=\"side\" opened class=\"sidenav\">\n      <mat-nav-list>\n        <a mat-list-item \n           *ngFor=\"let item of menuItems\" \n           [routerLink]=\"item.route\"\n           routerLinkActive=\"active-nav-item\">\n          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>\n          <span matListItemTitle>{{ item.label }}</span>\n        </a>\n      </mat-nav-list>\n    </mat-sidenav>\n\n    <!-- Main Content -->\n    <mat-sidenav-content class=\"main-content\">\n      <div class=\"dashboard-content\">\n        <!-- Welcome Section -->\n        <div class=\"welcome-section\">\n          <h1 class=\"welcome-title\">\n            {{ getGreeting() }}, {{ currentUser?.firstName }} ! 👋\n          </h1>\n          <p class=\"welcome-subtitle\">\n            Voici un aperçu de vos missions freelance\n          </p>\n        </div>\n\n        <!-- Stats Cards -->\n        <div class=\"stats-grid\">\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon projects\">\n                  <mat-icon>work</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalProjects }}</div>\n                  <div class=\"stat-label\">Projets totaux</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon rate\">\n                  <mat-icon>euro</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.averageDailyRate | number:'1.0-0' }}€</div>\n                  <div class=\"stat-label\">TJM moyen</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon revenue\">\n                  <mat-icon>trending_up</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalRevenue | number:'1.0-0' }}€</div>\n                  <div class=\"stat-label\">Revenus estimés</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon active\">\n                  <mat-icon>play_circle</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.activeProjects }}</div>\n                  <div class=\"stat-label\">Projets actifs</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Recent Projects -->\n        <div class=\"recent-projects-section\">\n          <div class=\"section-header\">\n            <h2>Projets récents</h2>\n            <button mat-raised-button color=\"primary\" routerLink=\"/projects\">\n              Voir tous les projets\n            </button>\n          </div>\n\n          <div class=\"projects-grid\" *ngIf=\"recentProjects.length > 0; else noProjects\">\n            <mat-card class=\"project-card\" *ngFor=\"let project of recentProjects\">\n              <mat-card-header>\n                <mat-card-title>{{ project.role }}</mat-card-title>\n                <mat-card-subtitle>{{ project.clientName }}</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"project-info\">\n                  <div class=\"project-rate\">\n                    <mat-icon>euro</mat-icon>\n                    <span>{{ project.dailyRate }}€/jour</span>\n                  </div>\n                  <div class=\"project-mode\" *ngIf=\"project.workMode\">\n                    <mat-icon>location_on</mat-icon>\n                    <span>{{ project.workMode | titlecase }}</span>\n                  </div>\n                </div>\n                <p class=\"project-tech\" *ngIf=\"project.techStack\">\n                  {{ project.techStack }}\n                </p>\n              </mat-card-content>\n              <mat-card-actions>\n                <button mat-button [routerLink]=\"['/projects', project.id]\">\n                  Voir détails\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n\n          <ng-template #noProjects>\n            <mat-card class=\"empty-state\">\n              <mat-card-content>\n                <div class=\"empty-content\">\n                  <mat-icon class=\"empty-icon\">work_off</mat-icon>\n                  <h3>Aucun projet pour le moment</h3>\n                  <p>Commencez par ajouter votre premier projet !</p>\n                  <button mat-raised-button color=\"primary\" routerLink=\"/projects/new\">\n                    Ajouter un projet\n                  </button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </ng-template>\n        </div>\n      </div>\n    </mat-sidenav-content>\n  </mat-sidenav-container>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;IC4B5CC,EAJF,CAAAC,cAAA,YAGsC,mBACV;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAC5C;;;;IAJDH,EAAA,CAAAI,UAAA,eAAAC,OAAA,CAAAC,KAAA,CAAyB;IAEAN,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAClBT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;;;;;IAmG7BV,EADF,CAAAC,cAAA,cAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;;;;IADEH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAW,WAAA,OAAAC,UAAA,CAAAC,QAAA,EAAkC;;;;;IAG5Cb,EAAA,CAAAC,cAAA,YAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAc,kBAAA,MAAAF,UAAA,CAAAG,SAAA,MACF;;;;;IAhBAf,EAFJ,CAAAC,cAAA,mBAAsE,sBACnD,qBACC;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACnDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC7CF,EAD6C,CAAAG,YAAA,EAAoB,EAC/C;IAIZH,EAHN,CAAAC,cAAA,uBAAkB,cACU,cACE,eACd;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACrCF,EADqC,CAAAG,YAAA,EAAO,EACtC;IACNH,EAAA,CAAAgB,UAAA,KAAAC,oDAAA,kBAAmD;IAIrDjB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,UAAA,KAAAE,kDAAA,gBAAkD;IAGpDlB,EAAA,CAAAG,YAAA,EAAmB;IAEjBH,EADF,CAAAC,cAAA,wBAAkB,kBAC4C;IAC1DD,EAAA,CAAAE,MAAA,2BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IAvBSH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,iBAAA,CAAAI,UAAA,CAAAO,IAAA,CAAkB;IACfnB,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAI,UAAA,CAAAQ,UAAA,CAAwB;IAMjCpB,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAc,kBAAA,KAAAF,UAAA,CAAAS,SAAA,gBAA6B;IAEVrB,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAQ,UAAA,CAAAC,QAAA,CAAsB;IAK1Bb,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAQ,UAAA,CAAAG,SAAA,CAAuB;IAK7Bf,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAAX,UAAA,CAAAY,EAAA,EAAwC;;;;;IAtBjExB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAgB,UAAA,IAAAS,6CAAA,wBAAsE;IA0BxEzB,EAAA,CAAAG,YAAA,EAAM;;;;IA1B+CH,EAAA,CAAAO,SAAA,EAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAsB,MAAA,CAAAC,cAAA,CAAiB;;;;;IAgC9D3B,EAHN,CAAAC,cAAA,mBAA8B,uBACV,cACW,mBACI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnDH,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,2BACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;;;ADtIvB,OAAM,MAAOyB,kBAAkB;EAoB7BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,gBAAkC;IAFlC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IArB1B,KAAAC,gBAAgB,GAAwB,IAAI;IAC5C,KAAAN,cAAc,GAAiB,EAAE;IACjC,KAAAO,KAAK,GAAG;MACNC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE;KACjB;IAED,KAAAC,SAAS,GAAG,CACV;MAAE9B,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,iBAAiB;MAAEJ,KAAK,EAAE;IAAY,CAAE,EACpE;MAAEG,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,SAAS;MAAEJ,KAAK,EAAE;IAAW,CAAE,EACtD;MAAEG,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEJ,KAAK,EAAE;IAAU,CAAE,EACzD;MAAEG,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEJ,KAAK,EAAE;IAAW,CAAE,EAC3D;MAAEG,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,SAAS;MAAEJ,KAAK,EAAE;IAAU,CAAE,EACvD;MAAEG,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEJ,KAAK,EAAE;IAAU,CAAE,CACvD;EAME;EAEHkC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACX,WAAW,CAACY,OAAO,EAAE;IAC7C,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACF,WAAW,EAAEjB,EAAE,EAAE;MACxB;MACA,IAAI,CAACQ,gBAAgB,CAACY,mBAAmB,CAAC,IAAI,CAACH,WAAW,CAACjB,EAAE,CAAC,CAACqB,SAAS,CAAC;QACvEC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAI,CAACd,gBAAgB,GAAGc,OAAO;UAC/B,IAAI,CAACC,WAAW,CAACD,OAAO,CAAC;QAC3B,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK;OAChE,CAAC;MAEF;MACA,IAAI,CAAClB,cAAc,CAACoB,gBAAgB,CAAC,IAAI,CAACV,WAAW,CAACjB,EAAE,CAAC,CAACqB,SAAS,CAAC;QAClEC,IAAI,EAAGM,QAAQ,IAAI;UACjB,IAAI,CAACzB,cAAc,GAAGyB,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACDJ,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;OACjE,CAAC;;EAEN;EAEAD,WAAWA,CAACD,OAAqB;IAC/B,IAAI,CAACb,KAAK,GAAG;MACXC,aAAa,EAAEY,OAAO,CAACZ,aAAa,IAAI,CAAC;MACzCC,gBAAgB,EAAEW,OAAO,CAACX,gBAAgB,IAAI,CAAC;MAC/CC,YAAY,EAAE,IAAI,CAACiB,qBAAqB,EAAE;MAC1ChB,cAAc,EAAE,IAAI,CAACX,cAAc,CAAC4B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,IAAI,IAAIC,IAAI,CAACF,CAAC,CAACC,SAAS,CAAC,IAAI,IAAIC,IAAI,EAAE,CAAC,CAACC;KACrG;EACH;EAEAL,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC3B,cAAc,CAACiC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAI;MACnD,IAAIA,OAAO,CAACzC,SAAS,IAAIyC,OAAO,CAACC,gBAAgB,IAAID,OAAO,CAACE,WAAW,EAAE;QACxE,MAAMC,WAAW,GAAGH,OAAO,CAACE,WAAW,GAAG,EAAE;QAC5C,OAAOH,KAAK,GAAIC,OAAO,CAACzC,SAAS,GAAG4C,WAAW,GAAGH,OAAO,CAACC,gBAAiB;;MAE7E,OAAOF,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;EACP;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAACpC,WAAW,CAACoC,MAAM,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAG,IAAIV,IAAI,EAAE,CAACW,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;;;uBAhFWxC,kBAAkB,EAAA5B,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1E,EAAA,CAAAsE,iBAAA,CAAAK,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAlBhD,kBAAkB;MAAAiD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/E,EAAA,CAAAgF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC5B3BtF,EAHJ,CAAAC,cAAA,aAAiC,qBAEwB,gBACkB;UAA/CD,EAAA,CAAAwF,UAAA,mBAAAC,oDAAA;YAAAzF,EAAA,CAAA0F,aAAA,CAAAC,GAAA;YAAA,MAAAC,UAAA,GAAA5F,EAAA,CAAA6F,WAAA;YAAA,OAAA7F,EAAA,CAAA8F,WAAA,CAASF,UAAA,CAAAG,MAAA,EAAgB;UAAA,EAAC;UAChD/F,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;UAETH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzCH,EAAA,CAAAgG,SAAA,cAA4B;UAG1BhG,EADF,CAAAC,cAAA,gBAAsE,eAC1D;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnCH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,IAAwD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvFH,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAC7B;UAILH,EAFJ,CAAAC,cAAA,yBAA8B,kBACgB,gBAChC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UACdF,EADc,CAAAG,YAAA,EAAO,EACZ;UACTH,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAwF,UAAA,mBAAAS,qDAAA;YAAAjG,EAAA,CAAA0F,aAAA,CAAAC,GAAA;YAAA,OAAA3F,EAAA,CAAA8F,WAAA,CAASP,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UACtClE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAW;UAGvBF,EAHuB,CAAAG,YAAA,EAAO,EACjB,EACA,EACC;UAKVH,EAFJ,CAAAC,cAAA,iCAAiD,0BACU,oBACzC;UACZD,EAAA,CAAAgB,UAAA,KAAAkF,gCAAA,gBAGsC;UAK1ClG,EADE,CAAAG,YAAA,EAAe,EACH;UAORH,EAJN,CAAAC,cAAA,+BAA0C,eACT,eAEA,cACD;UACxBD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA4B;UAC1BD,EAAA,CAAAE,MAAA,wDACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAQIH,EALV,CAAAC,cAAA,eAAwB,oBACM,wBACR,eACU,eACQ,gBACpB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;UAEJH,EADF,CAAAC,cAAA,eAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAI9CF,EAJ8C,CAAAG,YAAA,EAAM,EACxC,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAA4B,wBACR,eACU,eACI,gBAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;UAEJH,EADF,CAAAC,cAAA,eAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAA8C;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7EH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAIzCF,EAJyC,CAAAG,YAAA,EAAM,EACnC,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAA4B,wBACR,eACU,eACO,gBACnB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAA0C;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzEH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAI/CF,EAJ+C,CAAAG,YAAA,EAAM,EACzC,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAA4B,wBACR,eACU,eACM,gBAClB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAKhDF,EALgD,CAAAG,YAAA,EAAM,EACxC,EACF,EACW,EACV,EACP;UAKFH,EAFJ,CAAAC,cAAA,eAAqC,eACP,UACtB;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAE,MAAA,+BACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UA+BNH,EA7BA,CAAAgB,UAAA,KAAAmF,kCAAA,kBAA8E,KAAAC,0CAAA,iCAAApG,EAAA,CAAAqG,sBAAA,CA6BrD;UAkBnCrG,EAJQ,CAAAG,YAAA,EAAM,EACF,EACc,EACA,EACpB;;;;;UAhKiBH,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,sBAAAkG,WAAA,CAA8B;UAEvBtG,EAAA,CAAAO,SAAA,GAAwD;UAAxDP,EAAA,CAAAuG,kBAAA,KAAAhB,GAAA,CAAA9C,WAAA,kBAAA8C,GAAA,CAAA9C,WAAA,CAAA+D,SAAA,OAAAjB,GAAA,CAAA9C,WAAA,kBAAA8C,GAAA,CAAA9C,WAAA,CAAAgE,QAAA,KAAwD;UAqB1DzG,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAmF,GAAA,CAAAhD,SAAA,CAAY;UAe5BvC,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAuG,kBAAA,MAAAhB,GAAA,CAAApB,WAAA,UAAAoB,GAAA,CAAA9C,WAAA,kBAAA8C,GAAA,CAAA9C,WAAA,CAAA+D,SAAA,qBACF;UAeiCxG,EAAA,CAAAO,SAAA,IAAyB;UAAzBP,EAAA,CAAAQ,iBAAA,CAAA+E,GAAA,CAAArD,KAAA,CAAAC,aAAA,CAAyB;UAczBnC,EAAA,CAAAO,SAAA,IAA8C;UAA9CP,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAA0G,WAAA,SAAAnB,GAAA,CAAArD,KAAA,CAAAE,gBAAA,qBAA8C;UAc9CpC,EAAA,CAAAO,SAAA,IAA0C;UAA1CP,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAA0G,WAAA,SAAAnB,GAAA,CAAArD,KAAA,CAAAG,YAAA,qBAA0C;UAc1CrC,EAAA,CAAAO,SAAA,IAA0B;UAA1BP,EAAA,CAAAQ,iBAAA,CAAA+E,GAAA,CAAArD,KAAA,CAAAI,cAAA,CAA0B;UAiB/BtC,EAAA,CAAAO,SAAA,GAAiC;UAAAP,EAAjC,CAAAI,UAAA,SAAAmF,GAAA,CAAA5D,cAAA,CAAAgC,MAAA,KAAiC,aAAAgD,aAAA,CAAe;;;qBD1GlFpH,YAAY,EAAAqH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,aAAA,EACZxH,YAAY,EAAAyH,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA,EACZ1H,gBAAgB,EAAA2H,EAAA,CAAAC,UAAA,EAChB3H,gBAAgB,EAAA4H,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChB9H,aAAa,EAAA+H,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,gBAAA,EACblI,aAAa,EAAAmI,EAAA,CAAAC,OAAA,EACbnI,eAAe,EAAAoI,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfrI,aAAa,EAAAsI,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,cAAA,EAAAH,GAAA,CAAAI,aAAA,EAAAJ,GAAA,CAAAK,eAAA,EAAAL,GAAA,CAAAM,YAAA,EACb3I,aAAa,EAAA4I,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}