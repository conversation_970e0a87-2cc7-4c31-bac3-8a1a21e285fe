{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"firstName\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"lastName\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"confirmPassword\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_spinner_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction RegisterComponent_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9er mon compte\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9ation...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.registerForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else if (confirmPassword?.hasError('passwordMismatch')) {\n      confirmPassword.setErrors(null);\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const userData = {\n        firstName: this.registerForm.value.firstName,\n        lastName: this.registerForm.value.lastName,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password\n      };\n      this.authService.register(userData).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Compte créé avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: error => {\n          this.isLoading = false;\n          let errorMessage = 'Erreur lors de la création du compte. Veuillez réessayer.';\n          if (error.status === 409) {\n            errorMessage = 'Un compte existe déjà avec cet email.';\n          } else if (error.status === 0) {\n            errorMessage = 'Impossible de se connecter au serveur.';\n          }\n          this.snackBar.open(errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  getErrorMessage(field) {\n    const control = this.registerForm.get(field);\n    if (control?.hasError('required')) {\n      const fieldNames = {\n        firstName: 'Prénom',\n        lastName: 'Nom',\n        email: 'Email',\n        password: 'Mot de passe',\n        confirmPassword: 'Confirmation du mot de passe'\n      };\n      return `${fieldNames[field]} requis`;\n    }\n    if (control?.hasError('email')) {\n      return 'Email invalide';\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `Minimum ${minLength} caractères requis`;\n    }\n    if (control?.hasError('passwordMismatch')) {\n      return 'Les mots de passe ne correspondent pas';\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 57,\n      vars: 18,\n      consts: [[1, \"register-container\"], [1, \"register-card-container\"], [1, \"register-card\"], [1, \"register-title\"], [1, \"register-icon\"], [1, \"register-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Jean\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Dupont\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"register-button\", \"full-width\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"register-spinner\", 4, \"ngIf\"], [1, \"register-actions\"], [1, \"login-link\"], [\"routerLink\", \"/login\", 1, \"login-link-text\"], [\"diameter\", \"20\", 1, \"register-spinner\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\")(4, \"mat-card-title\", 3)(5, \"mat-icon\", 4);\n          i0.ɵɵtext(6, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Cr\\u00E9er un compte Indezy \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n          i0.ɵɵtext(9, \" Rejoignez la communaut\\u00E9 des freelances organis\\u00E9s \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 6)(13, \"mat-form-field\", 7)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 8);\n          i0.ɵɵelementStart(17, \"mat-icon\", 9);\n          i0.ɵɵtext(18, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, RegisterComponent_mat_error_19_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"mat-form-field\", 7)(21, \"mat-label\");\n          i0.ɵɵtext(22, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"input\", 11);\n          i0.ɵɵtemplate(24, RegisterComponent_mat_error_24_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 12)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 13);\n          i0.ɵɵelementStart(29, \"mat-icon\", 9);\n          i0.ɵɵtext(30, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, RegisterComponent_mat_error_31_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-form-field\", 12)(33, \"mat-label\");\n          i0.ɵɵtext(34, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 14);\n          i0.ɵɵelementStart(36, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_36_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(37, \"mat-icon\");\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(39, RegisterComponent_mat_error_39_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"mat-form-field\", 12)(41, \"mat-label\");\n          i0.ɵɵtext(42, \"Confirmer le mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"input\", 16);\n          i0.ɵɵelementStart(44, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_44_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(45, \"mat-icon\");\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(47, RegisterComponent_mat_error_47_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"button\", 17);\n          i0.ɵɵtemplate(49, RegisterComponent_mat_spinner_49_Template, 1, 0, \"mat-spinner\", 18)(50, RegisterComponent_span_50_Template, 2, 0, \"span\", 10)(51, RegisterComponent_span_51_Template, 2, 0, \"span\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"mat-card-actions\", 19)(53, \"p\", 20);\n          i0.ɵɵtext(54, \" D\\u00E9j\\u00E0 un compte ? \");\n          i0.ɵɵelementStart(55, \"a\", 21);\n          i0.ɵɵtext(56, \"Se connecter\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_8_0;\n          let tmp_13_0;\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide confirm password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatSnackBarModule, MatProgressSpinnerModule, i11.MatProgressSpinner],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.register-card-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n\\n.register-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n\\n.register-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #667eea;\\n}\\n\\n.register-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-top: 20px;\\n}\\n\\n.name-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 8px;\\n  position: relative;\\n}\\n\\n.register-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.register-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 16px;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  margin: 0;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.login-link-text[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.login-link-text[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 600px) {\\n  .register-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .register-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .name-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n  .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Validators", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatSnackBarModule", "MatProgressSpinnerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ɵɵelement", "RegisterComponent", "constructor", "fb", "authService", "router", "snackBar", "isLoading", "hidePassword", "hideConfirmPassword", "registerForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "email", "password", "confirmPassword", "validators", "passwordMatchValidator", "ngOnInit", "isAuthenticated", "navigate", "form", "get", "value", "setErrors", "passwordMismatch", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "valid", "userData", "register", "subscribe", "next", "response", "open", "duration", "panelClass", "error", "errorMessage", "status", "field", "control", "fieldNames", "errors", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_11_listener", "ɵɵtemplate", "RegisterComponent_mat_error_19_Template", "RegisterComponent_mat_error_24_Template", "RegisterComponent_mat_error_31_Template", "RegisterComponent_Template_button_click_36_listener", "RegisterComponent_mat_error_39_Template", "RegisterComponent_Template_button_click_44_listener", "RegisterComponent_mat_error_47_Template", "RegisterComponent_mat_spinner_49_Template", "RegisterComponent_span_50_Template", "RegisterComponent_span_51_Template", "ɵɵproperty", "tmp_1_0", "invalid", "touched", "tmp_2_0", "tmp_3_0", "ɵɵtextInterpolate", "tmp_8_0", "tmp_13_0", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\auth\\register\\register.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { AuthService } from '../../../services/auth.service';\n\n@Component({\n  selector: 'app-register',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss']\n})\nexport class RegisterComponent implements OnInit {\n  registerForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  hideConfirmPassword = true;\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  ngOnInit(): void {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    \n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else if (confirmPassword?.hasError('passwordMismatch')) {\n      confirmPassword.setErrors(null);\n    }\n    \n    return null;\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const userData = {\n        firstName: this.registerForm.value.firstName,\n        lastName: this.registerForm.value.lastName,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password\n      };\n\n      this.authService.register(userData).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Compte créé avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          let errorMessage = 'Erreur lors de la création du compte. Veuillez réessayer.';\n          \n          if (error.status === 409) {\n            errorMessage = 'Un compte existe déjà avec cet email.';\n          } else if (error.status === 0) {\n            errorMessage = 'Impossible de se connecter au serveur.';\n          }\n\n          this.snackBar.open(errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  getErrorMessage(field: string): string {\n    const control = this.registerForm.get(field);\n    if (control?.hasError('required')) {\n      const fieldNames: { [key: string]: string } = {\n        firstName: 'Prénom',\n        lastName: 'Nom',\n        email: 'Email',\n        password: 'Mot de passe',\n        confirmPassword: 'Confirmation du mot de passe'\n      };\n      return `${fieldNames[field]} requis`;\n    }\n    if (control?.hasError('email')) {\n      return 'Email invalide';\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `Minimum ${minLength} caractères requis`;\n    }\n    if (control?.hasError('passwordMismatch')) {\n      return 'Les mots de passe ne correspondent pas';\n    }\n    return '';\n  }\n}\n", "<div class=\"register-container\">\n  <div class=\"register-card-container\">\n    <mat-card class=\"register-card\">\n      <mat-card-header>\n        <mat-card-title class=\"register-title\">\n          <mat-icon class=\"register-icon\">person_add</mat-icon>\n          Créer un compte Indezy\n        </mat-card-title>\n        <mat-card-subtitle>\n          Rejoignez la communauté des freelances organisés\n        </mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\n          <div class=\"name-row\">\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\n              <mat-label>Prénom</mat-label>\n              <input matInput \n                     type=\"text\" \n                     formControlName=\"firstName\" \n                     placeholder=\"Jean\"\n                     autocomplete=\"given-name\">\n              <mat-icon matSuffix>person</mat-icon>\n              <mat-error *ngIf=\"registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched\">\n                {{ getErrorMessage('firstName') }}\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\n              <mat-label>Nom</mat-label>\n              <input matInput \n                     type=\"text\" \n                     formControlName=\"lastName\" \n                     placeholder=\"Dupont\"\n                     autocomplete=\"family-name\">\n              <mat-error *ngIf=\"registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched\">\n                {{ getErrorMessage('lastName') }}\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Email</mat-label>\n            <input matInput \n                   type=\"email\" \n                   formControlName=\"email\" \n                   placeholder=\"<EMAIL>\"\n                   autocomplete=\"email\">\n            <mat-icon matSuffix>email</mat-icon>\n            <mat-error *ngIf=\"registerForm.get('email')?.invalid && registerForm.get('email')?.touched\">\n              {{ getErrorMessage('email') }}\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Mot de passe</mat-label>\n            <input matInput \n                   [type]=\"hidePassword ? 'password' : 'text'\" \n                   formControlName=\"password\"\n                   autocomplete=\"new-password\">\n            <button mat-icon-button \n                    matSuffix \n                    type=\"button\"\n                    (click)=\"hidePassword = !hidePassword\"\n                    [attr.aria-label]=\"'Hide password'\"\n                    [attr.aria-pressed]=\"hidePassword\">\n              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n            </button>\n            <mat-error *ngIf=\"registerForm.get('password')?.invalid && registerForm.get('password')?.touched\">\n              {{ getErrorMessage('password') }}\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Confirmer le mot de passe</mat-label>\n            <input matInput \n                   [type]=\"hideConfirmPassword ? 'password' : 'text'\" \n                   formControlName=\"confirmPassword\"\n                   autocomplete=\"new-password\">\n            <button mat-icon-button \n                    matSuffix \n                    type=\"button\"\n                    (click)=\"hideConfirmPassword = !hideConfirmPassword\"\n                    [attr.aria-label]=\"'Hide confirm password'\"\n                    [attr.aria-pressed]=\"hideConfirmPassword\">\n              <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n            </button>\n            <mat-error *ngIf=\"registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched\">\n              {{ getErrorMessage('confirmPassword') }}\n            </mat-error>\n          </mat-form-field>\n\n          <button mat-raised-button \n                  color=\"primary\" \n                  type=\"submit\" \n                  class=\"register-button full-width\"\n                  [disabled]=\"registerForm.invalid || isLoading\">\n            <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"register-spinner\"></mat-spinner>\n            <span *ngIf=\"!isLoading\">Créer mon compte</span>\n            <span *ngIf=\"isLoading\">Création...</span>\n          </button>\n        </form>\n      </mat-card-content>\n\n      <mat-card-actions class=\"register-actions\">\n        <p class=\"login-link\">\n          Déjà un compte ? \n          <a routerLink=\"/login\" class=\"login-link-text\">Se connecter</a>\n        </p>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACxF,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAqB,6BAA6B;AAC5E,SAASC,wBAAwB,QAAQ,oCAAoC;;;;;;;;;;;;;;;ICc/DC,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,mBACF;;;;;IAUAP,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,kBACF;;;;;IAYFP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,eACF;;;;;IAiBAP,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,kBACF;;;;;IAiBAP,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,yBACF;;;;;IAQAP,EAAA,CAAAQ,SAAA,sBAAoF;;;;;IACpFR,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,4BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADrEtD,OAAM,MAAOM,iBAAiB;EAM5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAQxB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAChCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACkC,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DI,eAAe,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAAC+B,QAAQ,CAAC;KAC5C,EAAE;MAAEM,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACjB,WAAW,CAACkB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEAH,sBAAsBA,CAACI,IAAe;IACpC,MAAMP,QAAQ,GAAGO,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMP,eAAe,GAAGM,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIR,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAACS,KAAK,KAAKR,eAAe,CAACQ,KAAK,EAAE;MAC3ER,eAAe,CAACS,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;KACtD,MAAM,IAAIV,eAAe,EAAEW,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MACxDX,eAAe,CAACS,SAAS,CAAC,IAAI,CAAC;;IAGjC,OAAO,IAAI;EACb;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpB,YAAY,CAACqB,KAAK,EAAE;MAC3B,IAAI,CAACxB,SAAS,GAAG,IAAI;MACrB,MAAMyB,QAAQ,GAAG;QACfpB,SAAS,EAAE,IAAI,CAACF,YAAY,CAACgB,KAAK,CAACd,SAAS;QAC5CG,QAAQ,EAAE,IAAI,CAACL,YAAY,CAACgB,KAAK,CAACX,QAAQ;QAC1CC,KAAK,EAAE,IAAI,CAACN,YAAY,CAACgB,KAAK,CAACV,KAAK;QACpCC,QAAQ,EAAE,IAAI,CAACP,YAAY,CAACgB,KAAK,CAACT;OACnC;MAED,IAAI,CAACb,WAAW,CAAC6B,QAAQ,CAACD,QAAQ,CAAC,CAACE,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC7B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,QAAQ,CAAC+B,IAAI,CAAC,0BAA0B,EAAE,QAAQ,EAAE;YACvDC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAAClC,MAAM,CAACkB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDiB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACjC,SAAS,GAAG,KAAK;UACtB,IAAIkC,YAAY,GAAG,2DAA2D;UAE9E,IAAID,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;YACxBD,YAAY,GAAG,uCAAuC;WACvD,MAAM,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;YAC7BD,YAAY,GAAG,wCAAwC;;UAGzD,IAAI,CAACnC,QAAQ,CAAC+B,IAAI,CAACI,YAAY,EAAE,QAAQ,EAAE;YACzCH,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAxC,eAAeA,CAAC4C,KAAa;IAC3B,MAAMC,OAAO,GAAG,IAAI,CAAClC,YAAY,CAACe,GAAG,CAACkB,KAAK,CAAC;IAC5C,IAAIC,OAAO,EAAEf,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,MAAMgB,UAAU,GAA8B;QAC5CjC,SAAS,EAAE,QAAQ;QACnBG,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,cAAc;QACxBC,eAAe,EAAE;OAClB;MACD,OAAO,GAAG2B,UAAU,CAACF,KAAK,CAAC,SAAS;;IAEtC,IAAIC,OAAO,EAAEf,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,gBAAgB;;IAEzB,IAAIe,OAAO,EAAEf,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMf,SAAS,GAAG8B,OAAO,CAACE,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,WAAWjC,SAAS,oBAAoB;;IAEjD,IAAI8B,OAAO,EAAEf,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MACzC,OAAO,wCAAwC;;IAEjD,OAAO,EAAE;EACX;;;uBAtGW5B,iBAAiB,EAAAT,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1D,EAAA,CAAAwD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5D,EAAA,CAAAwD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9D,EAAA,CAAAwD,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBvD,iBAAiB;MAAAwD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnE,EAAA,CAAAoE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BpB1E,EALV,CAAAC,cAAA,aAAgC,aACO,kBACH,sBACb,wBACwB,kBACL;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,wBAAmB;UACjBD,EAAA,CAAAE,MAAA,mEACF;UACFF,EADE,CAAAG,YAAA,EAAoB,EACJ;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,eAC+D;UAA9CD,EAAA,CAAA4E,UAAA,sBAAAC,qDAAA;YAAA,OAAYF,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAGlDtC,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAQ,SAAA,gBAIiC;UACjCR,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA8E,UAAA,KAAAC,uCAAA,wBAAoG;UAGtG/E,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC1BH,EAAA,CAAAQ,SAAA,iBAIkC;UAClCR,EAAA,CAAA8E,UAAA,KAAAE,uCAAA,wBAAkG;UAItGhF,EADE,CAAAG,YAAA,EAAiB,EACb;UAGJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAQ,SAAA,iBAI4B;UAC5BR,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA8E,UAAA,KAAAG,uCAAA,wBAA4F;UAG9FjF,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAQ,SAAA,iBAGmC;UACnCR,EAAA,CAAAC,cAAA,kBAK2C;UAFnCD,EAAA,CAAA4E,UAAA,mBAAAM,oDAAA;YAAA,OAAAP,GAAA,CAAA3D,YAAA,IAAA2D,GAAA,CAAA3D,YAAA;UAAA,EAAsC;UAG5ChB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAA8E,UAAA,KAAAK,uCAAA,wBAAkG;UAGpGnF,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChDH,EAAA,CAAAQ,SAAA,iBAGmC;UACnCR,EAAA,CAAAC,cAAA,kBAKkD;UAF1CD,EAAA,CAAA4E,UAAA,mBAAAQ,oDAAA;YAAA,OAAAT,GAAA,CAAA1D,mBAAA,IAAA0D,GAAA,CAAA1D,mBAAA;UAAA,EAAoD;UAG1DjB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAyD;UACrEF,EADqE,CAAAG,YAAA,EAAW,EACvE;UACTH,EAAA,CAAA8E,UAAA,KAAAO,uCAAA,wBAAgH;UAGlHrF,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,kBAIuD;UAGrDD,EAFA,CAAA8E,UAAA,KAAAQ,yCAAA,0BAAsE,KAAAC,kCAAA,mBAC7C,KAAAC,kCAAA,mBACD;UAG9BxF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAA2C,aACnB;UACpBD,EAAA,CAAAE,MAAA,oCACA;UAAAF,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAKrEF,EALqE,CAAAG,YAAA,EAAI,EAC7D,EACa,EACV,EACP,EACF;;;;;;;;UAnGQH,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAyF,UAAA,cAAAd,GAAA,CAAAzD,YAAA,CAA0B;UAUdlB,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAyF,UAAA,WAAAC,OAAA,GAAAf,GAAA,CAAAzD,YAAA,CAAAe,GAAA,gCAAAyD,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAf,GAAA,CAAAzD,YAAA,CAAAe,GAAA,gCAAAyD,OAAA,CAAAE,OAAA,EAAsF;UAYtF5F,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAyF,UAAA,WAAAI,OAAA,GAAAlB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,+BAAA4D,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAlB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,+BAAA4D,OAAA,CAAAD,OAAA,EAAoF;UActF5F,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAyF,UAAA,WAAAK,OAAA,GAAAnB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,4BAAA6D,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAnB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,4BAAA6D,OAAA,CAAAF,OAAA,EAA8E;UAQnF5F,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAyF,UAAA,SAAAd,GAAA,CAAA3D,YAAA,uBAA2C;UAO1ChB,EAAA,CAAAI,SAAA,EAAmC;;UAE/BJ,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAA+F,iBAAA,CAAApB,GAAA,CAAA3D,YAAA,mCAAkD;UAElDhB,EAAA,CAAAI,SAAA,EAAoF;UAApFJ,EAAA,CAAAyF,UAAA,WAAAO,OAAA,GAAArB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,+BAAA+D,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAArB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,+BAAA+D,OAAA,CAAAJ,OAAA,EAAoF;UAQzF5F,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAyF,UAAA,SAAAd,GAAA,CAAA1D,mBAAA,uBAAkD;UAOjDjB,EAAA,CAAAI,SAAA,EAA2C;;UAEvCJ,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAA+F,iBAAA,CAAApB,GAAA,CAAA1D,mBAAA,mCAAyD;UAEzDjB,EAAA,CAAAI,SAAA,EAAkG;UAAlGJ,EAAA,CAAAyF,UAAA,WAAAQ,QAAA,GAAAtB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,sCAAAgE,QAAA,CAAAN,OAAA,OAAAM,QAAA,GAAAtB,GAAA,CAAAzD,YAAA,CAAAe,GAAA,sCAAAgE,QAAA,CAAAL,OAAA,EAAkG;UASxG5F,EAAA,CAAAI,SAAA,EAA8C;UAA9CJ,EAAA,CAAAyF,UAAA,aAAAd,GAAA,CAAAzD,YAAA,CAAAyE,OAAA,IAAAhB,GAAA,CAAA5D,SAAA,CAA8C;UACtCf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAyF,UAAA,SAAAd,GAAA,CAAA5D,SAAA,CAAe;UACtBf,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAyF,UAAA,UAAAd,GAAA,CAAA5D,SAAA,CAAgB;UAChBf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAyF,UAAA,SAAAd,GAAA,CAAA5D,SAAA,CAAe;;;qBDnF9B1B,YAAY,EAAA6G,EAAA,CAAAC,IAAA,EACZ5G,mBAAmB,EAAAkE,EAAA,CAAA2C,aAAA,EAAA3C,EAAA,CAAA4C,oBAAA,EAAA5C,EAAA,CAAA6C,eAAA,EAAA7C,EAAA,CAAA8C,oBAAA,EAAA9C,EAAA,CAAA+C,kBAAA,EAAA/C,EAAA,CAAAgD,eAAA,EACnBjH,YAAY,EAAAqE,EAAA,CAAA6C,UAAA,EACZjH,aAAa,EAAAkH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbvH,kBAAkB,EAAAwH,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB3H,cAAc,EAAA4H,EAAA,CAAAC,QAAA,EACd5H,eAAe,EAAA6H,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf9H,aAAa,EAAA+H,GAAA,CAAAC,OAAA,EACb/H,iBAAiB,EACjBC,wBAAwB,EAAA+H,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}