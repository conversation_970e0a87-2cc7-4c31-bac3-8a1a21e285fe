{"ast": null, "code": "export const clientRoutes = [{\n  path: '',\n  loadComponent: () => import('./client-list/client-list.component').then(m => m.ClientListComponent)\n}, {\n  path: 'create',\n  loadComponent: () => import('./client-form/client-form.component').then(m => m.ClientFormComponent)\n}, {\n  path: ':id',\n  loadComponent: () => import('./client-detail/client-detail.component').then(m => m.ClientDetailComponent)\n}, {\n  path: ':id/edit',\n  loadComponent: () => import('./client-form/client-form.component').then(m => m.ClientFormComponent)\n}];", "map": {"version": 3, "names": ["clientRoutes", "path", "loadComponent", "then", "m", "ClientListComponent", "ClientFormComponent", "ClientDetailComponent"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\clients.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const clientRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./client-list/client-list.component').then(m => m.ClientListComponent)\n  },\n  {\n    path: 'create',\n    loadComponent: () => import('./client-form/client-form.component').then(m => m.ClientFormComponent)\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./client-detail/client-detail.component').then(m => m.ClientDetailComponent)\n  },\n  {\n    path: ':id/edit',\n    loadComponent: () => import('./client-form/client-form.component').then(m => m.ClientFormComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,YAAY,GAAW,CAClC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,mBAAmB;CACnG,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,mBAAmB;CACnG,EACD;EACEL,IAAI,EAAE,KAAK;EACXC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,qBAAqB;CACzG,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,mBAAmB;CACnG,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}