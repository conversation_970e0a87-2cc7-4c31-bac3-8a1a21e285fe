{"ast": null, "code": "export const routes = [{\n  path: '',\n  redirectTo: '/login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'profile',\n  loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'projects',\n  loadComponent: () => import('./components/projects/project-list/project-list.component').then(m => m.ProjectListComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'clients',\n  loadChildren: () => import('./components/clients/clients.routes').then(m => m.clientRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'contacts',\n  loadChildren: () => import('./components/contacts/contacts.routes').then(m => m.contactRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'sources',\n  loadChildren: () => import('./components/sources/sources.routes').then(m => m.sourceRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];", "map": {"version": 3, "names": ["routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "LoginComponent", "RegisterComponent", "DashboardComponent", "canActivate", "<PERSON><PERSON><PERSON><PERSON>", "ProfileComponent", "ProjectListComponent", "loadChildren", "clientRoutes", "contactRoutes", "sourceRoutes"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { authGuard } from './guards/auth.guard';\n\nexport const routes: Routes = [\n  { path: '', redirectTo: '/login', pathMatch: 'full' },\n  {\n    path: 'login',\n    loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n  },\n  {\n    path: 'register',\n    loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n  },\n  {\n    path: 'dashboard',\n    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'profile',\n    loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'projects',\n    loadComponent: () => import('./components/projects/project-list/project-list.component').then(m => m.ProjectListComponent),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'clients',\n    loadChildren: () => import('./components/clients/clients.routes').then(m => m.clientRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'contacts',\n    loadChildren: () => import('./components/contacts/contacts.routes').then(m => m.contactRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'sources',\n    loadChildren: () => import('./components/sources/sources.routes').then(m => m.sourceRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: '**',\n    redirectTo: '/login'\n  }\n];\n"], "mappings": "AAGA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE;AAAM,CAAE,EACrD;EACEF,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;CAClG,EACD;EACEN,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CAC3G,EACD;EACEP,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,kBAAkB,CAAC;EACzGC,WAAW,EAAE,CAACC,SAAS;CACxB,EACD;EACEV,IAAI,EAAE,SAAS;EACfG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,gBAAgB,CAAC;EACnGF,WAAW,EAAE,CAACC,SAAS;CACxB,EACD;EACEV,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2DAA2D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,oBAAoB,CAAC;EAC1HH,WAAW,EAAE,CAACC,SAAS;CACxB,EACD;EACEV,IAAI,EAAE,SAAS;EACfa,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACT,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,YAAY,CAAC;EAC3FL,WAAW,EAAE,CAACC,SAAS;CACxB,EACD;EACEV,IAAI,EAAE,UAAU;EAChBa,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACT,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,aAAa,CAAC;EAC9FN,WAAW,EAAE,CAACC,SAAS;CACxB,EACD;EACEV,IAAI,EAAE,SAAS;EACfa,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACT,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,YAAY,CAAC;EAC3FP,WAAW,EAAE,CAACC,SAAS;CACxB,EACD;EACEV,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}