{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nexport const authGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  if (authService.isAuthenticated()) {\n    return true;\n  } else {\n    router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}