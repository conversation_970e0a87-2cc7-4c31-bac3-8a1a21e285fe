{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction LoginComponent_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction LoginComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = this.loginForm.value;\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Connexion réussie!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: error => {\n          this.isLoading = false;\n          let errorMessage = 'Erreur de connexion. Veuillez réessayer.';\n          if (error.status === 401) {\n            errorMessage = 'Email ou mot de passe incorrect.';\n          } else if (error.status === 0) {\n            errorMessage = 'Impossible de se connecter au serveur.';\n          }\n          this.snackBar.open(errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  getErrorMessage(field) {\n    const control = this.loginForm.get(field);\n    if (control?.hasError('required')) {\n      return `${field === 'email' ? 'Email' : 'Mot de passe'} requis`;\n    }\n    if (control?.hasError('email')) {\n      return 'Email invalide';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Le mot de passe doit contenir au moins 6 caractères';\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 11,\n      consts: [[1, \"login-container\"], [1, \"login-card-container\"], [1, \"login-card\"], [1, \"login-title\"], [1, \"login-icon\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", \"full-width\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"login-spinner\", 4, \"ngIf\"], [1, \"login-actions\"], [1, \"signup-link\"], [\"routerLink\", \"/register\", 1, \"signup-link-text\"], [\"diameter\", \"20\", 1, \"login-spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\")(4, \"mat-card-title\", 3)(5, \"mat-icon\", 4);\n          i0.ɵɵtext(6, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Connexion \\u00E0 Indezy \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n          i0.ɵɵtext(9, \" G\\u00E9rez vos missions freelance en toute simplicit\\u00E9 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"mat-form-field\", 6)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 7);\n          i0.ɵɵelementStart(16, \"mat-icon\", 8);\n          i0.ɵɵtext(17, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, LoginComponent_mat_error_18_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 6)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 10);\n          i0.ɵɵelementStart(23, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_23_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(26, LoginComponent_mat_error_26_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 12);\n          i0.ɵɵtemplate(28, LoginComponent_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 13)(29, LoginComponent_span_29_Template, 2, 0, \"span\", 9)(30, LoginComponent_span_30_Template, 2, 0, \"span\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"mat-card-actions\", 14)(32, \"p\", 15);\n          i0.ɵɵtext(33, \" Pas encore de compte ? \");\n          i0.ɵɵelementStart(34, \"a\", 16);\n          i0.ɵɵtext(35, \"Cr\\u00E9er un compte\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatSnackBarModule, MatProgressSpinnerModule, i11.MatProgressSpinner],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.login-card-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n\\n.login-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n\\n.login-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #667eea;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-top: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 8px;\\n  position: relative;\\n}\\n\\n.login-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.login-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 16px;\\n}\\n\\n.signup-link[_ngcontent-%COMP%] {\\n  margin: 0;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.signup-link-text[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.signup-link-text[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Validators", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatSnackBarModule", "MatProgressSpinnerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ɵɵelement", "LoginComponent", "constructor", "fb", "authService", "router", "snackBar", "isLoading", "hidePassword", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "isAuthenticated", "navigate", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "panelClass", "error", "errorMessage", "status", "field", "control", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_11_listener", "ɵɵtemplate", "LoginComponent_mat_error_18_Template", "LoginComponent_Template_button_click_23_listener", "LoginComponent_mat_error_26_Template", "LoginComponent_mat_spinner_28_Template", "LoginComponent_span_29_Template", "LoginComponent_span_30_Template", "ɵɵproperty", "tmp_1_0", "invalid", "touched", "ɵɵtextInterpolate", "tmp_6_0", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\auth\\login\\login.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { AuthService } from '../../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = this.loginForm.value;\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Connexion réussie!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          let errorMessage = 'Erreur de connexion. Veuillez réessayer.';\n          \n          if (error.status === 401) {\n            errorMessage = 'Email ou mot de passe incorrect.';\n          } else if (error.status === 0) {\n            errorMessage = 'Impossible de se connecter au serveur.';\n          }\n\n          this.snackBar.open(errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  getErrorMessage(field: string): string {\n    const control = this.loginForm.get(field);\n    if (control?.hasError('required')) {\n      return `${field === 'email' ? 'Email' : 'Mot de passe'} requis`;\n    }\n    if (control?.hasError('email')) {\n      return 'Email invalide';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Le mot de passe doit contenir au moins 6 caractères';\n    }\n    return '';\n  }\n}\n", "<div class=\"login-container\">\n  <div class=\"login-card-container\">\n    <mat-card class=\"login-card\">\n      <mat-card-header>\n        <mat-card-title class=\"login-title\">\n          <mat-icon class=\"login-icon\">work</mat-icon>\n          Connexion à Indezy\n        </mat-card-title>\n        <mat-card-subtitle>\n          <PERSON><PERSON><PERSON> vos missions freelance en toute simplicité\n        </mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Email</mat-label>\n            <input matInput \n                   type=\"email\" \n                   formControlName=\"email\" \n                   placeholder=\"<EMAIL>\"\n                   autocomplete=\"email\">\n            <mat-icon matSuffix>email</mat-icon>\n            <mat-error *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\n              {{ getErrorMessage('email') }}\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Mot de passe</mat-label>\n            <input matInput \n                   [type]=\"hidePassword ? 'password' : 'text'\" \n                   formControlName=\"password\"\n                   autocomplete=\"current-password\">\n            <button mat-icon-button \n                    matSuffix \n                    type=\"button\"\n                    (click)=\"hidePassword = !hidePassword\"\n                    [attr.aria-label]=\"'Hide password'\"\n                    [attr.aria-pressed]=\"hidePassword\">\n              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n            </button>\n            <mat-error *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\n              {{ getErrorMessage('password') }}\n            </mat-error>\n          </mat-form-field>\n\n          <button mat-raised-button \n                  color=\"primary\" \n                  type=\"submit\" \n                  class=\"login-button full-width\"\n                  [disabled]=\"loginForm.invalid || isLoading\">\n            <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"login-spinner\"></mat-spinner>\n            <span *ngIf=\"!isLoading\">Se connecter</span>\n            <span *ngIf=\"isLoading\">Connexion...</span>\n          </button>\n        </form>\n      </mat-card-content>\n\n      <mat-card-actions class=\"login-actions\">\n        <p class=\"signup-link\">\n          Pas encore de compte ? \n          <a routerLink=\"/register\" class=\"signup-link-text\">Créer un compte</a>\n        </p>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACxF,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAqB,6BAA6B;AAC5E,SAASC,wBAAwB,QAAQ,oCAAoC;;;;;;;;;;;;;;;ICajEC,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,eACF;;;;;IAiBAP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,kBACF;;;;;IAQAP,EAAA,CAAAQ,SAAA,sBAAiF;;;;;IACjFR,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADvBvD,OAAM,MAAOM,cAAc;EAKzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IAQjB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ,EAAE9B,UAAU,CAAC6B,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAAC8B,QAAQ,EAAE9B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACX,WAAW,CAACY,eAAe,EAAE,EAAE;MACtC,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MACrB,MAAMa,WAAW,GAAG,IAAI,CAACX,SAAS,CAACY,KAAK;MAExC,IAAI,CAACjB,WAAW,CAACkB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,QAAQ,CAACoB,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YACjDC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAACvB,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAIuB,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;YACxBD,YAAY,GAAG,kCAAkC;WAClD,MAAM,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;YAC7BD,YAAY,GAAG,wCAAwC;;UAGzD,IAAI,CAACxB,QAAQ,CAACoB,IAAI,CAACI,YAAY,EAAE,QAAQ,EAAE;YACzCH,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEA7B,eAAeA,CAACiC,KAAa;IAC3B,MAAMC,OAAO,GAAG,IAAI,CAACxB,SAAS,CAACyB,GAAG,CAACF,KAAK,CAAC;IACzC,IAAIC,OAAO,EAAEE,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAGH,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc,SAAS;;IAEjE,IAAIC,OAAO,EAAEE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,gBAAgB;;IAEzB,IAAIF,OAAO,EAAEE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,qDAAqD;;IAE9D,OAAO,EAAE;EACX;;;uBArEWlC,cAAc,EAAAT,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhD,EAAA,CAAA4C,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAlD,EAAA,CAAA4C,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd3C,cAAc;MAAA4C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvD,EAAA,CAAAwD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BjB9D,EALV,CAAAC,cAAA,aAA6B,aACO,kBACH,sBACV,wBACqB,kBACL;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5CH,EAAA,CAAAE,MAAA,gCACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,wBAAmB;UACjBD,EAAA,CAAAE,MAAA,mEACF;UACFF,EADE,CAAAG,YAAA,EAAoB,EACJ;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,eACyD;UAA3CD,EAAA,CAAAgE,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAEjD1B,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAQ,SAAA,gBAI4B;UAC5BR,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAkE,UAAA,KAAAC,oCAAA,uBAAsF;UAGxFnE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAQ,SAAA,iBAGuC;UACvCR,EAAA,CAAAC,cAAA,kBAK2C;UAFnCD,EAAA,CAAAgE,UAAA,mBAAAI,iDAAA;YAAA,OAAAL,GAAA,CAAA/C,YAAA,IAAA+C,GAAA,CAAA/C,YAAA;UAAA,EAAsC;UAG5ChB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAAkE,UAAA,KAAAG,oCAAA,uBAA4F;UAG9FrE,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,kBAIoD;UAGlDD,EAFA,CAAAkE,UAAA,KAAAI,sCAAA,0BAAmE,KAAAC,+BAAA,kBAC1C,KAAAC,+BAAA,kBACD;UAG9BxE,EAFI,CAAAG,YAAA,EAAS,EACJ,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAAwC,aACf;UACrBD,EAAA,CAAAE,MAAA,gCACA;UAAAF,EAAA,CAAAC,cAAA,aAAmD;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAK5EF,EAL4E,CAAAG,YAAA,EAAI,EACpE,EACa,EACV,EACP,EACF;;;;;UArDQH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAAyE,UAAA,cAAAV,GAAA,CAAA9C,SAAA,CAAuB;UASbjB,EAAA,CAAAI,SAAA,GAAwE;UAAxEJ,EAAA,CAAAyE,UAAA,WAAAC,OAAA,GAAAX,GAAA,CAAA9C,SAAA,CAAAyB,GAAA,4BAAAgC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAX,GAAA,CAAA9C,SAAA,CAAAyB,GAAA,4BAAAgC,OAAA,CAAAE,OAAA,EAAwE;UAQ7E5E,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAyE,UAAA,SAAAV,GAAA,CAAA/C,YAAA,uBAA2C;UAO1ChB,EAAA,CAAAI,SAAA,EAAmC;;UAE/BJ,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAA6E,iBAAA,CAAAd,GAAA,CAAA/C,YAAA,mCAAkD;UAElDhB,EAAA,CAAAI,SAAA,EAA8E;UAA9EJ,EAAA,CAAAyE,UAAA,WAAAK,OAAA,GAAAf,GAAA,CAAA9C,SAAA,CAAAyB,GAAA,+BAAAoC,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAf,GAAA,CAAA9C,SAAA,CAAAyB,GAAA,+BAAAoC,OAAA,CAAAF,OAAA,EAA8E;UASpF5E,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAyE,UAAA,aAAAV,GAAA,CAAA9C,SAAA,CAAA0D,OAAA,IAAAZ,GAAA,CAAAhD,SAAA,CAA2C;UACnCf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAyE,UAAA,SAAAV,GAAA,CAAAhD,SAAA,CAAe;UACtBf,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAyE,UAAA,UAAAV,GAAA,CAAAhD,SAAA,CAAgB;UAChBf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAyE,UAAA,SAAAV,GAAA,CAAAhD,SAAA,CAAe;;;qBDrC9B1B,YAAY,EAAA0F,EAAA,CAAAC,IAAA,EACZzF,mBAAmB,EAAAsD,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnB9F,YAAY,EAAAyD,EAAA,CAAAsC,UAAA,EACZ9F,aAAa,EAAA+F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbpG,kBAAkB,EAAAqG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBxG,cAAc,EAAAyG,EAAA,CAAAC,QAAA,EACdzG,eAAe,EAAA0G,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3G,aAAa,EAAA4G,GAAA,CAAAC,OAAA,EACb5G,iBAAiB,EACjBC,wBAAwB,EAAA4G,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}