.client-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  
  .header-card {
    margin-bottom: 20px;
    
    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1.5rem;
        
        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
    
    .actions-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      gap: 20px;
      margin-top: 16px;
      
      .search-filters {
        display: flex;
        gap: 16px;
        flex: 1;
        
        .search-field {
          flex: 1;
          max-width: 400px;
        }
        
        .status-filter {
          min-width: 150px;
        }
      }
      
      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        
        .search-filters {
          flex-direction: column;
          
          .search-field,
          .status-filter {
            max-width: none;
          }
        }
      }
    }
  }
  
  .table-card {
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      
      mat-spinner {
        margin-bottom: 16px;
      }
    }
    
    .no-data {
      text-align: center;
      padding: 40px;
      
      mat-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        color: rgba(0, 0, 0, 0.3);
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 0 0 8px 0;
        color: rgba(0, 0, 0, 0.6);
      }
      
      p {
        margin: 0 0 24px 0;
        color: rgba(0, 0, 0, 0.5);
      }
    }
    
    .table-container {
      overflow-x: auto;
      
      .clients-table {
        width: 100%;
        
        .client-name {
          display: flex;
          align-items: center;
          gap: 8px;
          
          strong {
            font-weight: 500;
          }
          
          .website-link {
            color: rgba(0, 0, 0, 0.5);
            text-decoration: none;
            
            mat-icon {
              font-size: 1rem;
              width: 1rem;
              height: 1rem;
            }
            
            &:hover {
              color: var(--mdc-theme-primary, #1976d2);
            }
          }
        }
        
        .email-link,
        .phone-link {
          color: var(--mdc-theme-primary, #1976d2);
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 4px;
          
          button {
            min-width: 40px;
            width: 40px;
            height: 40px;
            
            mat-icon {
              font-size: 1.2rem;
              width: 1.2rem;
              height: 1.2rem;
            }
          }
        }
        
        // Responsive table
        @media (max-width: 768px) {
          .mat-column-phone,
          .mat-column-industry {
            display: none;
          }
        }
        
        @media (max-width: 600px) {
          .mat-column-contactPerson {
            display: none;
          }
        }
      }
    }
  }
}

// Material Design overrides
.mat-mdc-table {
  .mat-mdc-header-cell {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.87);
  }
  
  .mat-mdc-cell {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }
  
  .mat-mdc-row:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.mat-mdc-chip {
  font-size: 0.75rem;
  min-height: 24px;
  
  &.mat-primary {
    background-color: #e8f5e8;
    color: #2e7d32;
  }
  
  &.mat-accent {
    background-color: #fff3e0;
    color: #f57c00;
  }
  
  &.mat-warn {
    background-color: #ffebee;
    color: #d32f2f;
  }
}
