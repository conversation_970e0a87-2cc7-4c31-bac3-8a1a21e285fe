{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/freelance.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nconst _c0 = a0 => [\"/projects\", a0];\nfunction DashboardComponent_div_59_mat_card_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, project_r1.workMode));\n  }\n}\nfunction DashboardComponent_div_59_mat_card_1_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r1.techStack, \" \");\n  }\n}\nfunction DashboardComponent_div_59_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 21)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 22)(8, \"div\", 23)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_59_mat_card_1_div_13_Template, 6, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, DashboardComponent_div_59_mat_card_1_p_14_Template, 2, 1, \"p\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-card-actions\")(16, \"button\", 26);\n    i0.ɵɵtext(17, \" Voir d\\u00E9tails \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r1.clientName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", project_r1.dailyRate, \"\\u20AC/jour\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r1.workMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", project_r1.techStack);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(6, _c0, project_r1.id));\n  }\n}\nfunction DashboardComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, DashboardComponent_div_59_mat_card_1_Template, 18, 8, \"mat-card\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentProjects);\n  }\n}\nfunction DashboardComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 29)(1, \"mat-card-content\")(2, \"div\", 30)(3, \"mat-icon\", 31);\n    i0.ɵɵtext(4, \"work_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Aucun projet pour le moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Commencez par ajouter votre premier projet !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵtext(10, \" Ajouter un projet \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, projectService, freelanceService) {\n    this.authService = authService;\n    this.projectService = projectService;\n    this.freelanceService = freelanceService;\n    this.freelanceProfile = null;\n    this.recentProjects = [];\n    this.stats = {\n      totalProjects: 0,\n      averageDailyRate: 0,\n      totalRevenue: 0,\n      activeProjects: 0\n    };\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getUser();\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    if (this.currentUser?.id) {\n      // Load freelance profile\n      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n        next: profile => {\n          this.freelanceProfile = profile;\n          this.updateStats(profile);\n        },\n        error: error => console.error('Error loading profile:', error)\n      });\n      // Load recent projects\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: projects => {\n          this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n        },\n        error: error => console.error('Error loading projects:', error)\n      });\n    }\n  }\n  updateStats(profile) {\n    this.stats = {\n      totalProjects: profile.totalProjects || 0,\n      averageDailyRate: profile.averageDailyRate || 0,\n      totalRevenue: this.calculateTotalRevenue(),\n      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n    };\n  }\n  calculateTotalRevenue() {\n    return this.recentProjects.reduce((total, project) => {\n      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n        const monthlyDays = project.daysPerYear / 12;\n        return total + project.dailyRate * monthlyDays * project.durationInMonths;\n      }\n      return total;\n    }, 0);\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.FreelanceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 62,\n      vars: 14,\n      consts: [[\"noProjects\", \"\"], [1, \"dashboard-container\"], [1, \"welcome-section\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-icon\", \"projects\"], [1, \"stat-info\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-icon\", \"rate\"], [1, \"stat-icon\", \"revenue\"], [1, \"stat-icon\", \"active\"], [1, \"recent-projects-section\"], [1, \"section-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects\"], [\"class\", \"projects-grid\", 4, \"ngIf\", \"ngIfElse\"], [1, \"projects-grid\"], [\"class\", \"project-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"project-card\"], [1, \"project-info\"], [1, \"project-rate\"], [\"class\", \"project-mode\", 4, \"ngIf\"], [\"class\", \"project-tech\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"routerLink\"], [1, \"project-mode\"], [1, \"project-tech\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/projects/new\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\", 3);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 4);\n          i0.ɵɵtext(5, \" Voici un aper\\u00E7u de vos missions freelance \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"mat-card\", 6)(8, \"mat-card-content\")(9, \"div\", 7)(10, \"div\", 8)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"work\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11);\n          i0.ɵɵtext(17, \"Projets totaux\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(18, \"mat-card\", 6)(19, \"mat-card-content\")(20, \"div\", 7)(21, \"div\", 12)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"euro\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 9)(25, \"div\", 10);\n          i0.ɵɵtext(26);\n          i0.ɵɵpipe(27, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 11);\n          i0.ɵɵtext(29, \"TJM moyen\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(30, \"mat-card\", 6)(31, \"mat-card-content\")(32, \"div\", 7)(33, \"div\", 13)(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"trending_up\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10);\n          i0.ɵɵtext(38);\n          i0.ɵɵpipe(39, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 11);\n          i0.ɵɵtext(41, \"Revenus estim\\u00E9s\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"mat-card\", 6)(43, \"mat-card-content\")(44, \"div\", 7)(45, \"div\", 14)(46, \"mat-icon\");\n          i0.ɵɵtext(47, \"play_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 11);\n          i0.ɵɵtext(52, \"Projets actifs\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(53, \"div\", 15)(54, \"div\", 16)(55, \"h2\");\n          i0.ɵɵtext(56, \"Projets r\\u00E9cents\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"button\", 17);\n          i0.ɵɵtext(58, \" Voir tous les projets \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(59, DashboardComponent_div_59_Template, 2, 1, \"div\", 18)(60, DashboardComponent_ng_template_60_Template, 11, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const noProjects_r3 = i0.ɵɵreference(61);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\" \", ctx.getGreeting(), \", \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" ! \\uD83D\\uDC4B \");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.totalProjects);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(27, 8, ctx.stats.averageDailyRate, \"1.0-0\"), \"\\u20AC\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(39, 11, ctx.stats.totalRevenue, \"1.0-0\"), \"\\u20AC\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.activeProjects);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentProjects.length > 0)(\"ngIfElse\", noProjects_r3);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, i4.TitleCasePipe, RouterModule, i5.RouterLink, MatIconModule, i6.MatIcon, MatButtonModule, i7.MatButton, MatCardModule, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.dashboard-toolbar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n}\\n\\n.menu-button[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n\\n.toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.user-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n}\\n\\n.sidenav-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.sidenav[_ngcontent-%COMP%] {\\n  width: 250px;\\n  border-right: 1px solid #e0e0e0;\\n}\\n\\n.active-nav-item[_ngcontent-%COMP%] {\\n  background-color: rgba(63, 81, 181, 0.1);\\n  color: #3f51b5;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.welcome-title[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.welcome-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 32px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.stat-icon.projects[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n.stat-icon.rate[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n.stat-icon.revenue[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n}\\n.stat-icon.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\\n}\\n\\n.stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  line-height: 1;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin-top: 4px;\\n}\\n\\n.recent-projects-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n  color: #333;\\n}\\n\\n.projects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n\\n.project-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.project-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.project-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n.project-info[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n.project-info[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.project-tech[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #888;\\n  margin: 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.empty-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0 0 24px 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .sidenav[_ngcontent-%COMP%] {\\n    width: 200px;\\n  }\\n  .dashboard-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .welcome-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 16px;\\n  }\\n  .projects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatIconModule", "MatButtonModule", "MatCardModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "project_r1", "workMode", "ɵɵtextInterpolate1", "techStack", "ɵɵtemplate", "DashboardComponent_div_59_mat_card_1_div_13_Template", "DashboardComponent_div_59_mat_card_1_p_14_Template", "role", "clientName", "dailyRate", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "id", "DashboardComponent_div_59_mat_card_1_Template", "ctx_r1", "recentProjects", "DashboardComponent", "constructor", "authService", "projectService", "freelanceService", "freelanceProfile", "stats", "totalProjects", "averageDailyRate", "totalRevenue", "activeProjects", "ngOnInit", "currentUser", "getUser", "loadDashboardData", "getByIdWithProjects", "subscribe", "next", "profile", "updateStats", "error", "console", "getByFreelanceId", "projects", "slice", "calculateTotalRevenue", "filter", "p", "startDate", "Date", "length", "reduce", "total", "project", "durationInMonths", "daysPerYear", "monthlyDays", "logout", "getGreeting", "hour", "getHours", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ProjectService", "i3", "FreelanceService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_59_Template", "DashboardComponent_ng_template_60_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate2", "firstName", "ɵɵpipeBind2", "noProjects_r3", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "TitleCasePipe", "i5", "RouterLink", "i6", "MatIcon", "i7", "MatButton", "i8", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { AuthService } from '../../services/auth.service';\nimport { ProjectService, ProjectDto } from '../../services/project.service';\nimport { FreelanceService, FreelanceDto } from '../../services/freelance.service';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatIconModule,\n    MatButtonModule,\n    MatCardModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any;\n  freelanceProfile: FreelanceDto | null = null;\n  recentProjects: ProjectDto[] = [];\n  stats = {\n    totalProjects: 0,\n    averageDailyRate: 0,\n    totalRevenue: 0,\n    activeProjects: 0\n  };\n\n\n\n  constructor(\n    private authService: AuthService,\n    private projectService: ProjectService,\n    private freelanceService: FreelanceService\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getUser();\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    if (this.currentUser?.id) {\n      // Load freelance profile\n      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({\n        next: (profile) => {\n          this.freelanceProfile = profile;\n          this.updateStats(profile);\n        },\n        error: (error) => console.error('Error loading profile:', error)\n      });\n\n      // Load recent projects\n      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({\n        next: (projects) => {\n          this.recentProjects = projects.slice(0, 5); // Get 5 most recent\n        },\n        error: (error) => console.error('Error loading projects:', error)\n      });\n    }\n  }\n\n  updateStats(profile: FreelanceDto): void {\n    this.stats = {\n      totalProjects: profile.totalProjects || 0,\n      averageDailyRate: profile.averageDailyRate || 0,\n      totalRevenue: this.calculateTotalRevenue(),\n      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length\n    };\n  }\n\n  calculateTotalRevenue(): number {\n    return this.recentProjects.reduce((total, project) => {\n      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {\n        const monthlyDays = project.daysPerYear / 12;\n        return total + (project.dailyRate * monthlyDays * project.durationInMonths);\n      }\n      return total;\n    }, 0);\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n}\n", "<div class=\"dashboard-container\">\n        <!-- Welcome Section -->\n        <div class=\"welcome-section\">\n          <h1 class=\"welcome-title\">\n            {{ getGreeting() }}, {{ currentUser?.firstName }} ! 👋\n          </h1>\n          <p class=\"welcome-subtitle\">\n            Voici un aperçu de vos missions freelance\n          </p>\n        </div>\n\n        <!-- Stats Cards -->\n        <div class=\"stats-grid\">\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon projects\">\n                  <mat-icon>work</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalProjects }}</div>\n                  <div class=\"stat-label\">Projets totaux</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon rate\">\n                  <mat-icon>euro</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.averageDailyRate | number:'1.0-0' }}€</div>\n                  <div class=\"stat-label\">TJM moyen</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon revenue\">\n                  <mat-icon>trending_up</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalRevenue | number:'1.0-0' }}€</div>\n                  <div class=\"stat-label\">Revenus estimés</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-icon active\">\n                  <mat-icon>play_circle</mat-icon>\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.activeProjects }}</div>\n                  <div class=\"stat-label\">Projets actifs</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Recent Projects -->\n        <div class=\"recent-projects-section\">\n          <div class=\"section-header\">\n            <h2>Projets récents</h2>\n            <button mat-raised-button color=\"primary\" routerLink=\"/projects\">\n              Voir tous les projets\n            </button>\n          </div>\n\n          <div class=\"projects-grid\" *ngIf=\"recentProjects.length > 0; else noProjects\">\n            <mat-card class=\"project-card\" *ngFor=\"let project of recentProjects\">\n              <mat-card-header>\n                <mat-card-title>{{ project.role }}</mat-card-title>\n                <mat-card-subtitle>{{ project.clientName }}</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"project-info\">\n                  <div class=\"project-rate\">\n                    <mat-icon>euro</mat-icon>\n                    <span>{{ project.dailyRate }}€/jour</span>\n                  </div>\n                  <div class=\"project-mode\" *ngIf=\"project.workMode\">\n                    <mat-icon>location_on</mat-icon>\n                    <span>{{ project.workMode | titlecase }}</span>\n                  </div>\n                </div>\n                <p class=\"project-tech\" *ngIf=\"project.techStack\">\n                  {{ project.techStack }}\n                </p>\n              </mat-card-content>\n              <mat-card-actions>\n                <button mat-button [routerLink]=\"['/projects', project.id]\">\n                  Voir détails\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n\n          <ng-template #noProjects>\n            <mat-card class=\"empty-state\">\n              <mat-card-content>\n                <div class=\"empty-content\">\n                  <mat-icon class=\"empty-icon\">work_off</mat-icon>\n                  <h3>Aucun projet pour le moment</h3>\n                  <p>Commencez par ajouter votre premier projet !</p>\n                  <button mat-raised-button color=\"primary\" routerLink=\"/projects/new\">\n                    Ajouter un projet\n                  </button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </ng-template>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;ICuFlCC,EADF,CAAAC,cAAA,cAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;;;;IADEH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,UAAA,CAAAC,QAAA,EAAkC;;;;;IAG5CR,EAAA,CAAAC,cAAA,YAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAF,UAAA,CAAAG,SAAA,MACF;;;;;IAhBAV,EAFJ,CAAAC,cAAA,mBAAsE,sBACnD,qBACC;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACnDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC7CF,EAD6C,CAAAG,YAAA,EAAoB,EAC/C;IAIZH,EAHN,CAAAC,cAAA,uBAAkB,cACU,cACE,eACd;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACrCF,EADqC,CAAAG,YAAA,EAAO,EACtC;IACNH,EAAA,CAAAW,UAAA,KAAAC,oDAAA,kBAAmD;IAIrDZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,UAAA,KAAAE,kDAAA,gBAAkD;IAGpDb,EAAA,CAAAG,YAAA,EAAmB;IAEjBH,EADF,CAAAC,cAAA,wBAAkB,kBAC4C;IAC1DD,EAAA,CAAAE,MAAA,2BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IAvBSH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAE,UAAA,CAAAO,IAAA,CAAkB;IACfd,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAE,UAAA,CAAAQ,UAAA,CAAwB;IAMjCf,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAS,kBAAA,KAAAF,UAAA,CAAAS,SAAA,gBAA6B;IAEVhB,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAiB,UAAA,SAAAV,UAAA,CAAAC,QAAA,CAAsB;IAK1BR,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAiB,UAAA,SAAAV,UAAA,CAAAG,SAAA,CAAuB;IAK7BV,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAZ,UAAA,CAAAa,EAAA,EAAwC;;;;;IAtBjEpB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAW,UAAA,IAAAU,6CAAA,wBAAsE;IA0BxErB,EAAA,CAAAG,YAAA,EAAM;;;;IA1B+CH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAiB,UAAA,YAAAK,MAAA,CAAAC,cAAA,CAAiB;;;;;IAgC9DvB,EAHN,CAAAC,cAAA,mBAA8B,uBACV,cACW,mBACI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnDH,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,2BACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;;;ADjGvB,OAAM,MAAOqB,kBAAkB;EAa7BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,gBAAkC;IAFlC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,gBAAgB,GAAwB,IAAI;IAC5C,KAAAN,cAAc,GAAiB,EAAE;IACjC,KAAAO,KAAK,GAAG;MACNC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE;KACjB;EAQE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,OAAO,EAAE;IAC7C,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACF,WAAW,EAAEhB,EAAE,EAAE;MACxB;MACA,IAAI,CAACQ,gBAAgB,CAACW,mBAAmB,CAAC,IAAI,CAACH,WAAW,CAAChB,EAAE,CAAC,CAACoB,SAAS,CAAC;QACvEC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAI,CAACb,gBAAgB,GAAGa,OAAO;UAC/B,IAAI,CAACC,WAAW,CAACD,OAAO,CAAC;QAC3B,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK;OAChE,CAAC;MAEF;MACA,IAAI,CAACjB,cAAc,CAACmB,gBAAgB,CAAC,IAAI,CAACV,WAAW,CAAChB,EAAE,CAAC,CAACoB,SAAS,CAAC;QAClEC,IAAI,EAAGM,QAAQ,IAAI;UACjB,IAAI,CAACxB,cAAc,GAAGwB,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACDJ,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;OACjE,CAAC;;EAEN;EAEAD,WAAWA,CAACD,OAAqB;IAC/B,IAAI,CAACZ,KAAK,GAAG;MACXC,aAAa,EAAEW,OAAO,CAACX,aAAa,IAAI,CAAC;MACzCC,gBAAgB,EAAEU,OAAO,CAACV,gBAAgB,IAAI,CAAC;MAC/CC,YAAY,EAAE,IAAI,CAACgB,qBAAqB,EAAE;MAC1Cf,cAAc,EAAE,IAAI,CAACX,cAAc,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,IAAI,IAAIC,IAAI,CAACF,CAAC,CAACC,SAAS,CAAC,IAAI,IAAIC,IAAI,EAAE,CAAC,CAACC;KACrG;EACH;EAEAL,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC1B,cAAc,CAACgC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAI;MACnD,IAAIA,OAAO,CAACzC,SAAS,IAAIyC,OAAO,CAACC,gBAAgB,IAAID,OAAO,CAACE,WAAW,EAAE;QACxE,MAAMC,WAAW,GAAGH,OAAO,CAACE,WAAW,GAAG,EAAE;QAC5C,OAAOH,KAAK,GAAIC,OAAO,CAACzC,SAAS,GAAG4C,WAAW,GAAGH,OAAO,CAACC,gBAAiB;;MAE7E,OAAOF,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;EACP;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAACnC,WAAW,CAACmC,MAAM,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAG,IAAIV,IAAI,EAAE,CAACW,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;;;uBAzEWvC,kBAAkB,EAAAxB,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnE,EAAA,CAAAiE,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArE,EAAA,CAAAiE,iBAAA,CAAAK,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAlB/C,kBAAkB;MAAAgD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1E,EAAA,CAAA2E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBrBjF,EAHV,CAAAC,cAAA,aAAiC,aAEI,YACD;UACxBD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAC1BD,EAAA,CAAAE,MAAA,uDACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAQIH,EALV,CAAAC,cAAA,aAAwB,kBACM,uBACR,aACU,cACQ,gBACpB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;UAEJH,EADF,CAAAC,cAAA,cAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAI9CF,EAJ8C,CAAAG,YAAA,EAAM,EACxC,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,mBAA4B,wBACR,cACU,eACI,gBAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;UAEJH,EADF,CAAAC,cAAA,cAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAA8C;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7EH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAIzCF,EAJyC,CAAAG,YAAA,EAAM,EACnC,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,mBAA4B,wBACR,cACU,eACO,gBACnB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,cAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAA0C;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzEH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAI/CF,EAJ+C,CAAAG,YAAA,EAAM,EACzC,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,mBAA4B,wBACR,cACU,eACM,gBAClB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,cAAuB,eACI;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAKhDF,EALgD,CAAAG,YAAA,EAAM,EACxC,EACF,EACW,EACV,EACP;UAKFH,EAFJ,CAAAC,cAAA,eAAqC,eACP,UACtB;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAE,MAAA,+BACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UA+BNH,EA7BA,CAAAW,UAAA,KAAAwE,kCAAA,kBAA8E,KAAAC,0CAAA,iCAAApF,EAAA,CAAAqF,sBAAA,CA6BrD;UAenCrF,EADE,CAAAG,YAAA,EAAM,EACF;;;;UAvHMH,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAsF,kBAAA,MAAAJ,GAAA,CAAApB,WAAA,UAAAoB,GAAA,CAAA9C,WAAA,kBAAA8C,GAAA,CAAA9C,WAAA,CAAAmD,SAAA,qBACF;UAeiCvF,EAAA,CAAAI,SAAA,IAAyB;UAAzBJ,EAAA,CAAAK,iBAAA,CAAA6E,GAAA,CAAApD,KAAA,CAAAC,aAAA,CAAyB;UAczB/B,EAAA,CAAAI,SAAA,IAA8C;UAA9CJ,EAAA,CAAAS,kBAAA,KAAAT,EAAA,CAAAwF,WAAA,QAAAN,GAAA,CAAApD,KAAA,CAAAE,gBAAA,qBAA8C;UAc9ChC,EAAA,CAAAI,SAAA,IAA0C;UAA1CJ,EAAA,CAAAS,kBAAA,KAAAT,EAAA,CAAAwF,WAAA,SAAAN,GAAA,CAAApD,KAAA,CAAAG,YAAA,qBAA0C;UAc1CjC,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAK,iBAAA,CAAA6E,GAAA,CAAApD,KAAA,CAAAI,cAAA,CAA0B;UAiB/BlC,EAAA,CAAAI,SAAA,GAAiC;UAAAJ,EAAjC,CAAAiB,UAAA,SAAAiE,GAAA,CAAA3D,cAAA,CAAA+B,MAAA,KAAiC,aAAAmC,aAAA,CAAe;;;qBDjElF9F,YAAY,EAAA+F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,aAAA,EACZlG,YAAY,EAAAmG,EAAA,CAAAC,UAAA,EACZnG,aAAa,EAAAoG,EAAA,CAAAC,OAAA,EACbpG,eAAe,EAAAqG,EAAA,CAAAC,SAAA,EACfrG,aAAa,EAAAsG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}