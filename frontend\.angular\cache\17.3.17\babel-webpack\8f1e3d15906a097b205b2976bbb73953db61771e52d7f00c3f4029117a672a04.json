{"ast": null, "code": "import { BehaviorSubject, Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = `${environment.apiUrl}/auth`;\n    this.TOKEN_KEY = 'indezy_token';\n    this.USER_KEY = 'indezy_user';\n    this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n    this.currentUser$ = this.currentUserSubject.asObservable();\n  }\n  login(credentials) {\n    // Mock login for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        if (credentials.email && credentials.password) {\n          const mockResponse = {\n            token: 'mock-jwt-token-' + Date.now(),\n            user: {\n              id: 1,\n              email: credentials.email,\n              firstName: '<PERSON>',\n              lastName: 'Doe'\n            }\n          };\n          this.setToken(mockResponse.token);\n          this.setUser(mockResponse.user);\n          this.currentUserSubject.next(mockResponse.user);\n          observer.next(mockResponse);\n          observer.complete();\n        } else {\n          observer.error({\n            status: 401,\n            message: 'Invalid credentials'\n          });\n        }\n      }, 1000);\n    });\n  }\n  register(userData) {\n    // Mock register for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        if (userData.email && userData.password && userData.firstName && userData.lastName) {\n          const mockResponse = {\n            token: 'mock-jwt-token-' + Date.now(),\n            user: {\n              id: 1,\n              email: userData.email,\n              firstName: userData.firstName,\n              lastName: userData.lastName\n            }\n          };\n          this.setToken(mockResponse.token);\n          this.setUser(mockResponse.user);\n          this.currentUserSubject.next(mockResponse.user);\n          observer.next(mockResponse);\n          observer.complete();\n        } else {\n          observer.error({\n            status: 400,\n            message: 'Invalid user data'\n          });\n        }\n      }, 1000);\n    });\n  }\n  logout() {\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  getToken() {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n  getUser() {\n    return this.getUserFromStorage();\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    if (!token) {\n      return false;\n    }\n    // For mock tokens, just check if token exists and user exists\n    if (token.startsWith('mock-jwt-token-')) {\n      return this.getUser() !== null;\n    }\n    // Check if real JWT token is expired (basic check)\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n  setToken(token) {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n  setUser(user) {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n  }\n  getUserFromStorage() {\n    const userStr = localStorage.getItem(this.USER_KEY);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "TOKEN_KEY", "USER_KEY", "currentUserSubject", "getUserFromStorage", "currentUser$", "asObservable", "login", "credentials", "observer", "setTimeout", "email", "password", "mockResponse", "token", "Date", "now", "user", "id", "firstName", "lastName", "setToken", "setUser", "next", "complete", "error", "status", "message", "register", "userData", "logout", "localStorage", "removeItem", "navigate", "getToken", "getItem", "getUser", "isAuthenticated", "startsWith", "payload", "JSON", "parse", "atob", "split", "currentTime", "Math", "floor", "exp", "setItem", "stringify", "userStr", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { Router } from '@angular/router';\nimport { environment } from '../../environments/environment';\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  token: string;\n  user: {\n    id: number;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n}\n\nexport interface RegisterRequest {\n  firstName: string;\n  lastName: string;\n  email: string;\n  password: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = `${environment.apiUrl}/auth`;\n  private readonly TOKEN_KEY = 'indezy_token';\n  private readonly USER_KEY = 'indezy_user';\n\n  private currentUserSubject = new BehaviorSubject<any>(this.getUserFromStorage());\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  login(credentials: LoginRequest): Observable<LoginResponse> {\n    // Mock login for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        if (credentials.email && credentials.password) {\n          const mockResponse: LoginResponse = {\n            token: 'mock-jwt-token-' + Date.now(),\n            user: {\n              id: 1,\n              email: credentials.email,\n              firstName: 'John',\n              lastName: 'Doe'\n            }\n          };\n          this.setToken(mockResponse.token);\n          this.setUser(mockResponse.user);\n          this.currentUserSubject.next(mockResponse.user);\n          observer.next(mockResponse);\n          observer.complete();\n        } else {\n          observer.error({ status: 401, message: 'Invalid credentials' });\n        }\n      }, 1000);\n    });\n  }\n\n  register(userData: RegisterRequest): Observable<LoginResponse> {\n    // Mock register for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        if (userData.email && userData.password && userData.firstName && userData.lastName) {\n          const mockResponse: LoginResponse = {\n            token: 'mock-jwt-token-' + Date.now(),\n            user: {\n              id: 1,\n              email: userData.email,\n              firstName: userData.firstName,\n              lastName: userData.lastName\n            }\n          };\n          this.setToken(mockResponse.token);\n          this.setUser(mockResponse.user);\n          this.currentUserSubject.next(mockResponse.user);\n          observer.next(mockResponse);\n          observer.complete();\n        } else {\n          observer.error({ status: 400, message: 'Invalid user data' });\n        }\n      }, 1000);\n    });\n  }\n\n  logout(): void {\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  getUser(): any {\n    return this.getUserFromStorage();\n  }\n\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    if (!token) {\n      return false;\n    }\n\n    // For mock tokens, just check if token exists and user exists\n    if (token.startsWith('mock-jwt-token-')) {\n      return this.getUser() !== null;\n    }\n\n    // Check if real JWT token is expired (basic check)\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n\n  private setUser(user: any): void {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n  }\n\n  private getUserFromStorage(): any {\n    const userStr = localStorage.getItem(this.USER_KEY);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAEC,UAAU,QAAQ,MAAM;AAElD,SAASC,WAAW,QAAQ,gCAAgC;;;;AA2B5D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAG,GAAGL,WAAW,CAACM,MAAM,OAAO;IACtC,KAAAC,SAAS,GAAG,cAAc;IAC1B,KAAAC,QAAQ,GAAG,aAAa;IAEjC,KAAAC,kBAAkB,GAAG,IAAIX,eAAe,CAAM,IAAI,CAACY,kBAAkB,EAAE,CAAC;IACzE,KAAAC,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;EAKzD;EAEHC,KAAKA,CAACC,WAAyB;IAC7B;IACA,OAAO,IAAIf,UAAU,CAACgB,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACd,IAAIF,WAAW,CAACG,KAAK,IAAIH,WAAW,CAACI,QAAQ,EAAE;UAC7C,MAAMC,YAAY,GAAkB;YAClCC,KAAK,EAAE,iBAAiB,GAAGC,IAAI,CAACC,GAAG,EAAE;YACrCC,IAAI,EAAE;cACJC,EAAE,EAAE,CAAC;cACLP,KAAK,EAAEH,WAAW,CAACG,KAAK;cACxBQ,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;;WAEb;UACD,IAAI,CAACC,QAAQ,CAACR,YAAY,CAACC,KAAK,CAAC;UACjC,IAAI,CAACQ,OAAO,CAACT,YAAY,CAACI,IAAI,CAAC;UAC/B,IAAI,CAACd,kBAAkB,CAACoB,IAAI,CAACV,YAAY,CAACI,IAAI,CAAC;UAC/CR,QAAQ,CAACc,IAAI,CAACV,YAAY,CAAC;UAC3BJ,QAAQ,CAACe,QAAQ,EAAE;SACpB,MAAM;UACLf,QAAQ,CAACgB,KAAK,CAAC;YAAEC,MAAM,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAqB,CAAE,CAAC;;MAEnE,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAACC,QAAyB;IAChC;IACA,OAAO,IAAIpC,UAAU,CAACgB,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACd,IAAImB,QAAQ,CAAClB,KAAK,IAAIkB,QAAQ,CAACjB,QAAQ,IAAIiB,QAAQ,CAACV,SAAS,IAAIU,QAAQ,CAACT,QAAQ,EAAE;UAClF,MAAMP,YAAY,GAAkB;YAClCC,KAAK,EAAE,iBAAiB,GAAGC,IAAI,CAACC,GAAG,EAAE;YACrCC,IAAI,EAAE;cACJC,EAAE,EAAE,CAAC;cACLP,KAAK,EAAEkB,QAAQ,CAAClB,KAAK;cACrBQ,SAAS,EAAEU,QAAQ,CAACV,SAAS;cAC7BC,QAAQ,EAAES,QAAQ,CAACT;;WAEtB;UACD,IAAI,CAACC,QAAQ,CAACR,YAAY,CAACC,KAAK,CAAC;UACjC,IAAI,CAACQ,OAAO,CAACT,YAAY,CAACI,IAAI,CAAC;UAC/B,IAAI,CAACd,kBAAkB,CAACoB,IAAI,CAACV,YAAY,CAACI,IAAI,CAAC;UAC/CR,QAAQ,CAACc,IAAI,CAACV,YAAY,CAAC;UAC3BJ,QAAQ,CAACe,QAAQ,EAAE;SACpB,MAAM;UACLf,QAAQ,CAACgB,KAAK,CAAC;YAAEC,MAAM,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAmB,CAAE,CAAC;;MAEjE,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EAEAG,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,IAAI,CAAC/B,SAAS,CAAC;IACvC8B,YAAY,CAACC,UAAU,CAAC,IAAI,CAAC9B,QAAQ,CAAC;IACtC,IAAI,CAACC,kBAAkB,CAACoB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACzB,MAAM,CAACmC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,QAAQA,CAAA;IACN,OAAOH,YAAY,CAACI,OAAO,CAAC,IAAI,CAAClC,SAAS,CAAC;EAC7C;EAEAmC,OAAOA,CAAA;IACL,OAAO,IAAI,CAAChC,kBAAkB,EAAE;EAClC;EAEAiC,eAAeA,CAAA;IACb,MAAMvB,KAAK,GAAG,IAAI,CAACoB,QAAQ,EAAE;IAC7B,IAAI,CAACpB,KAAK,EAAE;MACV,OAAO,KAAK;;IAGd;IACA,IAAIA,KAAK,CAACwB,UAAU,CAAC,iBAAiB,CAAC,EAAE;MACvC,OAAO,IAAI,CAACF,OAAO,EAAE,KAAK,IAAI;;IAGhC;IACA,IAAI;MACF,MAAMG,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC5B,KAAK,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC/B,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MACjD,OAAOuB,OAAO,CAACQ,GAAG,GAAGH,WAAW;KACjC,CAAC,MAAM;MACN,OAAO,KAAK;;EAEhB;EAEQvB,QAAQA,CAACP,KAAa;IAC5BiB,YAAY,CAACiB,OAAO,CAAC,IAAI,CAAC/C,SAAS,EAAEa,KAAK,CAAC;EAC7C;EAEQQ,OAAOA,CAACL,IAAS;IACvBc,YAAY,CAACiB,OAAO,CAAC,IAAI,CAAC9C,QAAQ,EAAEsC,IAAI,CAACS,SAAS,CAAChC,IAAI,CAAC,CAAC;EAC3D;EAEQb,kBAAkBA,CAAA;IACxB,MAAM8C,OAAO,GAAGnB,YAAY,CAACI,OAAO,CAAC,IAAI,CAACjC,QAAQ,CAAC;IACnD,OAAOgD,OAAO,GAAGV,IAAI,CAACC,KAAK,CAACS,OAAO,CAAC,GAAG,IAAI;EAC7C;;;uBAhHWvD,WAAW,EAAAwD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX7D,WAAW;MAAA8D,OAAA,EAAX9D,WAAW,CAAA+D,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}