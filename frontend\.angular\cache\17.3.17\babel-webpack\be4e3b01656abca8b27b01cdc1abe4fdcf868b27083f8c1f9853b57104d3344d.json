{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/project.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction ProjectDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails du projet...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_divider_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Jours/an \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.daysPerYear, \" jours \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_divider_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Dur\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.durationInMonths, \" mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_divider_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Renouvellement \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Tous les \", ctx_r1.project.orderRenewalInMonths, \" mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" T\\u00E9l\\u00E9travail \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.remoteDaysPerMonth, \" jours/mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_mat_divider_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Sur site \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.onsiteDaysPerMonth, \" jours/mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Mode de travail \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, ProjectDetailComponent_div_2_mat_card_100_div_7_Template, 7, 1, \"div\", 18)(8, ProjectDetailComponent_div_2_mat_card_100_mat_divider_8_Template, 1, 0, \"mat-divider\", 17)(9, ProjectDetailComponent_div_2_mat_card_100_div_9_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.remoteDaysPerMonth);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.remoteDaysPerMonth && ctx_r1.project.onsiteDaysPerMonth);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.onsiteDaysPerMonth);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_101_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"span\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 25);\n    i0.ɵɵtext(4, \"\\u00C9chou\\u00E9es\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.failedSteps);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Progression \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 21)(8, \"div\", 22)(9, \"div\", 23)(10, \"span\", 24);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 25);\n    i0.ɵɵtext(13, \"Termin\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 23)(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 25);\n    i0.ɵɵtext(18, \"En cours\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ProjectDetailComponent_div_2_mat_card_101_div_19_Template, 5, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 27);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.project.completedSteps || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.project.totalSteps || 0) - (ctx_r1.project.completedSteps || 0) - (ctx_r1.project.failedSteps || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.failedSteps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getProgressPercentage(), \"% compl\\u00E9t\\u00E9 \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_102_mat_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const star_r3 = ctx.$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r3 === \"star\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", star_r3, \" \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00C9valuation personnelle \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 28);\n    i0.ɵɵtemplate(8, ProjectDetailComponent_div_2_mat_card_102_mat_icon_8_Template, 2, 3, \"mat-icon\", 29);\n    i0.ɵɵelementStart(9, \"span\", 30);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getRatingStars(ctx_r1.project.personalRating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.project.personalRating, \"/5\");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const document_r4 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(document_r4);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_2_mat_card_103_div_7_div_1_Template, 5, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.project.documents);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_mat_divider_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Lien projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16)(6, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_mat_card_103_div_9_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.openLink());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"open_in_new\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"href\", ctx_r1.project.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.link, \" \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"folder\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Documents et liens \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, ProjectDetailComponent_div_2_mat_card_103_div_7_Template, 2, 1, \"div\", 31)(8, ProjectDetailComponent_div_2_mat_card_103_mat_divider_8_Template, 1, 0, \"mat-divider\", 17)(9, ProjectDetailComponent_div_2_mat_card_103_div_9_Template, 10, 2, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.documents == null ? null : ctx_r1.project.documents.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.project.documents == null ? null : ctx_r1.project.documents.length) && ctx_r1.project.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.link);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Notes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 37);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.notes, \" \");\n  }\n}\nfunction ProjectDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-card\", 5)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"div\", 6)(5, \"div\", 7)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-subtitle\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-card-actions\")(14, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBack());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onEdit());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDelete());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 12)(27, \"mat-card\", 13)(28, \"mat-card-header\")(29, \"mat-card-title\")(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Informations du projet \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"mat-card-content\")(34, \"div\", 14)(35, \"div\", 15)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 16);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(41, \"mat-divider\");\n    i0.ɵɵelementStart(42, \"div\", 14)(43, \"div\", 15)(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" Stack technique \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 16);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(49, \"mat-divider\");\n    i0.ɵɵelementStart(50, \"div\", 14)(51, \"div\", 15)(52, \"mat-icon\");\n    i0.ɵɵtext(53, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(54, \" Avantages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 16);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"mat-card\", 13)(58, \"mat-card-header\")(59, \"mat-card-title\")(60, \"mat-icon\");\n    i0.ɵɵtext(61, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" Informations financi\\u00E8res \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"mat-card-content\")(64, \"div\", 14)(65, \"div\", 15)(66, \"mat-icon\");\n    i0.ɵɵtext(67, \"payments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(68, \" TJM \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 16);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(71, \"mat-divider\");\n    i0.ɵɵelementStart(72, \"div\", 14)(73, \"div\", 15)(74, \"mat-icon\");\n    i0.ɵɵtext(75, \"calculate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(76, \" Revenus estim\\u00E9s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 16);\n    i0.ɵɵtext(78);\n    i0.ɵɵpipe(79, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(80, ProjectDetailComponent_div_2_mat_divider_80_Template, 1, 0, \"mat-divider\", 17)(81, ProjectDetailComponent_div_2_div_81_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"mat-card\", 13)(83, \"mat-card-header\")(84, \"mat-card-title\")(85, \"mat-icon\");\n    i0.ɵɵtext(86, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" Planning \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"mat-card-content\")(89, \"div\", 14)(90, \"div\", 15)(91, \"mat-icon\");\n    i0.ɵɵtext(92, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \" Date de d\\u00E9but \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"div\", 16);\n    i0.ɵɵtext(95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(96, ProjectDetailComponent_div_2_mat_divider_96_Template, 1, 0, \"mat-divider\", 17)(97, ProjectDetailComponent_div_2_div_97_Template, 7, 1, \"div\", 18)(98, ProjectDetailComponent_div_2_mat_divider_98_Template, 1, 0, \"mat-divider\", 17)(99, ProjectDetailComponent_div_2_div_99_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(100, ProjectDetailComponent_div_2_mat_card_100_Template, 10, 3, \"mat-card\", 19)(101, ProjectDetailComponent_div_2_mat_card_101_Template, 22, 4, \"mat-card\", 19)(102, ProjectDetailComponent_div_2_mat_card_102_Template, 11, 2, \"mat-card\", 19)(103, ProjectDetailComponent_div_2_mat_card_103_Template, 10, 3, \"mat-card\", 19)(104, ProjectDetailComponent_div_2_mat_card_104_Template, 9, 1, \"mat-card\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.role, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.getWorkModeColor(ctx_r1.project.workMode));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getWorkModeLabel(ctx_r1.project.workMode), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Client: \", ctx_r1.project.clientName, \" \\u2022 \", ctx_r1.project.dailyRate, \"\\u20AC/jour \");\n    i0.ɵɵadvance(28);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.description || \"Non sp\\u00E9cifi\\u00E9e\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.techStack || \"Non sp\\u00E9cifi\\u00E9e\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.advantages || \"Non sp\\u00E9cifi\\u00E9s\", \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.dailyRate, \"\\u20AC/jour \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(79, 22, ctx_r1.calculateTotalRevenue(), \"1.0-0\"), \"\\u20AC \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.daysPerYear);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.daysPerYear);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatDate(ctx_r1.project.startDate), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.durationInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.durationInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.orderRenewalInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.orderRenewalInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.workMode === \"HYBRID\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.totalSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.personalRating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.project.documents == null ? null : ctx_r1.project.documents.length) || ctx_r1.project.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.notes);\n  }\n}\nexport let ProjectDetailComponent = /*#__PURE__*/(() => {\n  class ProjectDetailComponent {\n    constructor(projectService, router, route, snackBar) {\n      this.projectService = projectService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        if (params['id']) {\n          this.projectId = +params['id'];\n          this.loadProject();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadProject() {\n      if (!this.projectId) return;\n      this.isLoading = true;\n      this.projectService.getById(this.projectId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: project => {\n          if (project) {\n            this.project = project;\n          } else {\n            this.snackBar.open('Projet non trouvé', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/projects']);\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading project:', error);\n          this.snackBar.open('Erreur lors du chargement du projet', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    onBack() {\n      this.router.navigate(['/projects']);\n    }\n    onEdit() {\n      if (this.project) {\n        this.router.navigate(['/projects', this.project.id, 'edit']);\n      }\n    }\n    onDelete() {\n      if (!this.project) return;\n      if (confirm(`Êtes-vous sûr de vouloir supprimer le projet \"${this.project.role}\" ?`)) {\n        this.projectService.delete(this.project.id).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.snackBar.open('Projet supprimé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/projects']);\n          },\n          error: error => {\n            console.error('Error deleting project:', error);\n            this.snackBar.open('Erreur lors de la suppression du projet', 'Fermer', {\n              duration: 3000\n            });\n          }\n        });\n      }\n    }\n    // Utility methods\n    getWorkModeLabel(workMode) {\n      switch (workMode) {\n        case 'REMOTE':\n          return 'Télétravail';\n        case 'ONSITE':\n          return 'Sur site';\n        case 'HYBRID':\n          return 'Hybride';\n        default:\n          return 'Non spécifié';\n      }\n    }\n    getWorkModeColor(workMode) {\n      switch (workMode) {\n        case 'REMOTE':\n          return 'primary';\n        case 'ONSITE':\n          return 'accent';\n        case 'HYBRID':\n          return 'warn';\n        default:\n          return '';\n      }\n    }\n    getRatingStars(rating) {\n      const stars = [];\n      for (let i = 1; i <= 5; i++) {\n        stars.push(i <= (rating || 0) ? 'star' : 'star_border');\n      }\n      return stars;\n    }\n    formatDate(dateString) {\n      if (!dateString) return 'Non spécifié';\n      return new Date(dateString).toLocaleDateString('fr-FR');\n    }\n    calculateTotalRevenue() {\n      if (!this.project?.dailyRate || !this.project?.durationInMonths) return 0;\n      const daysPerMonth = (this.project.daysPerYear || 220) / 12;\n      return this.project.dailyRate * daysPerMonth * this.project.durationInMonths;\n    }\n    getProgressPercentage() {\n      if (!this.project?.totalSteps) return 0;\n      return Math.round((this.project.completedSteps || 0) / this.project.totalSteps * 100);\n    }\n    openLink() {\n      if (this.project?.link) {\n        window.open(this.project.link, '_blank');\n      }\n    }\n    static {\n      this.ɵfac = function ProjectDetailComponent_Factory(t) {\n        return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectDetailComponent,\n        selectors: [[\"app-project-detail\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 2,\n        consts: [[1, \"project-detail-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"project-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"project-content\"], [1, \"header-card\"], [1, \"title-row\"], [1, \"project-name\"], [\"selected\", \"\", 3, \"color\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"details-grid\"], [1, \"info-card\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [4, \"ngIf\"], [\"class\", \"info-item\", 4, \"ngIf\"], [\"class\", \"info-card\", 4, \"ngIf\"], [\"class\", \"info-card notes-card\", 4, \"ngIf\"], [1, \"progress-info\"], [1, \"progress-stats\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"progress-percentage\"], [1, \"rating-display\"], [3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [\"class\", \"documents-list\", 4, \"ngIf\"], [1, \"documents-list\"], [\"class\", \"document-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"document-item\"], [\"target\", \"_blank\", 1, \"project-link\", 3, \"click\", \"href\"], [1, \"info-card\", \"notes-card\"], [1, \"notes-content\"]],\n        template: function ProjectDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, ProjectDetailComponent_div_1_Template, 4, 0, \"div\", 1)(2, ProjectDetailComponent_div_2_Template, 105, 25, \"div\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.project);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, RouterModule, MatCardModule, i5.MatCard, i5.MatCardActions, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, MatButtonModule, i6.MatButton, MatIconModule, i7.MatIcon, MatChipsModule, i8.MatChip, MatProgressSpinnerModule, i9.MatProgressSpinner],\n        styles: [\".project-detail-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.project-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.project-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{margin-bottom:24px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:1.5rem;font-weight:500}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:1.5rem;height:1.5rem;color:var(--mdc-theme-primary, #1976d2)}@media (max-width: 600px){.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:12px}}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{margin-top:8px;color:#0009;font-size:.9rem}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:16px 24px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.1rem;width:1.1rem;height:1.1rem}@media (max-width: 600px){.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]{flex-wrap:wrap}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{flex:1;min-width:120px}}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:24px}@media (max-width: 768px){.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:1.1rem}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.2rem;width:1.2rem;height:1.2rem;color:var(--mdc-theme-primary, #1976d2)}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding-top:16px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:12px 0}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:500;color:#000000b3;min-width:120px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem;color:#00000080}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{flex:1;text-align:right}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]{color:var(--mdc-theme-primary, #1976d2);text-decoration:none;display:flex;align-items:center;justify-content:flex-end;gap:4px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.9rem;width:.9rem;height:.9rem}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 600px){.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:4px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{text-align:left}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]{justify-content:flex-start}}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%]{margin:0}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-bottom:16px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{text-align:center}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:1.5rem;font-weight:600;color:var(--mdc-theme-primary, #1976d2)}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.8rem;color:#0009}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%]{text-align:center;font-size:1.1rem;font-weight:500;color:#000c}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#ddd;font-size:1.5rem;width:1.5rem;height:1.5rem}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   mat-icon.filled[_ngcontent-%COMP%]{color:#ffc107}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{font-weight:500;color:#000000b3}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .documents-list[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 0}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .documents-list[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#00000080;font-size:1rem;width:1rem;height:1rem}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%]{grid-column:1/-1}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%]   .notes-content[_ngcontent-%COMP%]{background-color:#00000005;border-left:4px solid var(--mdc-theme-primary, #1976d2);padding:16px;border-radius:4px;line-height:1.6;white-space:pre-wrap}.mat-mdc-chip[_ngcontent-%COMP%]{font-size:.75rem;min-height:28px}.mat-mdc-chip.mat-primary[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.mat-mdc-chip.mat-accent[_ngcontent-%COMP%]{background-color:#fff3e0;color:#f57c00}.mat-mdc-chip.mat-warn[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]{background-color:var(--mdc-theme-primary, #1976d2);color:#fff}.mat-mdc-button.mat-warn[_ngcontent-%COMP%]{color:#d32f2f}.mat-mdc-button.mat-warn[_ngcontent-%COMP%]:hover{background-color:#d32f2f0a}@media (max-width: 768px){.project-detail-container[_ngcontent-%COMP%]{padding:16px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{margin-bottom:16px}.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]{gap:16px}}\"]\n      });\n    }\n  }\n  return ProjectDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}