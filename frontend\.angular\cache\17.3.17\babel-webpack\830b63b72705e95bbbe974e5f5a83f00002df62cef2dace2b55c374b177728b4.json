{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FreelanceService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/freelances`;\n  }\n  getAll() {\n    return this.http.get(this.API_URL);\n  }\n  getById(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  getByIdWithProjects(id) {\n    // Mock data for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        const mockFreelance = {\n          id: id,\n          firstName: 'John',\n          lastName: 'Doe',\n          email: '<EMAIL>',\n          phone: '+33 6 12 34 56 78',\n          address: '123 Rue de la Paix, 75001 Paris',\n          totalProjects: 3,\n          averageDailyRate: 633\n        };\n        observer.next(mockFreelance);\n        observer.complete();\n      }, 500);\n    });\n  }\n  getByEmail(email) {\n    const params = new HttpParams().set('email', email);\n    return this.http.get(`${this.API_URL}/by-email`, {\n      params\n    });\n  }\n  create(freelance) {\n    return this.http.post(this.API_URL, freelance);\n  }\n  update(id, freelance) {\n    return this.http.put(`${this.API_URL}/${id}`, freelance);\n  }\n  delete(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  checkEmailExists(email) {\n    const params = new HttpParams().set('email', email);\n    return this.http.get(`${this.API_URL}/exists`, {\n      params\n    });\n  }\n  updatePassword(id, newPassword) {\n    return this.http.patch(`${this.API_URL}/${id}/password`, newPassword);\n  }\n  static {\n    this.ɵfac = function FreelanceService_Factory(t) {\n      return new (t || FreelanceService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FreelanceService,\n      factory: FreelanceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "Observable", "environment", "FreelanceService", "constructor", "http", "API_URL", "apiUrl", "getAll", "get", "getById", "id", "getByIdWithProjects", "observer", "setTimeout", "mockFreelance", "firstName", "lastName", "email", "phone", "address", "totalProjects", "averageDailyRate", "next", "complete", "getByEmail", "params", "set", "create", "freelance", "post", "update", "put", "delete", "checkEmailExists", "updatePassword", "newPassword", "patch", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\services\\freelance.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\nexport interface FreelanceDto {\n  id?: number;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  birthDate?: string;\n  address?: string;\n  city?: string;\n  status: 'AVAILABLE' | 'EMPLOYED' | 'UNAVAILABLE';\n  noticePeriodInDays?: number;\n  availabilityDate?: string;\n  reversionRate?: number;\n  cvFilePath?: string;\n  fullName?: string;\n  totalProjects?: number;\n  averageDailyRate?: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FreelanceService {\n  private readonly API_URL = `${environment.apiUrl}/freelances`;\n\n  constructor(private http: HttpClient) {}\n\n  getAll(): Observable<FreelanceDto[]> {\n    return this.http.get<FreelanceDto[]>(this.API_URL);\n  }\n\n  getById(id: number): Observable<FreelanceDto> {\n    return this.http.get<FreelanceDto>(`${this.API_URL}/${id}`);\n  }\n\n  getByIdWithProjects(id: number): Observable<FreelanceDto> {\n    // Mock data for development - replace with real API call when backend is ready\n    return new Observable(observer => {\n      setTimeout(() => {\n        const mockFreelance: FreelanceDto = {\n          id: id,\n          firstName: 'John',\n          lastName: 'Doe',\n          email: '<EMAIL>',\n          phone: '+33 6 12 34 56 78',\n          address: '123 Rue de la Paix, 75001 Paris',\n          totalProjects: 3,\n          averageDailyRate: 633\n        };\n        observer.next(mockFreelance);\n        observer.complete();\n      }, 500);\n    });\n  }\n\n  getByEmail(email: string): Observable<FreelanceDto> {\n    const params = new HttpParams().set('email', email);\n    return this.http.get<FreelanceDto>(`${this.API_URL}/by-email`, { params });\n  }\n\n  create(freelance: FreelanceDto): Observable<FreelanceDto> {\n    return this.http.post<FreelanceDto>(this.API_URL, freelance);\n  }\n\n  update(id: number, freelance: FreelanceDto): Observable<FreelanceDto> {\n    return this.http.put<FreelanceDto>(`${this.API_URL}/${id}`, freelance);\n  }\n\n  delete(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  checkEmailExists(email: string): Observable<boolean> {\n    const params = new HttpParams().set('email', email);\n    return this.http.get<boolean>(`${this.API_URL}/exists`, { params });\n  }\n\n  updatePassword(id: number, newPassword: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/password`, newPassword);\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,WAAW,QAAQ,gCAAgC;;;AAwB5D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,GAAGJ,WAAW,CAACK,MAAM,aAAa;EAEtB;EAEvCC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAiB,IAAI,CAACH,OAAO,CAAC;EACpD;EAEAI,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAe,GAAG,IAAI,CAACH,OAAO,IAAIK,EAAE,EAAE,CAAC;EAC7D;EAEAC,mBAAmBA,CAACD,EAAU;IAC5B;IACA,OAAO,IAAIV,UAAU,CAACY,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACd,MAAMC,aAAa,GAAiB;UAClCJ,EAAE,EAAEA,EAAE;UACNK,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,KAAK;UACfC,KAAK,EAAE,oBAAoB;UAC3BC,KAAK,EAAE,mBAAmB;UAC1BC,OAAO,EAAE,iCAAiC;UAC1CC,aAAa,EAAE,CAAC;UAChBC,gBAAgB,EAAE;SACnB;QACDT,QAAQ,CAACU,IAAI,CAACR,aAAa,CAAC;QAC5BF,QAAQ,CAACW,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEAC,UAAUA,CAACP,KAAa;IACtB,MAAMQ,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAAC2B,GAAG,CAAC,OAAO,EAAET,KAAK,CAAC;IACnD,OAAO,IAAI,CAACb,IAAI,CAACI,GAAG,CAAe,GAAG,IAAI,CAACH,OAAO,WAAW,EAAE;MAAEoB;IAAM,CAAE,CAAC;EAC5E;EAEAE,MAAMA,CAACC,SAAuB;IAC5B,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAe,IAAI,CAACxB,OAAO,EAAEuB,SAAS,CAAC;EAC9D;EAEAE,MAAMA,CAACpB,EAAU,EAAEkB,SAAuB;IACxC,OAAO,IAAI,CAACxB,IAAI,CAAC2B,GAAG,CAAe,GAAG,IAAI,CAAC1B,OAAO,IAAIK,EAAE,EAAE,EAAEkB,SAAS,CAAC;EACxE;EAEAI,MAAMA,CAACtB,EAAU;IACf,OAAO,IAAI,CAACN,IAAI,CAAC4B,MAAM,CAAO,GAAG,IAAI,CAAC3B,OAAO,IAAIK,EAAE,EAAE,CAAC;EACxD;EAEAuB,gBAAgBA,CAAChB,KAAa;IAC5B,MAAMQ,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAAC2B,GAAG,CAAC,OAAO,EAAET,KAAK,CAAC;IACnD,OAAO,IAAI,CAACb,IAAI,CAACI,GAAG,CAAU,GAAG,IAAI,CAACH,OAAO,SAAS,EAAE;MAAEoB;IAAM,CAAE,CAAC;EACrE;EAEAS,cAAcA,CAACxB,EAAU,EAAEyB,WAAmB;IAC5C,OAAO,IAAI,CAAC/B,IAAI,CAACgC,KAAK,CAAO,GAAG,IAAI,CAAC/B,OAAO,IAAIK,EAAE,WAAW,EAAEyB,WAAW,CAAC;EAC7E;;;uBAzDWjC,gBAAgB,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBtC,gBAAgB;MAAAuC,OAAA,EAAhBvC,gBAAgB,CAAAwC,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}