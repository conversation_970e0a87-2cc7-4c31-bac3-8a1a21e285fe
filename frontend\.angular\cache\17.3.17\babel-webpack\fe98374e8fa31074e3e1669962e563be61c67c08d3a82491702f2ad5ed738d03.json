{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let FreelanceService = /*#__PURE__*/(() => {\n  class FreelanceService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = `${environment.apiUrl}/freelances`;\n    }\n    getAll() {\n      return this.http.get(this.API_URL);\n    }\n    getById(id) {\n      return this.http.get(`${this.API_URL}/${id}`);\n    }\n    getByIdWithProjects(id) {\n      // Mock data for development - replace with real API call when backend is ready\n      return new Observable(observer => {\n        setTimeout(() => {\n          const mockFreelance = {\n            id: id,\n            firstName: 'John',\n            lastName: 'Doe',\n            email: '<EMAIL>',\n            phone: '+33 6 12 34 56 78',\n            address: '123 Rue de la Paix, 75001 Paris',\n            status: 'AVAILABLE',\n            totalProjects: 3,\n            averageDailyRate: 633\n          };\n          observer.next(mockFreelance);\n          observer.complete();\n        }, 500);\n      });\n    }\n    getByEmail(email) {\n      const params = new HttpParams().set('email', email);\n      return this.http.get(`${this.API_URL}/by-email`, {\n        params\n      });\n    }\n    create(freelance) {\n      return this.http.post(this.API_URL, freelance);\n    }\n    update(id, freelance) {\n      return this.http.put(`${this.API_URL}/${id}`, freelance);\n    }\n    delete(id) {\n      return this.http.delete(`${this.API_URL}/${id}`);\n    }\n    checkEmailExists(email) {\n      const params = new HttpParams().set('email', email);\n      return this.http.get(`${this.API_URL}/exists`, {\n        params\n      });\n    }\n    updatePassword(id, newPassword) {\n      return this.http.patch(`${this.API_URL}/${id}/password`, newPassword);\n    }\n    static {\n      this.ɵfac = function FreelanceService_Factory(t) {\n        return new (t || FreelanceService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FreelanceService,\n        factory: FreelanceService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FreelanceService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}