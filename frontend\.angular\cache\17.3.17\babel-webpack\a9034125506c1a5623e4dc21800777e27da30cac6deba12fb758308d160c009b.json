{"ast": null, "code": "import { authGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'profile',\n  loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'projects',\n  loadComponent: () => import('./components/projects/project-list/project-list.component').then(m => m.ProjectListComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'clients',\n  loadChildren: () => import('./components/clients/clients.routes').then(m => m.clientRoutes),\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "LoginComponent", "RegisterComponent", "DashboardComponent", "canActivate", "ProfileComponent", "ProjectListComponent", "loadChildren", "clientRoutes"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { authGuard } from './guards/auth.guard';\n\nexport const routes: Routes = [\n  { path: '', redirectTo: '/login', pathMatch: 'full' },\n  {\n    path: 'login',\n    loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n  },\n  {\n    path: 'register',\n    loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n  },\n  {\n    path: 'dashboard',\n    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n    canActivate: [authGuard]\n  },\n  {\n    path: 'profile',\n    loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),\n    canActivate: [authGuard]\n  },\n  {\n    path: 'projects',\n    loadComponent: () => import('./components/projects/project-list/project-list.component').then(m => m.ProjectListComponent),\n    canActivate: [authGuard]\n  },\n  {\n    path: 'clients',\n    loadChildren: () => import('./components/clients/clients.routes').then(m => m.clientRoutes),\n    canActivate: [authGuard]\n  },\n  {\n    path: '**',\n    redirectTo: '/login'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,qBAAqB;AAE/C,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE;AAAM,CAAE,EACrD;EACEF,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;CAClG,EACD;EACEN,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CAC3G,EACD;EACEP,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,kBAAkB,CAAC;EACzGC,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB,CAAC;EACnGD,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2DAA2D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,oBAAoB,CAAC;EAC1HF,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfY,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACR,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,YAAY,CAAC;EAC3FJ,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}