{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snackbar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nlet ClientListComponent = class ClientListComponent {\n  constructor(clientService, dialog, snackBar) {\n    this.clientService = clientService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.clients = [];\n    this.filteredClients = [];\n    this.displayedColumns = ['name', 'contactPerson', 'email', 'phone', 'industry', 'status', 'actions'];\n    this.searchQuery = '';\n    this.statusFilter = 'ALL';\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n    this.searchSubject = new Subject();\n    // Setup search debouncing\n    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(query => {\n      this.performSearch(query);\n    });\n  }\n  ngOnInit() {\n    this.loadClients();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadClients() {\n    this.isLoading = true;\n    this.clientService.getClients().pipe(takeUntil(this.destroy$)).subscribe({\n      next: clients => {\n        this.clients = clients;\n        this.applyFilters();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading clients:', error);\n        this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onSearchChange(query) {\n    this.searchQuery = query;\n    this.searchSubject.next(query);\n  }\n  onStatusFilterChange() {\n    this.applyFilters();\n  }\n  performSearch(query) {\n    if (query.trim()) {\n      this.clientService.searchClients(query).pipe(takeUntil(this.destroy$)).subscribe(clients => {\n        this.filteredClients = this.filterByStatus(clients);\n      });\n    } else {\n      this.applyFilters();\n    }\n  }\n  applyFilters() {\n    this.filteredClients = this.filterByStatus(this.clients);\n  }\n  filterByStatus(clients) {\n    if (this.statusFilter === 'ALL') {\n      return clients;\n    }\n    return clients.filter(client => client.status === this.statusFilter);\n  }\n  deleteClient(client) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.name}\" ?`)) {\n      this.clientService.deleteClient(client.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.loadClients();\n        },\n        error: error => {\n          console.error('Error deleting client:', error);\n          this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n  getStatusLabel(status) {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n};\nClientListComponent = __decorate([Component({\n  selector: 'app-client-list',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule, MatTableModule, MatButtonModule, MatIconModule, MatCardModule, MatChipsModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule],\n  templateUrl: './client-list.component.html',\n  styleUrls: ['./client-list.component.scss']\n})], ClientListComponent);\nexport { ClientListComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "MatTableModule", "MatButtonModule", "MatIconModule", "MatCardModule", "MatChipsModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "ClientListComponent", "constructor", "clientService", "dialog", "snackBar", "clients", "filteredClients", "displayedColumns", "searchQuery", "statusFilter", "isLoading", "destroy$", "searchSubject", "pipe", "subscribe", "query", "performSearch", "ngOnInit", "loadClients", "ngOnDestroy", "next", "complete", "getClients", "applyFilters", "error", "console", "open", "duration", "onSearchChange", "onStatusFilterChange", "trim", "searchClients", "filterByStatus", "filter", "client", "status", "deleteClient", "confirm", "name", "id", "getStatusColor", "getStatusLabel", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\clients\\client-list\\client-list.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snackbar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\n\nimport { ClientService, ClientDto } from '../../../services/client.service';\n\n@Component({\n  selector: 'app-client-list',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    FormsModule,\n    MatTableModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatChipsModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './client-list.component.html',\n  styleUrls: ['./client-list.component.scss']\n})\nexport class ClientListComponent implements OnInit, OnDestroy {\n  clients: ClientDto[] = [];\n  filteredClients: ClientDto[] = [];\n  displayedColumns: string[] = ['name', 'contactPerson', 'email', 'phone', 'industry', 'status', 'actions'];\n  \n  searchQuery = '';\n  statusFilter = 'ALL';\n  isLoading = false;\n  \n  private destroy$ = new Subject<void>();\n  private searchSubject = new Subject<string>();\n\n  constructor(\n    private clientService: ClientService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {\n    // Setup search debouncing\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      takeUntil(this.destroy$)\n    ).subscribe(query => {\n      this.performSearch(query);\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadClients();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadClients(): void {\n    this.isLoading = true;\n    this.clientService.getClients()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (clients) => {\n          this.clients = clients;\n          this.applyFilters();\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading clients:', error);\n          this.snackBar.open('Erreur lors du chargement des clients', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onSearchChange(query: string): void {\n    this.searchQuery = query;\n    this.searchSubject.next(query);\n  }\n\n  onStatusFilterChange(): void {\n    this.applyFilters();\n  }\n\n  private performSearch(query: string): void {\n    if (query.trim()) {\n      this.clientService.searchClients(query)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(clients => {\n          this.filteredClients = this.filterByStatus(clients);\n        });\n    } else {\n      this.applyFilters();\n    }\n  }\n\n  private applyFilters(): void {\n    this.filteredClients = this.filterByStatus(this.clients);\n  }\n\n  private filterByStatus(clients: ClientDto[]): ClientDto[] {\n    if (this.statusFilter === 'ALL') {\n      return clients;\n    }\n    return clients.filter(client => client.status === this.statusFilter);\n  }\n\n  deleteClient(client: ClientDto): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.name}\" ?`)) {\n      this.clientService.deleteClient(client.id)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Client supprimé avec succès', 'Fermer', {\n              duration: 3000\n            });\n            this.loadClients();\n          },\n          error: (error) => {\n            console.error('Error deleting client:', error);\n            this.snackBar.open('Erreur lors de la suppression du client', 'Fermer', {\n              duration: 3000\n            });\n          }\n        });\n    }\n  }\n\n  getStatusColor(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'primary';\n      case 'INACTIVE':\n        return 'warn';\n      case 'PROSPECT':\n        return 'accent';\n      default:\n        return '';\n    }\n  }\n\n  getStatusLabel(status: string): string {\n    switch (status) {\n      case 'ACTIVE':\n        return 'Actif';\n      case 'INACTIVE':\n        return 'Inactif';\n      case 'PROSPECT':\n        return 'Prospect';\n      default:\n        return status;\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,iBAAiB,QAAqB,4BAA4B;AAC3E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;AA0BtE,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAY9BC,YACUC,aAA4B,EAC5BC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,OAAO,GAAgB,EAAE;IACzB,KAAAC,eAAe,GAAgB,EAAE;IACjC,KAAAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC;IAEzG,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,SAAS,GAAG,KAAK;IAET,KAAAC,QAAQ,GAAG,IAAIf,OAAO,EAAQ;IAC9B,KAAAgB,aAAa,GAAG,IAAIhB,OAAO,EAAU;IAO3C;IACA,IAAI,CAACgB,aAAa,CAACC,IAAI,CACrBf,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CACzB,CAACG,SAAS,CAACC,KAAK,IAAG;MAClB,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEAH,WAAWA,CAAA;IACT,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,aAAa,CAACoB,UAAU,EAAE,CAC5BT,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAC;MACTM,IAAI,EAAGf,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACkB,YAAY,EAAE;QACnB,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACpB,QAAQ,CAACsB,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;UACpEC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACjB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAkB,cAAcA,CAACb,KAAa;IAC1B,IAAI,CAACP,WAAW,GAAGO,KAAK;IACxB,IAAI,CAACH,aAAa,CAACQ,IAAI,CAACL,KAAK,CAAC;EAChC;EAEAc,oBAAoBA,CAAA;IAClB,IAAI,CAACN,YAAY,EAAE;EACrB;EAEQP,aAAaA,CAACD,KAAa;IACjC,IAAIA,KAAK,CAACe,IAAI,EAAE,EAAE;MAChB,IAAI,CAAC5B,aAAa,CAAC6B,aAAa,CAAChB,KAAK,CAAC,CACpCF,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACT,OAAO,IAAG;QACnB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC0B,cAAc,CAAC3B,OAAO,CAAC;MACrD,CAAC,CAAC;KACL,MAAM;MACL,IAAI,CAACkB,YAAY,EAAE;;EAEvB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACjB,eAAe,GAAG,IAAI,CAAC0B,cAAc,CAAC,IAAI,CAAC3B,OAAO,CAAC;EAC1D;EAEQ2B,cAAcA,CAAC3B,OAAoB;IACzC,IAAI,IAAI,CAACI,YAAY,KAAK,KAAK,EAAE;MAC/B,OAAOJ,OAAO;;IAEhB,OAAOA,OAAO,CAAC4B,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,IAAI,CAAC1B,YAAY,CAAC;EACtE;EAEA2B,YAAYA,CAACF,MAAiB;IAC5B,IAAIG,OAAO,CAAC,iDAAiDH,MAAM,CAACI,IAAI,KAAK,CAAC,EAAE;MAC9E,IAAI,CAACpC,aAAa,CAACkC,YAAY,CAACF,MAAM,CAACK,EAAE,CAAC,CACvC1B,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAC;QACTM,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAChB,QAAQ,CAACsB,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;YAC1DC,QAAQ,EAAE;WACX,CAAC;UACF,IAAI,CAACT,WAAW,EAAE;QACpB,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACpB,QAAQ,CAACsB,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;YACtEC,QAAQ,EAAE;WACX,CAAC;QACJ;OACD,CAAC;;EAER;EAEAa,cAAcA,CAACL,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,UAAU;QACb,OAAO,QAAQ;MACjB;QACE,OAAO,EAAE;;EAEf;EAEAM,cAAcA,CAACN,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB;QACE,OAAOA,MAAM;;EAEnB;CACD;AAtIYnC,mBAAmB,GAAA0C,UAAA,EAtB/B7D,SAAS,CAAC;EACT8D,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP/D,YAAY,EACZC,YAAY,EACZY,WAAW,EACXX,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,CACzB;EACDoD,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,8BAA8B;CAC3C,CAAC,C,EACW/C,mBAAmB,CAsI/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}