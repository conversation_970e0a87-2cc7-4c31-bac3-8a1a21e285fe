{"ast": null, "code": "import * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, InjectionToken, inject, Injectable, SkipSelf, Directive, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { MatCommonModule } from '@angular/material/core';\nimport { trigger, state, style, transition, group, animate, query, animateChild } from '@angular/animations';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nfunction MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nclass MatDialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Custom class for the overlay pane. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Custom class for the backdrop. */\n    this.backdropClass = '';\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Aria label to assign to the dialog element. */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n    this.restoreFocus = true;\n    /** Whether to wait for the opening animation to finish before trapping focus. */\n    this.delayFocusTrap = true;\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.closeOnNavigation = true;\n    // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n  }\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nlet MatDialogContainer = /*#__PURE__*/(() => {\n  class MatDialogContainer extends CdkDialogContainer {\n    constructor(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, _animationMode, focusMonitor) {\n      super(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor);\n      this._animationMode = _animationMode;\n      /** Emits when an animation state changes. */\n      this._animationStateChanged = new EventEmitter();\n      /** Whether animations are enabled. */\n      this._animationsEnabled = this._animationMode !== 'NoopAnimations';\n      /** Number of actions projected in the dialog. */\n      this._actionSectionCount = 0;\n      /** Host element of the dialog container component. */\n      this._hostElement = this._elementRef.nativeElement;\n      /** Duration of the dialog open animation. */\n      this._enterAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION : 0;\n      /** Duration of the dialog close animation. */\n      this._exitAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION : 0;\n      /** Current timer for dialog animations. */\n      this._animationTimer = null;\n      /**\n       * Completes the dialog open by clearing potential animation classes, trapping\n       * focus and emitting an opened event.\n       */\n      this._finishDialogOpen = () => {\n        this._clearAnimationClasses();\n        this._openAnimationDone(this._enterAnimationDuration);\n      };\n      /**\n       * Completes the dialog close by clearing potential animation classes, restoring\n       * focus and emitting a closed event.\n       */\n      this._finishDialogClose = () => {\n        this._clearAnimationClasses();\n        this._animationStateChanged.emit({\n          state: 'closed',\n          totalTime: this._exitAnimationDuration\n        });\n      };\n    }\n    _contentAttached() {\n      // Delegate to the original dialog-container initialization (i.e. saving the\n      // previous element, setting up the focus trap and moving focus to the container).\n      super._contentAttached();\n      // Note: Usually we would be able to use the MDC dialog foundation here to handle\n      // the dialog animation for us, but there are a few reasons why we just leverage\n      // their styles and not use the runtime foundation code:\n      //   1. Foundation does not allow us to disable animations.\n      //   2. Foundation contains unnecessary features we don't need and aren't\n      //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n      //   3. Foundation uses unnecessary timers for animations to work around limitations\n      //      in React's `setState` mechanism.\n      //      https://github.com/material-components/material-components-web/pull/3682.\n      this._startOpenAnimation();\n    }\n    /** Starts the dialog open animation if enabled. */\n    _startOpenAnimation() {\n      this._animationStateChanged.emit({\n        state: 'opening',\n        totalTime: this._enterAnimationDuration\n      });\n      if (this._animationsEnabled) {\n        this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n        // We need to give the `setProperty` call from above some time to be applied.\n        // One would expect that the open class is added once the animation finished, but MDC\n        // uses the open class in combination with the opening class to start the animation.\n        this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n        this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n      } else {\n        this._hostElement.classList.add(OPEN_CLASS);\n        // Note: We could immediately finish the dialog opening here with noop animations,\n        // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n        // Executing this immediately would mean that `afterOpened` emits synchronously\n        // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n        Promise.resolve().then(() => this._finishDialogOpen());\n      }\n    }\n    /**\n     * Starts the exit animation of the dialog if enabled. This method is\n     * called by the dialog ref.\n     */\n    _startExitAnimation() {\n      this._animationStateChanged.emit({\n        state: 'closing',\n        totalTime: this._exitAnimationDuration\n      });\n      this._hostElement.classList.remove(OPEN_CLASS);\n      if (this._animationsEnabled) {\n        this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n        // We need to give the `setProperty` call from above some time to be applied.\n        this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n        this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n      } else {\n        // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n        // set up before any user can subscribe to the backdrop click. The subscription triggers\n        // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n        // animation state event if animations are disabled, the overlay would be disposed\n        // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n        // skipped. We work around this by waiting with the dialog close until the next tick when\n        // all subscriptions have been fired as expected. This is not an ideal solution, but\n        // there doesn't seem to be any other good way. Alternatives that have been considered:\n        //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n        //      Also this issue is specific to the MDC implementation where the dialog could\n        //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n        //      and closing always takes at least a tick.\n        //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n        //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n        //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n        // Based on the fact that this is specific to the MDC-based implementation of the dialog\n        // animations, the defer is applied here.\n        Promise.resolve().then(() => this._finishDialogClose());\n      }\n    }\n    /**\n     * Updates the number action sections.\n     * @param delta Increase/decrease in the number of sections.\n     */\n    _updateActionSectionCount(delta) {\n      this._actionSectionCount += delta;\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Clears all dialog animation classes. */\n    _clearAnimationClasses() {\n      this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n    }\n    _waitForAnimationToComplete(duration, callback) {\n      if (this._animationTimer !== null) {\n        clearTimeout(this._animationTimer);\n      }\n      // Note that we want this timer to run inside the NgZone, because we want\n      // the related events like `afterClosed` to be inside the zone as well.\n      this._animationTimer = setTimeout(callback, duration);\n    }\n    /** Runs a callback in `requestAnimationFrame`, if available. */\n    _requestAnimationFrame(callback) {\n      this._ngZone.runOutsideAngular(() => {\n        if (typeof requestAnimationFrame === 'function') {\n          requestAnimationFrame(callback);\n        } else {\n          callback();\n        }\n      });\n    }\n    _captureInitialFocus() {\n      if (!this._config.delayFocusTrap) {\n        this._trapFocus();\n      }\n    }\n    /**\n     * Callback for when the open dialog animation has finished. Intended to\n     * be called by sub-classes that use different animation implementations.\n     */\n    _openAnimationDone(totalTime) {\n      if (this._config.delayFocusTrap) {\n        this._trapFocus();\n      }\n      this._animationStateChanged.next({\n        state: 'opened',\n        totalTime\n      });\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      if (this._animationTimer !== null) {\n        clearTimeout(this._animationTimer);\n      }\n    }\n    attachComponentPortal(portal) {\n      // When a component is passed into the dialog, the host element interrupts\n      // the `display:flex` from affecting the dialog title, content, and\n      // actions. To fix this, we make the component host `display: contents` by\n      // marking its host with the `mat-mdc-dialog-component-host` class.\n      //\n      // Note that this problem does not exist when a template ref is used since\n      // the title, contents, and actions are then nested directly under the\n      // dialog surface.\n      const ref = super.attachComponentPortal(portal);\n      ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n      return ref;\n    }\n    static {\n      this.ɵfac = function MatDialogContainer_Factory(t) {\n        return new (t || MatDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MatDialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatDialogContainer,\n        selectors: [[\"mat-dialog-container\"]],\n        hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n        hostVars: 10,\n        hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"id\", ctx._config.id);\n            i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled)(\"mat-mdc-dialog-container-with-actions\", ctx._actionSectionCount > 0);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 0,\n        consts: [[1, \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n        template: function MatDialogContainer_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵtemplate(2, MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n            i0.ɵɵelementEnd()();\n          }\n        },\n        dependencies: [CdkPortalOutlet],\n        styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0;transform:scale(0.8)}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--closing .mdc-dialog__surface{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{opacity:1}.mdc-dialog--open .mdc-dialog__surface{transform:none}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{width:100%;height:100%}.mat-mdc-dialog-component-host{display:contents}.mat-mdc-dialog-container{--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition:opacity linear var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container .mdc-dialog__surface{transition:transform var(--mat-dialog-transition-duration, 0ms) 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container,.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__surface{transition:none}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-title{padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return MatDialogContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\nvar MatDialogState = /*#__PURE__*/function (MatDialogState) {\n  MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n  MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n  MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n  return MatDialogState;\n}(MatDialogState || {});\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  constructor(_ref, config, _containerInstance) {\n    this._ref = _ref;\n    this._containerInstance = _containerInstance;\n    /** Subject for notifying the user that the dialog has finished opening. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the dialog has started closing. */\n    this._beforeClosed = new Subject();\n    /** Current state of the dialog. */\n    this._state = MatDialogState.OPEN;\n    this.disableClose = config.disableClose;\n    this.id = _ref.id;\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = /*#__PURE__*/new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n// Counter for unique dialog ids.\nlet uniqueId = 0;\n/**\n * Service to open Material Design modal dialogs.\n */\nlet MatDialog = /*#__PURE__*/(() => {\n  class MatDialog {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n      return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n      return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    _getAfterAllClosed() {\n      const parent = this._parentDialog;\n      return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    constructor(_overlay, injector,\n    /**\n     * @deprecated `_location` parameter to be removed.\n     * @breaking-change 10.0.0\n     */\n    location, _defaultOptions, _scrollStrategy, _parentDialog,\n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 15.0.0\n     */\n    _overlayContainer,\n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _animationMode) {\n      this._overlay = _overlay;\n      this._defaultOptions = _defaultOptions;\n      this._scrollStrategy = _scrollStrategy;\n      this._parentDialog = _parentDialog;\n      this._openDialogsAtThisLevel = [];\n      this._afterAllClosedAtThisLevel = new Subject();\n      this._afterOpenedAtThisLevel = new Subject();\n      this.dialogConfigClass = MatDialogConfig;\n      /**\n       * Stream that emits when all open dialog have finished closing.\n       * Will emit on subscribe if there are no open dialogs to begin with.\n       */\n      this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n      this._dialog = injector.get(Dialog);\n      this._dialogRefConstructor = MatDialogRef;\n      this._dialogContainerType = MatDialogContainer;\n      this._dialogDataToken = MAT_DIALOG_DATA;\n    }\n    open(componentOrTemplateRef, config) {\n      let dialogRef;\n      config = {\n        ...(this._defaultOptions || new MatDialogConfig()),\n        ...config\n      };\n      config.id = config.id || `mat-mdc-dialog-${uniqueId++}`;\n      config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n      const cdkRef = this._dialog.open(componentOrTemplateRef, {\n        ...config,\n        positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n        // Disable closing since we need to sync it up to the animation ourselves.\n        disableClose: true,\n        // Disable closing on destroy, because this service cleans up its open dialogs as well.\n        // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n        // the dialogs immediately whereas we want it to wait for the animations to finish.\n        closeOnDestroy: false,\n        // Disable closing on detachments so that we can sync up the animation.\n        // The Material dialog ref handles this manually.\n        closeOnOverlayDetachments: false,\n        container: {\n          type: this._dialogContainerType,\n          providers: () => [\n          // Provide our config as the CDK config as well since it has the same interface as the\n          // CDK one, but it contains the actual values passed in by the user for things like\n          // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n          {\n            provide: this.dialogConfigClass,\n            useValue: config\n          }, {\n            provide: DialogConfig,\n            useValue: config\n          }]\n        },\n        templateContext: () => ({\n          dialogRef\n        }),\n        providers: (ref, cdkConfig, dialogContainer) => {\n          dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n          dialogRef.updatePosition(config?.position);\n          return [{\n            provide: this._dialogContainerType,\n            useValue: dialogContainer\n          }, {\n            provide: this._dialogDataToken,\n            useValue: cdkConfig.data\n          }, {\n            provide: this._dialogRefConstructor,\n            useValue: dialogRef\n          }];\n        }\n      });\n      // This can't be assigned in the `providers` callback, because\n      // the instance hasn't been assigned to the CDK ref yet.\n      dialogRef.componentRef = cdkRef.componentRef;\n      dialogRef.componentInstance = cdkRef.componentInstance;\n      this.openDialogs.push(dialogRef);\n      this.afterOpened.next(dialogRef);\n      dialogRef.afterClosed().subscribe(() => {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n          this.openDialogs.splice(index, 1);\n          if (!this.openDialogs.length) {\n            this._getAfterAllClosed().next();\n          }\n        }\n      });\n      return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n      this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n      return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n      // Only close the dialogs at this level on destroy\n      // since the parent service may still be active.\n      this._closeDialogs(this._openDialogsAtThisLevel);\n      this._afterAllClosedAtThisLevel.complete();\n      this._afterOpenedAtThisLevel.complete();\n    }\n    _closeDialogs(dialogs) {\n      let i = dialogs.length;\n      while (i--) {\n        dialogs[i].close();\n      }\n    }\n    static {\n      this.ɵfac = function MatDialog_Factory(t) {\n        return new (t || MatDialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.Location, 8), i0.ɵɵinject(MAT_DIALOG_DEFAULT_OPTIONS, 8), i0.ɵɵinject(MAT_DIALOG_SCROLL_STRATEGY), i0.ɵɵinject(MatDialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: MatDialog,\n        factory: MatDialog.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MatDialog;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Counter used to generate unique IDs for dialog elements. */\nlet dialogElementUid = 0;\n/**\n * Button that will close the current dialog.\n */\nlet MatDialogClose = /*#__PURE__*/(() => {\n  class MatDialogClose {\n    constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    dialogRef, _elementRef, _dialog) {\n      this.dialogRef = dialogRef;\n      this._elementRef = _elementRef;\n      this._dialog = _dialog;\n      /** Default to \"button\" to prevents accidental form submits. */\n      this.type = 'button';\n    }\n    ngOnInit() {\n      if (!this.dialogRef) {\n        // When this directive is included in a dialog via TemplateRef (rather than being\n        // in a Component), the DialogRef isn't available via injection because embedded\n        // views cannot be given a custom injector. Instead, we look up the DialogRef by\n        // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n        // be resolved at constructor time.\n        this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n      }\n    }\n    ngOnChanges(changes) {\n      const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n      if (proxiedChange) {\n        this.dialogResult = proxiedChange.currentValue;\n      }\n    }\n    _onButtonClick(event) {\n      // Determinate the focus origin using the click event, because using the FocusMonitor will\n      // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n      // dialog, and therefore clicking the button won't result in a focus change. This means that\n      // the FocusMonitor won't detect any origin change, and will always output `program`.\n      _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n    static {\n      this.ɵfac = function MatDialogClose_Factory(t) {\n        return new (t || MatDialogClose)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatDialogClose,\n        selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n        hostVars: 2,\n        hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n              return ctx._onButtonClick($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n          }\n        },\n        inputs: {\n          ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n          type: \"type\",\n          dialogResult: [i0.ɵɵInputFlags.None, \"mat-dialog-close\", \"dialogResult\"],\n          _matDialogClose: [i0.ɵɵInputFlags.None, \"matDialogClose\", \"_matDialogClose\"]\n        },\n        exportAs: [\"matDialogClose\"],\n        standalone: true,\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return MatDialogClose;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatDialogLayoutSection = /*#__PURE__*/(() => {\n  class MatDialogLayoutSection {\n    constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    _dialogRef, _elementRef, _dialog) {\n      this._dialogRef = _dialogRef;\n      this._elementRef = _elementRef;\n      this._dialog = _dialog;\n    }\n    ngOnInit() {\n      if (!this._dialogRef) {\n        this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n      }\n      if (this._dialogRef) {\n        Promise.resolve().then(() => {\n          this._onAdd();\n        });\n      }\n    }\n    ngOnDestroy() {\n      // Note: we null check because there are some internal\n      // tests that are mocking out `MatDialogRef` incorrectly.\n      const instance = this._dialogRef?._containerInstance;\n      if (instance) {\n        Promise.resolve().then(() => {\n          this._onRemove();\n        });\n      }\n    }\n    static {\n      this.ɵfac = function MatDialogLayoutSection_Factory(t) {\n        return new (t || MatDialogLayoutSection)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatDialogLayoutSection,\n        standalone: true\n      });\n    }\n  }\n  return MatDialogLayoutSection;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nlet MatDialogTitle = /*#__PURE__*/(() => {\n  class MatDialogTitle extends MatDialogLayoutSection {\n    constructor() {\n      super(...arguments);\n      this.id = `mat-mdc-dialog-title-${dialogElementUid++}`;\n    }\n    _onAdd() {\n      // Note: we null check the queue, because there are some internal\n      // tests that are mocking out `MatDialogRef` incorrectly.\n      this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n    }\n    _onRemove() {\n      this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵMatDialogTitle_BaseFactory;\n        return function MatDialogTitle_Factory(t) {\n          return (ɵMatDialogTitle_BaseFactory || (ɵMatDialogTitle_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogTitle)))(t || MatDialogTitle);\n        };\n      })();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatDialogTitle,\n        selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n        hostVars: 1,\n        hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"id\", ctx.id);\n          }\n        },\n        inputs: {\n          id: \"id\"\n        },\n        exportAs: [\"matDialogTitle\"],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatDialogTitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Scrollable content container of a dialog.\n */\nlet MatDialogContent = /*#__PURE__*/(() => {\n  class MatDialogContent {\n    static {\n      this.ɵfac = function MatDialogContent_Factory(t) {\n        return new (t || MatDialogContent)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatDialogContent,\n        selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"],\n        standalone: true\n      });\n    }\n  }\n  return MatDialogContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nlet MatDialogActions = /*#__PURE__*/(() => {\n  class MatDialogActions extends MatDialogLayoutSection {\n    _onAdd() {\n      this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n    }\n    _onRemove() {\n      this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵMatDialogActions_BaseFactory;\n        return function MatDialogActions_Factory(t) {\n          return (ɵMatDialogActions_BaseFactory || (ɵMatDialogActions_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogActions)))(t || MatDialogActions);\n        };\n      })();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatDialogActions,\n        selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n        hostVars: 6,\n        hostBindings: function MatDialogActions_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-start\", ctx.align === \"start\")(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n          }\n        },\n        inputs: {\n          align: \"align\"\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatDialogActions;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nconst DIRECTIVES = [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent];\nlet MatDialogModule = /*#__PURE__*/(() => {\n  class MatDialogModule {\n    static {\n      this.ɵfac = function MatDialogModule_Factory(t) {\n        return new (t || MatDialogModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatDialogModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [MatDialog],\n        imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatDialogModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n */\nconst _defaultParams = {\n  params: {\n    enterAnimationDuration: '150ms',\n    exitAnimationDuration: '75ms'\n  }\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n */\nconst matDialogAnimations = {\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: /*#__PURE__*/trigger('dialogContainer', [\n  /*#__PURE__*/\n  // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  // specifying the transform explicitly, causes IE both to blur the dialog content and\n  // decimate the animation performance. Leaving it as `none` solves both issues.\n  state('void, exit', /*#__PURE__*/style({\n    opacity: 0,\n    transform: 'scale(0.7)'\n  })), /*#__PURE__*/state('enter', /*#__PURE__*/style({\n    transform: 'none'\n  })), /*#__PURE__*/transition('* => enter', /*#__PURE__*/group([/*#__PURE__*/animate('{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)', /*#__PURE__*/style({\n    transform: 'none',\n    opacity: 1\n  })), /*#__PURE__*/query('@*', /*#__PURE__*/animateChild(), {\n    optional: true\n  })]), _defaultParams), /*#__PURE__*/transition('* => void, * => exit', /*#__PURE__*/group([/*#__PURE__*/animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', /*#__PURE__*/style({\n    opacity: 0\n  })), /*#__PURE__*/query('@*', /*#__PURE__*/animateChild(), {\n    optional: true\n  })]), _defaultParams)])\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogState, MatDialogTitle, _closeDialogVia, _defaultParams, matDialogAnimations };\n//# sourceMappingURL=dialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}