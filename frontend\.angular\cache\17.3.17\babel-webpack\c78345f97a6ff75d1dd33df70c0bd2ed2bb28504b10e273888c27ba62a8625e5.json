{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/card\";\nimport * as i2 from \"@angular/material/icon\";\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 10,\n        vars: 0,\n        consts: [[1, \"profile-container\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6, \" Profil utilisateur \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\");\n            i0.ɵɵtext(9, \"Cette fonctionnalit\\u00E9 sera bient\\u00F4t disponible.\");\n            i0.ɵɵelementEnd()()()();\n          }\n        },\n        dependencies: [CommonModule, MatCardModule, i1.MatCard, i1.MatCardContent, i1.MatCardHeader, i1.MatCardTitle, MatButtonModule, MatIconModule, i2.MatIcon],\n        styles: [\".profile-container[_ngcontent-%COMP%]{padding:24px;max-width:800px;margin:0 auto}mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}