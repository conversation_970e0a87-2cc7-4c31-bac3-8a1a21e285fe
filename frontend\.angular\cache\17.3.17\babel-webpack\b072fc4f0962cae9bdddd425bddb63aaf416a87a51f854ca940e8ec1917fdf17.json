{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/project.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/divider\";\nfunction ProjectDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails du projet...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_divider_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Jours/an \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.daysPerYear, \" jours \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_divider_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Dur\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.durationInMonths, \" mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_divider_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Renouvellement \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Tous les \", ctx_r1.project.orderRenewalInMonths, \" mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" T\\u00E9l\\u00E9travail \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.remoteDaysPerMonth, \" jours/mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_mat_divider_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Sur site \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.onsiteDaysPerMonth, \" jours/mois \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Mode de travail \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, ProjectDetailComponent_div_2_mat_card_100_div_7_Template, 7, 1, \"div\", 18)(8, ProjectDetailComponent_div_2_mat_card_100_mat_divider_8_Template, 1, 0, \"mat-divider\", 17)(9, ProjectDetailComponent_div_2_mat_card_100_div_9_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.remoteDaysPerMonth);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.remoteDaysPerMonth && ctx_r1.project.onsiteDaysPerMonth);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.onsiteDaysPerMonth);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_101_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"span\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 25);\n    i0.ɵɵtext(4, \"\\u00C9chou\\u00E9es\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.failedSteps);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Progression \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 21)(8, \"div\", 22)(9, \"div\", 23)(10, \"span\", 24);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 25);\n    i0.ɵɵtext(13, \"Termin\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 23)(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 25);\n    i0.ɵɵtext(18, \"En cours\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ProjectDetailComponent_div_2_mat_card_101_div_19_Template, 5, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 27);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.project.completedSteps || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.project.totalSteps || 0) - (ctx_r1.project.completedSteps || 0) - (ctx_r1.project.failedSteps || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.failedSteps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getProgressPercentage(), \"% compl\\u00E9t\\u00E9 \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_102_mat_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const star_r3 = ctx.$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r3 === \"star\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", star_r3, \" \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00C9valuation personnelle \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 28);\n    i0.ɵɵtemplate(8, ProjectDetailComponent_div_2_mat_card_102_mat_icon_8_Template, 2, 3, \"mat-icon\", 29);\n    i0.ɵɵelementStart(9, \"span\", 30);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getRatingStars(ctx_r1.project.personalRating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.project.personalRating, \"/5\");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const document_r4 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(document_r4);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_2_mat_card_103_div_7_div_1_Template, 5, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.project.documents);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_mat_divider_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Lien projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16)(6, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_mat_card_103_div_9_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.openLink());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"open_in_new\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"href\", ctx_r1.project.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.link, \" \");\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"folder\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Documents et liens \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, ProjectDetailComponent_div_2_mat_card_103_div_7_Template, 2, 1, \"div\", 31)(8, ProjectDetailComponent_div_2_mat_card_103_mat_divider_8_Template, 1, 0, \"mat-divider\", 17)(9, ProjectDetailComponent_div_2_mat_card_103_div_9_Template, 10, 2, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.documents == null ? null : ctx_r1.project.documents.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.project.documents == null ? null : ctx_r1.project.documents.length) && ctx_r1.project.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.link);\n  }\n}\nfunction ProjectDetailComponent_div_2_mat_card_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Notes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 37);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.notes, \" \");\n  }\n}\nfunction ProjectDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-card\", 5)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"div\", 6)(5, \"div\", 7)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-subtitle\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-card-actions\")(14, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBack());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onEdit());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_2_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDelete());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 12)(27, \"mat-card\", 13)(28, \"mat-card-header\")(29, \"mat-card-title\")(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Informations du projet \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"mat-card-content\")(34, \"div\", 14)(35, \"div\", 15)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 16);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(41, \"mat-divider\");\n    i0.ɵɵelementStart(42, \"div\", 14)(43, \"div\", 15)(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" Stack technique \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 16);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(49, \"mat-divider\");\n    i0.ɵɵelementStart(50, \"div\", 14)(51, \"div\", 15)(52, \"mat-icon\");\n    i0.ɵɵtext(53, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(54, \" Avantages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 16);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"mat-card\", 13)(58, \"mat-card-header\")(59, \"mat-card-title\")(60, \"mat-icon\");\n    i0.ɵɵtext(61, \"euro\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" Informations financi\\u00E8res \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"mat-card-content\")(64, \"div\", 14)(65, \"div\", 15)(66, \"mat-icon\");\n    i0.ɵɵtext(67, \"payments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(68, \" TJM \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 16);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(71, \"mat-divider\");\n    i0.ɵɵelementStart(72, \"div\", 14)(73, \"div\", 15)(74, \"mat-icon\");\n    i0.ɵɵtext(75, \"calculate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(76, \" Revenus estim\\u00E9s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 16);\n    i0.ɵɵtext(78);\n    i0.ɵɵpipe(79, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(80, ProjectDetailComponent_div_2_mat_divider_80_Template, 1, 0, \"mat-divider\", 17)(81, ProjectDetailComponent_div_2_div_81_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"mat-card\", 13)(83, \"mat-card-header\")(84, \"mat-card-title\")(85, \"mat-icon\");\n    i0.ɵɵtext(86, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" Planning \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"mat-card-content\")(89, \"div\", 14)(90, \"div\", 15)(91, \"mat-icon\");\n    i0.ɵɵtext(92, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \" Date de d\\u00E9but \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"div\", 16);\n    i0.ɵɵtext(95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(96, ProjectDetailComponent_div_2_mat_divider_96_Template, 1, 0, \"mat-divider\", 17)(97, ProjectDetailComponent_div_2_div_97_Template, 7, 1, \"div\", 18)(98, ProjectDetailComponent_div_2_mat_divider_98_Template, 1, 0, \"mat-divider\", 17)(99, ProjectDetailComponent_div_2_div_99_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(100, ProjectDetailComponent_div_2_mat_card_100_Template, 10, 3, \"mat-card\", 19)(101, ProjectDetailComponent_div_2_mat_card_101_Template, 22, 4, \"mat-card\", 19)(102, ProjectDetailComponent_div_2_mat_card_102_Template, 11, 2, \"mat-card\", 19)(103, ProjectDetailComponent_div_2_mat_card_103_Template, 10, 3, \"mat-card\", 19)(104, ProjectDetailComponent_div_2_mat_card_104_Template, 9, 1, \"mat-card\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.role, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.getWorkModeColor(ctx_r1.project.workMode));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getWorkModeLabel(ctx_r1.project.workMode), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Client: \", ctx_r1.project.clientName, \" \\u2022 \", ctx_r1.project.dailyRate, \"\\u20AC/jour \");\n    i0.ɵɵadvance(28);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.description || \"Non sp\\u00E9cifi\\u00E9e\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.techStack || \"Non sp\\u00E9cifi\\u00E9e\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.advantages || \"Non sp\\u00E9cifi\\u00E9s\", \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.project.dailyRate, \"\\u20AC/jour \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(79, 22, ctx_r1.calculateTotalRevenue(), \"1.0-0\"), \"\\u20AC \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.daysPerYear);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.daysPerYear);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatDate(ctx_r1.project.startDate), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.durationInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.durationInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.orderRenewalInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.orderRenewalInMonths);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.workMode === \"HYBRID\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.totalSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.personalRating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.project.documents == null ? null : ctx_r1.project.documents.length) || ctx_r1.project.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project.notes);\n  }\n}\nexport class ProjectDetailComponent {\n  constructor(projectService, router, route, snackBar) {\n    this.projectService = projectService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.projectId = +params['id'];\n        this.loadProject();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadProject() {\n    if (!this.projectId) return;\n    this.isLoading = true;\n    this.projectService.getById(this.projectId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: project => {\n        if (project) {\n          this.project = project;\n        } else {\n          this.snackBar.open('Projet non trouvé', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/projects']);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading project:', error);\n        this.snackBar.open('Erreur lors du chargement du projet', 'Fermer', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onBack() {\n    this.router.navigate(['/projects']);\n  }\n  onEdit() {\n    if (this.project) {\n      this.router.navigate(['/projects', this.project.id, 'edit']);\n    }\n  }\n  onDelete() {\n    if (!this.project) return;\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le projet \"${this.project.role}\" ?`)) {\n      this.projectService.delete(this.project.id).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.snackBar.open('Projet supprimé avec succès', 'Fermer', {\n            duration: 3000\n          });\n          this.router.navigate(['/projects']);\n        },\n        error: error => {\n          console.error('Error deleting project:', error);\n          this.snackBar.open('Erreur lors de la suppression du projet', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  // Utility methods\n  getWorkModeLabel(workMode) {\n    switch (workMode) {\n      case 'REMOTE':\n        return 'Télétravail';\n      case 'ONSITE':\n        return 'Sur site';\n      case 'HYBRID':\n        return 'Hybride';\n      default:\n        return 'Non spécifié';\n    }\n  }\n  getWorkModeColor(workMode) {\n    switch (workMode) {\n      case 'REMOTE':\n        return 'primary';\n      case 'ONSITE':\n        return 'accent';\n      case 'HYBRID':\n        return 'warn';\n      default:\n        return '';\n    }\n  }\n  getRatingStars(rating) {\n    const stars = [];\n    for (let i = 1; i <= 5; i++) {\n      stars.push(i <= (rating || 0) ? 'star' : 'star_border');\n    }\n    return stars;\n  }\n  formatDate(dateString) {\n    if (!dateString) return 'Non spécifié';\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  }\n  calculateTotalRevenue() {\n    if (!this.project?.dailyRate || !this.project?.durationInMonths) return 0;\n    const daysPerMonth = (this.project.daysPerYear || 220) / 12;\n    return this.project.dailyRate * daysPerMonth * this.project.durationInMonths;\n  }\n  getProgressPercentage() {\n    if (!this.project?.totalSteps) return 0;\n    return Math.round((this.project.completedSteps || 0) / this.project.totalSteps * 100);\n  }\n  openLink() {\n    if (this.project?.link) {\n      window.open(this.project.link, '_blank');\n    }\n  }\n  static {\n    this.ɵfac = function ProjectDetailComponent_Factory(t) {\n      return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectDetailComponent,\n      selectors: [[\"app-project-detail\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"project-detail-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"project-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"project-content\"], [1, \"header-card\"], [1, \"title-row\"], [1, \"project-name\"], [\"selected\", \"\", 3, \"color\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"details-grid\"], [1, \"info-card\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [4, \"ngIf\"], [\"class\", \"info-item\", 4, \"ngIf\"], [\"class\", \"info-card\", 4, \"ngIf\"], [\"class\", \"info-card notes-card\", 4, \"ngIf\"], [1, \"progress-info\"], [1, \"progress-stats\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"progress-percentage\"], [1, \"rating-display\"], [3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [\"class\", \"documents-list\", 4, \"ngIf\"], [1, \"documents-list\"], [\"class\", \"document-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"document-item\"], [\"target\", \"_blank\", 1, \"project-link\", 3, \"click\", \"href\"], [1, \"info-card\", \"notes-card\"], [1, \"notes-content\"]],\n      template: function ProjectDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ProjectDetailComponent_div_1_Template, 4, 0, \"div\", 1)(2, ProjectDetailComponent_div_2_Template, 105, 25, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.project);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, RouterModule, MatCardModule, i5.MatCard, i5.MatCardActions, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, MatButtonModule, i6.MatButton, MatIconModule, i7.MatIcon, MatChipsModule, i8.MatChip, MatProgressSpinnerModule, i9.MatProgressSpinner, MatDividerModule, i10.MatDivider],\n      styles: [\".project-detail-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n@media (max-width: 600px) {\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .title-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 12px;\\n  }\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 0.9rem;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 16px 24px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n}\\n@media (max-width: 600px) {\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 120px;\\n  }\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n@media (max-width: 768px) {\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 1.1rem;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding-top: 16px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 12px 0;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n  min-width: 120px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: right;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%] {\\n  color: var(--mdc-theme-primary, #1976d2);\\n  text-decoration: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  gap: 4px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 0.9rem;\\n  height: 0.9rem;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n@media (max-width: 600px) {\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 4px;\\n  }\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%] {\\n    justify-content: flex-start;\\n  }\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  margin-bottom: 16px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--mdc-theme-primary, #1976d2);\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ddd;\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   mat-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .documents-list[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 0;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .documents-list[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%]   .info-card.notes-card[_ngcontent-%COMP%]   .notes-content[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-left: 4px solid var(--mdc-theme-primary, #1976d2);\\n  padding: 16px;\\n  border-radius: 4px;\\n  line-height: 1.6;\\n  white-space: pre-wrap;\\n}\\n\\n.mat-mdc-chip[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  min-height: 28px;\\n}\\n.mat-mdc-chip.mat-primary[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n.mat-mdc-chip.mat-accent[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #f57c00;\\n}\\n.mat-mdc-chip.mat-warn[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n}\\n\\n.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%] {\\n  background-color: var(--mdc-theme-primary, #1976d2);\\n  color: white;\\n}\\n\\n.mat-mdc-button.mat-warn[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.mat-mdc-button.mat-warn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(211, 47, 47, 0.04);\\n}\\n\\n@media (max-width: 768px) {\\n  .project-detail-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .project-detail-container[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]   .details-grid[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatProgressSpinnerModule", "MatDividerModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "project", "daysPerYear", "durationInMonths", "orderRenewalInMonths", "remoteDaysPerMonth", "onsiteDaysPerMonth", "ɵɵtemplate", "ProjectDetailComponent_div_2_mat_card_100_div_7_Template", "ProjectDetailComponent_div_2_mat_card_100_mat_divider_8_Template", "ProjectDetailComponent_div_2_mat_card_100_div_9_Template", "ɵɵproperty", "ɵɵtextInterpolate", "failedSteps", "ProjectDetailComponent_div_2_mat_card_101_div_19_Template", "completedSteps", "totalSteps", "getProgressPercentage", "ɵɵclassProp", "star_r3", "ProjectDetailComponent_div_2_mat_card_102_mat_icon_8_Template", "getRatingStars", "personalRating", "document_r4", "ProjectDetailComponent_div_2_mat_card_103_div_7_div_1_Template", "documents", "ɵɵlistener", "ProjectDetailComponent_div_2_mat_card_103_div_9_Template_a_click_6_listener", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "ɵɵresetView", "openLink", "link", "ɵɵsanitizeUrl", "ProjectDetailComponent_div_2_mat_card_103_div_7_Template", "ProjectDetailComponent_div_2_mat_card_103_mat_divider_8_Template", "ProjectDetailComponent_div_2_mat_card_103_div_9_Template", "length", "notes", "ProjectDetailComponent_div_2_Template_button_click_14_listener", "_r1", "onBack", "ProjectDetailComponent_div_2_Template_button_click_18_listener", "onEdit", "ProjectDetailComponent_div_2_Template_button_click_22_listener", "onDelete", "ProjectDetailComponent_div_2_mat_divider_80_Template", "ProjectDetailComponent_div_2_div_81_Template", "ProjectDetailComponent_div_2_mat_divider_96_Template", "ProjectDetailComponent_div_2_div_97_Template", "ProjectDetailComponent_div_2_mat_divider_98_Template", "ProjectDetailComponent_div_2_div_99_Template", "ProjectDetailComponent_div_2_mat_card_100_Template", "ProjectDetailComponent_div_2_mat_card_101_Template", "ProjectDetailComponent_div_2_mat_card_102_Template", "ProjectDetailComponent_div_2_mat_card_103_Template", "ProjectDetailComponent_div_2_mat_card_104_Template", "role", "getWorkModeColor", "workMode", "getWorkModeLabel", "ɵɵtextInterpolate2", "clientName", "dailyRate", "description", "techStack", "advantages", "ɵɵpipeBind2", "calculateTotalRevenue", "formatDate", "startDate", "ProjectDetailComponent", "constructor", "projectService", "router", "route", "snackBar", "isLoading", "destroy$", "ngOnInit", "params", "pipe", "subscribe", "projectId", "loadProject", "ngOnDestroy", "next", "complete", "getById", "open", "duration", "navigate", "error", "console", "id", "confirm", "delete", "rating", "stars", "i", "push", "dateString", "Date", "toLocaleDateString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "round", "window", "ɵɵdirectiveInject", "i1", "ProjectService", "i2", "Router", "ActivatedRoute", "i3", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProjectDetailComponent_Template", "rf", "ctx", "ProjectDetailComponent_div_1_Template", "ProjectDetailComponent_div_2_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i5", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i6", "MatButton", "i7", "MatIcon", "i8", "MatChip", "i9", "MatProgressSpinner", "i10", "<PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\project-detail\\project-detail.component.ts", "C:\\dev\\workspace\\indezy\\frontend\\src\\app\\components\\projects\\project-detail\\project-detail.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { ProjectService, ProjectDto } from '../../../services/project.service';\n\n@Component({\n  selector: 'app-project-detail',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatDividerModule\n  ],\n  templateUrl: './project-detail.component.html',\n  styleUrls: ['./project-detail.component.scss']\n})\nexport class ProjectDetailComponent implements OnInit, OnDestroy {\n  project?: ProjectDto;\n  isLoading = false;\n  projectId?: number;\n\n  private readonly destroy$ = new Subject<void>();\n\n  constructor(\n    private readonly projectService: ProjectService,\n    private readonly router: Router,\n    private readonly route: ActivatedRoute,\n    private readonly snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['id']) {\n        this.projectId = +params['id'];\n        this.loadProject();\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private loadProject(): void {\n    if (!this.projectId) return;\n    \n    this.isLoading = true;\n    this.projectService.getById(this.projectId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (project) => {\n          if (project) {\n            this.project = project;\n          } else {\n            this.snackBar.open('Projet non trouvé', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/projects']);\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading project:', error);\n          this.snackBar.open('Erreur lors du chargement du projet', 'Fermer', { duration: 3000 });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  onBack(): void {\n    this.router.navigate(['/projects']);\n  }\n\n  onEdit(): void {\n    if (this.project) {\n      this.router.navigate(['/projects', this.project.id, 'edit']);\n    }\n  }\n\n  onDelete(): void {\n    if (!this.project) return;\n    \n    if (confirm(`Êtes-vous sûr de vouloir supprimer le projet \"${this.project.role}\" ?`)) {\n      this.projectService.delete(this.project.id!)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open('Projet supprimé avec succès', 'Fermer', { duration: 3000 });\n            this.router.navigate(['/projects']);\n          },\n          error: (error) => {\n            console.error('Error deleting project:', error);\n            this.snackBar.open('Erreur lors de la suppression du projet', 'Fermer', { duration: 3000 });\n          }\n        });\n    }\n  }\n\n  // Utility methods\n  getWorkModeLabel(workMode?: string): string {\n    switch (workMode) {\n      case 'REMOTE': return 'Télétravail';\n      case 'ONSITE': return 'Sur site';\n      case 'HYBRID': return 'Hybride';\n      default: return 'Non spécifié';\n    }\n  }\n\n  getWorkModeColor(workMode?: string): string {\n    switch (workMode) {\n      case 'REMOTE': return 'primary';\n      case 'ONSITE': return 'accent';\n      case 'HYBRID': return 'warn';\n      default: return '';\n    }\n  }\n\n  getRatingStars(rating?: number): string[] {\n    const stars = [];\n    for (let i = 1; i <= 5; i++) {\n      stars.push(i <= (rating || 0) ? 'star' : 'star_border');\n    }\n    return stars;\n  }\n\n  formatDate(dateString?: string): string {\n    if (!dateString) return 'Non spécifié';\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  }\n\n  calculateTotalRevenue(): number {\n    if (!this.project?.dailyRate || !this.project?.durationInMonths) return 0;\n    const daysPerMonth = (this.project.daysPerYear || 220) / 12;\n    return this.project.dailyRate * daysPerMonth * this.project.durationInMonths;\n  }\n\n  getProgressPercentage(): number {\n    if (!this.project?.totalSteps) return 0;\n    return Math.round((this.project.completedSteps || 0) / this.project.totalSteps * 100);\n  }\n\n  openLink(): void {\n    if (this.project?.link) {\n      window.open(this.project.link, '_blank');\n    }\n  }\n}\n", "<div class=\"project-detail-container\">\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Chargement des détails du projet...</p>\n  </div>\n\n  <div *ngIf=\"!isLoading && project\" class=\"project-content\">\n    <!-- Header Card -->\n    <mat-card class=\"header-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <div class=\"title-row\">\n            <div class=\"project-name\">\n              <mat-icon>work</mat-icon>\n              {{ project.role }}\n            </div>\n            <mat-chip [color]=\"getWorkModeColor(project.workMode)\" selected>\n              {{ getWorkModeLabel(project.workMode) }}\n            </mat-chip>\n          </div>\n        </mat-card-title>\n        <mat-card-subtitle>\n          Client: {{ project.clientName }} • {{ project.dailyRate }}€/jour\n        </mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-actions>\n        <button mat-button (click)=\"onBack()\">\n          <mat-icon>arrow_back</mat-icon>\n          Retour\n        </button>\n        <button mat-raised-button color=\"primary\" (click)=\"onEdit()\">\n          <mat-icon>edit</mat-icon>\n          Modifier\n        </button>\n        <button mat-button color=\"warn\" (click)=\"onDelete()\">\n          <mat-icon>delete</mat-icon>\n          Supprimer\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <div class=\"details-grid\">\n      <!-- Project Information -->\n      <mat-card class=\"info-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>info</mat-icon>\n            Informations du projet\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"info-item\">\n            <div class=\"info-label\">\n              <mat-icon>description</mat-icon>\n              Description\n            </div>\n            <div class=\"info-value\">\n              {{ project.description || 'Non spécifiée' }}\n            </div>\n          </div>\n\n          <mat-divider></mat-divider>\n\n          <div class=\"info-item\">\n            <div class=\"info-label\">\n              <mat-icon>code</mat-icon>\n              Stack technique\n            </div>\n            <div class=\"info-value\">\n              {{ project.techStack || 'Non spécifiée' }}\n            </div>\n          </div>\n\n          <mat-divider></mat-divider>\n\n          <div class=\"info-item\">\n            <div class=\"info-label\">\n              <mat-icon>star</mat-icon>\n              Avantages\n            </div>\n            <div class=\"info-value\">\n              {{ project.advantages || 'Non spécifiés' }}\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Financial Information -->\n      <mat-card class=\"info-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>euro</mat-icon>\n            Informations financières\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"info-item\">\n            <div class=\"info-label\">\n              <mat-icon>payments</mat-icon>\n              TJM\n            </div>\n            <div class=\"info-value\">\n              {{ project.dailyRate }}€/jour\n            </div>\n          </div>\n\n          <mat-divider></mat-divider>\n\n          <div class=\"info-item\">\n            <div class=\"info-label\">\n              <mat-icon>calculate</mat-icon>\n              Revenus estimés\n            </div>\n            <div class=\"info-value\">\n              {{ calculateTotalRevenue() | number:'1.0-0' }}€\n            </div>\n          </div>\n\n          <mat-divider *ngIf=\"project.daysPerYear\"></mat-divider>\n\n          <div class=\"info-item\" *ngIf=\"project.daysPerYear\">\n            <div class=\"info-label\">\n              <mat-icon>event</mat-icon>\n              Jours/an\n            </div>\n            <div class=\"info-value\">\n              {{ project.daysPerYear }} jours\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Timeline Information -->\n      <mat-card class=\"info-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>schedule</mat-icon>\n            Planning\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"info-item\">\n            <div class=\"info-label\">\n              <mat-icon>play_arrow</mat-icon>\n              Date de début\n            </div>\n            <div class=\"info-value\">\n              {{ formatDate(project.startDate) }}\n            </div>\n          </div>\n\n          <mat-divider *ngIf=\"project.durationInMonths\"></mat-divider>\n\n          <div class=\"info-item\" *ngIf=\"project.durationInMonths\">\n            <div class=\"info-label\">\n              <mat-icon>hourglass_empty</mat-icon>\n              Durée\n            </div>\n            <div class=\"info-value\">\n              {{ project.durationInMonths }} mois\n            </div>\n          </div>\n\n          <mat-divider *ngIf=\"project.orderRenewalInMonths\"></mat-divider>\n\n          <div class=\"info-item\" *ngIf=\"project.orderRenewalInMonths\">\n            <div class=\"info-label\">\n              <mat-icon>refresh</mat-icon>\n              Renouvellement\n            </div>\n            <div class=\"info-value\">\n              Tous les {{ project.orderRenewalInMonths }} mois\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Work Mode Details -->\n      <mat-card class=\"info-card\" *ngIf=\"project.workMode === 'HYBRID'\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>work</mat-icon>\n            Mode de travail\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"info-item\" *ngIf=\"project.remoteDaysPerMonth\">\n            <div class=\"info-label\">\n              <mat-icon>home</mat-icon>\n              Télétravail\n            </div>\n            <div class=\"info-value\">\n              {{ project.remoteDaysPerMonth }} jours/mois\n            </div>\n          </div>\n\n          <mat-divider *ngIf=\"project.remoteDaysPerMonth && project.onsiteDaysPerMonth\"></mat-divider>\n\n          <div class=\"info-item\" *ngIf=\"project.onsiteDaysPerMonth\">\n            <div class=\"info-label\">\n              <mat-icon>business</mat-icon>\n              Sur site\n            </div>\n            <div class=\"info-value\">\n              {{ project.onsiteDaysPerMonth }} jours/mois\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Progress Information -->\n      <mat-card class=\"info-card\" *ngIf=\"project.totalSteps\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>trending_up</mat-icon>\n            Progression\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"progress-info\">\n            <div class=\"progress-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-value\">{{ project.completedSteps || 0 }}</span>\n                <span class=\"stat-label\">Terminées</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-value\">{{ (project.totalSteps || 0) - (project.completedSteps || 0) - (project.failedSteps || 0) }}</span>\n                <span class=\"stat-label\">En cours</span>\n              </div>\n              <div class=\"stat-item\" *ngIf=\"project.failedSteps\">\n                <span class=\"stat-value\">{{ project.failedSteps }}</span>\n                <span class=\"stat-label\">Échouées</span>\n              </div>\n            </div>\n            <div class=\"progress-percentage\">\n              {{ getProgressPercentage() }}% complété\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Personal Rating -->\n      <mat-card class=\"info-card\" *ngIf=\"project.personalRating\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>grade</mat-icon>\n            Évaluation personnelle\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"rating-display\">\n            <mat-icon *ngFor=\"let star of getRatingStars(project.personalRating)\" \n                     [class.filled]=\"star === 'star'\">\n              {{ star }}\n            </mat-icon>\n            <span class=\"rating-text\">{{ project.personalRating }}/5</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Documents and Links -->\n      <mat-card class=\"info-card\" *ngIf=\"project.documents?.length || project.link\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>folder</mat-icon>\n            Documents et liens\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"documents-list\" *ngIf=\"project.documents?.length\">\n            <div class=\"document-item\" *ngFor=\"let document of project.documents\">\n              <mat-icon>description</mat-icon>\n              <span>{{ document }}</span>\n            </div>\n          </div>\n\n          <mat-divider *ngIf=\"project.documents?.length && project.link\"></mat-divider>\n\n          <div class=\"info-item\" *ngIf=\"project.link\">\n            <div class=\"info-label\">\n              <mat-icon>link</mat-icon>\n              Lien projet\n            </div>\n            <div class=\"info-value\">\n              <a [href]=\"project.link\" target=\"_blank\" class=\"project-link\" (click)=\"openLink()\">\n                {{ project.link }}\n                <mat-icon>open_in_new</mat-icon>\n              </a>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Notes -->\n      <mat-card class=\"info-card notes-card\" *ngIf=\"project.notes\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>note</mat-icon>\n            Notes\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"notes-content\">\n            {{ project.notes }}\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgC,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICTvCC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,+CAAmC;IACxCH,EADwC,CAAAI,YAAA,EAAI,EACtC;;;;;IAmHEJ,EAAA,CAAAE,SAAA,kBAAuD;;;;;IAInDF,EAFJ,CAAAC,cAAA,cAAmD,cACzB,eACZ;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAC,WAAA,YACF;;;;;IAwBFT,EAAA,CAAAE,SAAA,kBAA4D;;;;;IAIxDF,EAFJ,CAAAC,cAAA,cAAwD,cAC9B,eACZ;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAE,gBAAA,WACF;;;;;IAGFV,EAAA,CAAAE,SAAA,kBAAgE;;;;;IAI5DF,EAFJ,CAAAC,cAAA,cAA4D,cAClC,eACZ;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,eAAAC,MAAA,CAAAC,OAAA,CAAAG,oBAAA,WACF;;;;;IAgBEX,EAFJ,CAAAC,cAAA,cAA0D,cAChC,eACZ;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAI,kBAAA,iBACF;;;;;IAGFZ,EAAA,CAAAE,SAAA,kBAA4F;;;;;IAIxFF,EAFJ,CAAAC,cAAA,cAA0D,cAChC,eACZ;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAK,kBAAA,iBACF;;;;;IAxBAb,EAHN,CAAAC,cAAA,mBAAkE,sBAC/C,qBACC,eACJ;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,wBACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAClBJ,EAAA,CAAAC,cAAA,uBAAkB;IAahBD,EAZA,CAAAc,UAAA,IAAAC,wDAAA,kBAA0D,IAAAC,gEAAA,0BAUoB,IAAAC,wDAAA,kBAEpB;IAU9DjB,EADE,CAAAI,YAAA,EAAmB,EACV;;;;IAtBiBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAI,kBAAA,CAAgC;IAU1CZ,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAI,kBAAA,IAAAL,MAAA,CAAAC,OAAA,CAAAK,kBAAA,CAA8D;IAEpDb,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAK,kBAAA,CAAgC;;;;;IAgClDb,EADF,CAAAC,cAAA,cAAmD,eACxB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,yBAAQ;IACnCH,EADmC,CAAAI,YAAA,EAAO,EACpC;;;;IAFqBJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAmB,iBAAA,CAAAZ,MAAA,CAAAC,OAAA,CAAAY,WAAA,CAAyB;;;;;IAhBtDpB,EAHN,CAAAC,cAAA,mBAAuD,sBACpC,qBACC,eACJ;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAG,MAAA,oBACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAKVJ,EAJR,CAAAC,cAAA,uBAAkB,cACW,cACG,cACH,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,sBAAS;IACpCH,EADoC,CAAAI,YAAA,EAAO,EACrC;IAEJJ,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAA4F;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5HJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IACnCH,EADmC,CAAAI,YAAA,EAAO,EACpC;IACNJ,EAAA,CAAAc,UAAA,KAAAO,yDAAA,kBAAmD;IAIrDrB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAM,EACF,EACW,EACV;;;;IAjBwBJ,EAAA,CAAAK,SAAA,IAAiC;IAAjCL,EAAA,CAAAmB,iBAAA,CAAAZ,MAAA,CAAAC,OAAA,CAAAc,cAAA,MAAiC;IAIjCtB,EAAA,CAAAK,SAAA,GAA4F;IAA5FL,EAAA,CAAAmB,iBAAA,EAAAZ,MAAA,CAAAC,OAAA,CAAAe,UAAA,UAAAhB,MAAA,CAAAC,OAAA,CAAAc,cAAA,UAAAf,MAAA,CAAAC,OAAA,CAAAY,WAAA,OAA4F;IAG/FpB,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAY,WAAA,CAAyB;IAMjDpB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAiB,qBAAA,4BACF;;;;;IAeAxB,EAAA,CAAAC,cAAA,eAC0C;IACxCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IAFFJ,EAAA,CAAAyB,WAAA,WAAAC,OAAA,YAAgC;IACvC1B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoB,OAAA,MACF;;;;;IATA1B,EAHN,CAAAC,cAAA,mBAA2D,sBACxC,qBACC,eACJ;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,oCACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAEhBJ,EADF,CAAAC,cAAA,uBAAkB,cACY;IAC1BD,EAAA,CAAAc,UAAA,IAAAa,6DAAA,uBAC0C;IAG1C3B,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,IAA8B;IAG9DH,EAH8D,CAAAI,YAAA,EAAO,EAC3D,EACW,EACV;;;;IAPsBJ,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkB,UAAA,YAAAX,MAAA,CAAAqB,cAAA,CAAArB,MAAA,CAAAC,OAAA,CAAAqB,cAAA,EAAyC;IAI1C7B,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAC,OAAA,CAAAqB,cAAA,OAA8B;;;;;IAgBtD7B,EADF,CAAAC,cAAA,cAAsE,eAC1D;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAc;IACtBH,EADsB,CAAAI,YAAA,EAAO,EACvB;;;;IADEJ,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAmB,iBAAA,CAAAW,WAAA,CAAc;;;;;IAHxB9B,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAc,UAAA,IAAAiB,8DAAA,kBAAsE;IAIxE/B,EAAA,CAAAI,YAAA,EAAM;;;;IAJ4CJ,EAAA,CAAAK,SAAA,EAAoB;IAApBL,EAAA,CAAAkB,UAAA,YAAAX,MAAA,CAAAC,OAAA,CAAAwB,SAAA,CAAoB;;;;;IAMtEhC,EAAA,CAAAE,SAAA,kBAA6E;;;;;;IAIzEF,EAFJ,CAAAC,cAAA,cAA4C,cAClB,eACZ;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAAwB,YAC6D;IAArBD,EAAA,CAAAiC,UAAA,mBAAAC,4EAAA;MAAAlC,EAAA,CAAAmC,aAAA,CAAAC,GAAA;MAAA,MAAA7B,MAAA,GAAAP,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAAS/B,MAAA,CAAAgC,QAAA,EAAU;IAAA,EAAC;IAChFvC,EAAA,CAAAG,MAAA,GACA;IAAAH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAG3BH,EAH2B,CAAAI,YAAA,EAAW,EAC9B,EACA,EACF;;;;IALCJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAgC,IAAA,EAAAxC,EAAA,CAAAyC,aAAA,CAAqB;IACtBzC,EAAA,CAAAK,SAAA,EACA;IADAL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAgC,IAAA,MACA;;;;;IAtBJxC,EAHN,CAAAC,cAAA,mBAA8E,sBAC3D,qBACC,eACJ;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,2BACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAClBJ,EAAA,CAAAC,cAAA,uBAAkB;IAUhBD,EATA,CAAAc,UAAA,IAAA4B,wDAAA,kBAA8D,IAAAC,gEAAA,0BAOC,IAAAC,wDAAA,mBAEnB;IAahD5C,EADE,CAAAI,YAAA,EAAmB,EACV;;;;IAtBsBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAwB,SAAA,kBAAAzB,MAAA,CAAAC,OAAA,CAAAwB,SAAA,CAAAa,MAAA,CAA+B;IAO9C7C,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAkB,UAAA,UAAAX,MAAA,CAAAC,OAAA,CAAAwB,SAAA,kBAAAzB,MAAA,CAAAC,OAAA,CAAAwB,SAAA,CAAAa,MAAA,KAAAtC,MAAA,CAAAC,OAAA,CAAAgC,IAAA,CAA+C;IAErCxC,EAAA,CAAAK,SAAA,EAAkB;IAAlBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAgC,IAAA,CAAkB;;;;;IAmBxCxC,EAHN,CAAAC,cAAA,mBAA6D,sBAC1C,qBACC,eACJ;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,cACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAEhBJ,EADF,CAAAC,cAAA,uBAAkB,cACW;IACzBD,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAM,EACW,EACV;;;;IAHLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAsC,KAAA,MACF;;;;;;IApSI9C,EAPZ,CAAAC,cAAA,aAA2D,kBAE3B,sBACX,qBACC,aACS,aACK,eACd;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,kBAAgE;IAC9DD,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAW,EACP,EACS;IACjBJ,EAAA,CAAAC,cAAA,yBAAmB;IACjBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAoB,EACJ;IAGhBJ,EADF,CAAAC,cAAA,wBAAkB,iBACsB;IAAnBD,EAAA,CAAAiC,UAAA,mBAAAc,+DAAA;MAAA/C,EAAA,CAAAmC,aAAA,CAAAa,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAAS/B,MAAA,CAAA0C,MAAA,EAAQ;IAAA,EAAC;IACnCjD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA6D;IAAnBD,EAAA,CAAAiC,UAAA,mBAAAiB,+DAAA;MAAAlD,EAAA,CAAAmC,aAAA,CAAAa,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAAS/B,MAAA,CAAA4C,MAAA,EAAQ;IAAA,EAAC;IAC1DnD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAqD;IAArBD,EAAA,CAAAiC,UAAA,mBAAAmB,+DAAA;MAAApD,EAAA,CAAAmC,aAAA,CAAAa,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAAS/B,MAAA,CAAA8C,QAAA,EAAU;IAAA,EAAC;IAClDrD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,mBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACQ,EACV;IAOHJ,EALR,CAAAC,cAAA,eAA0B,oBAEI,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,gCACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACO,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAM,EACF,EACW,EACV;IAMLJ,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,uCACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACO,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,aACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAINJ,EAFA,CAAAc,UAAA,KAAAwC,oDAAA,0BAAyC,KAAAC,4CAAA,kBAEU;IAUvDvD,EADE,CAAAI,YAAA,EAAmB,EACV;IAMLJ,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,kBACF;IACFH,EADE,CAAAI,YAAA,EAAiB,EACD;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACO,eACG,gBACZ;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAgBNJ,EAdA,CAAAc,UAAA,KAAA0C,oDAAA,0BAA8C,KAAAC,4CAAA,kBAEU,KAAAC,oDAAA,0BAUN,KAAAC,4CAAA,kBAEU;IAUhE3D,EADE,CAAAI,YAAA,EAAmB,EACV;IAuHXJ,EApHA,CAAAc,UAAA,MAAA8C,kDAAA,wBAAkE,MAAAC,kDAAA,wBAiCX,MAAAC,kDAAA,wBA+BI,MAAAC,kDAAA,wBAmBmB,MAAAC,kDAAA,uBAiCjB;IAcjEhE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAvSMJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAyD,IAAA,MACF;IACUjE,EAAA,CAAAK,SAAA,EAA4C;IAA5CL,EAAA,CAAAkB,UAAA,UAAAX,MAAA,CAAA2D,gBAAA,CAAA3D,MAAA,CAAAC,OAAA,CAAA2D,QAAA,EAA4C;IACpDnE,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAAC,OAAA,CAAA2D,QAAA,OACF;IAIFnE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAqE,kBAAA,cAAA9D,MAAA,CAAAC,OAAA,CAAA8D,UAAA,cAAA/D,MAAA,CAAAC,OAAA,CAAA+D,SAAA,iBACF;IAmCMvE,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAgE,WAAA,mCACF;IAWExE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAiE,SAAA,mCACF;IAWEzE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAAkE,UAAA,mCACF;IAoBE1E,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,CAAA+D,SAAA,iBACF;IAWEvE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAA2E,WAAA,SAAApE,MAAA,CAAAqE,qBAAA,wBACF;IAGY5E,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAC,WAAA,CAAyB;IAEfT,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAC,WAAA,CAAyB;IA2B7CT,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAsE,UAAA,CAAAtE,MAAA,CAAAC,OAAA,CAAAsE,SAAA,OACF;IAGY9E,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAE,gBAAA,CAA8B;IAEpBV,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAE,gBAAA,CAA8B;IAUxCV,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAG,oBAAA,CAAkC;IAExBX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAG,oBAAA,CAAkC;IAajCX,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAA2D,QAAA,cAAmC;IAiCnCnE,EAAA,CAAAK,SAAA,EAAwB;IAAxBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAe,UAAA,CAAwB;IA+BxBvB,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAqB,cAAA,CAA4B;IAmB5B7B,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAkB,UAAA,UAAAX,MAAA,CAAAC,OAAA,CAAAwB,SAAA,kBAAAzB,MAAA,CAAAC,OAAA,CAAAwB,SAAA,CAAAa,MAAA,KAAAtC,MAAA,CAAAC,OAAA,CAAAgC,IAAA,CAA+C;IAiCpCxC,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAsC,KAAA,CAAmB;;;ADzQjE,OAAM,MAAOiC,sBAAsB;EAOjCC,YACmBC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAHrB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAT3B,KAAAC,SAAS,GAAG,KAAK;IAGA,KAAAC,QAAQ,GAAG,IAAIxF,OAAO,EAAQ;EAO5C;EAEHyF,QAAQA,CAAA;IACN,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,IAAI,CAAC1F,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAG;MAClE,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACG,SAAS,GAAG,CAACH,MAAM,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACI,WAAW,EAAE;;IAEtB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACpB,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE;EAC1B;EAEQH,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;IAErB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,cAAc,CAACe,OAAO,CAAC,IAAI,CAACL,SAAS,CAAC,CACxCF,IAAI,CAAC1F,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTI,IAAI,EAAGtF,OAAO,IAAI;QAChB,IAAIA,OAAO,EAAE;UACX,IAAI,CAACA,OAAO,GAAGA,OAAO;SACvB,MAAM;UACL,IAAI,CAAC4E,QAAQ,CAACa,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACrE,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAErC,IAAI,CAACd,SAAS,GAAG,KAAK;MACxB,CAAC;MACDe,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,QAAQ,CAACa,IAAI,CAAC,qCAAqC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEApC,MAAMA,CAAA;IACJ,IAAI,CAACiC,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAhD,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC3C,OAAO,EAAE;MAChB,IAAI,CAAC0E,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC3F,OAAO,CAAC8F,EAAE,EAAE,MAAM,CAAC,CAAC;;EAEhE;EAEAjD,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC7C,OAAO,EAAE;IAEnB,IAAI+F,OAAO,CAAC,iDAAiD,IAAI,CAAC/F,OAAO,CAACyD,IAAI,KAAK,CAAC,EAAE;MACpF,IAAI,CAACgB,cAAc,CAACuB,MAAM,CAAC,IAAI,CAAChG,OAAO,CAAC8F,EAAG,CAAC,CACzCb,IAAI,CAAC1F,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;QACTI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACV,QAAQ,CAACa,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAC/E,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAAChB,QAAQ,CAACa,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC7F;OACD,CAAC;;EAER;EAEA;EACA9B,gBAAgBA,CAACD,QAAiB;IAChC,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,aAAa;MACnC,KAAK,QAAQ;QAAE,OAAO,UAAU;MAChC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B;QAAS,OAAO,cAAc;;EAElC;EAEAD,gBAAgBA,CAACC,QAAiB;IAChC,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B;QAAS,OAAO,EAAE;;EAEtB;EAEAvC,cAAcA,CAAC6E,MAAe;IAC5B,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3BD,KAAK,CAACE,IAAI,CAACD,CAAC,KAAKF,MAAM,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,aAAa,CAAC;;IAEzD,OAAOC,KAAK;EACd;EAEA7B,UAAUA,CAACgC,UAAmB;IAC5B,IAAI,CAACA,UAAU,EAAE,OAAO,cAAc;IACtC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EACzD;EAEAnC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpE,OAAO,EAAE+D,SAAS,IAAI,CAAC,IAAI,CAAC/D,OAAO,EAAEE,gBAAgB,EAAE,OAAO,CAAC;IACzE,MAAMsG,YAAY,GAAG,CAAC,IAAI,CAACxG,OAAO,CAACC,WAAW,IAAI,GAAG,IAAI,EAAE;IAC3D,OAAO,IAAI,CAACD,OAAO,CAAC+D,SAAS,GAAGyC,YAAY,GAAG,IAAI,CAACxG,OAAO,CAACE,gBAAgB;EAC9E;EAEAc,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAChB,OAAO,EAAEe,UAAU,EAAE,OAAO,CAAC;IACvC,OAAO0F,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI,CAAC1G,OAAO,CAACc,cAAc,IAAI,CAAC,IAAI,IAAI,CAACd,OAAO,CAACe,UAAU,GAAG,GAAG,CAAC;EACvF;EAEAgB,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/B,OAAO,EAAEgC,IAAI,EAAE;MACtB2E,MAAM,CAAClB,IAAI,CAAC,IAAI,CAACzF,OAAO,CAACgC,IAAI,EAAE,QAAQ,CAAC;;EAE5C;;;uBAhIWuC,sBAAsB,EAAA/E,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAxH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAzH,EAAA,CAAAoH,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtB5C,sBAAsB;MAAA6C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9H,EAAA,CAAA+H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9BnCrI,EAAA,CAAAC,cAAA,aAAsC;UAMpCD,EALA,CAAAc,UAAA,IAAAyH,qCAAA,iBAAiD,IAAAC,qCAAA,oBAKU;UAgT7DxI,EAAA,CAAAI,YAAA,EAAM;;;UArTEJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAkB,UAAA,SAAAoH,GAAA,CAAAjD,SAAA,CAAe;UAKfrF,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAkB,UAAA,UAAAoH,GAAA,CAAAjD,SAAA,IAAAiD,GAAA,CAAA9H,OAAA,CAA2B;;;qBDY/BlB,YAAY,EAAAmJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZrJ,YAAY,EACZC,aAAa,EAAAqJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACb1J,eAAe,EAAA2J,EAAA,CAAAC,SAAA,EACf3J,aAAa,EAAA4J,EAAA,CAAAC,OAAA,EACb5J,cAAc,EAAA6J,EAAA,CAAAC,OAAA,EACd7J,wBAAwB,EAAA8J,EAAA,CAAAC,kBAAA,EACxB9J,gBAAgB,EAAA+J,GAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}