{"ast": null, "code": "import { inject } from '@angular/core';\nimport { AuthService } from '../services/auth.service';\nexport const authInterceptor = (req, next) => {\n  const authService = inject(AuthService);\n  const token = authService.getToken();\n  if (token) {\n    const authReq = req.clone({\n      headers: req.headers.set('Authorization', `Bearer ${token}`)\n    });\n    return next(authReq);\n  }\n  return next(req);\n};", "map": {"version": 3, "names": ["inject", "AuthService", "authInterceptor", "req", "next", "authService", "token", "getToken", "authReq", "clone", "headers", "set"], "sources": ["C:\\dev\\workspace\\indezy\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { HttpInterceptorFn } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { AuthService } from '../services/auth.service';\n\nexport const authInterceptor: HttpInterceptorFn = (req, next) => {\n  const authService = inject(AuthService);\n  const token = authService.getToken();\n\n  if (token) {\n    const authReq = req.clone({\n      headers: req.headers.set('Authorization', `Bearer ${token}`)\n    });\n    return next(authReq);\n  }\n\n  return next(req);\n};\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,OAAO,MAAMC,eAAe,GAAsBA,CAACC,GAAG,EAAEC,IAAI,KAAI;EAC9D,MAAMC,WAAW,GAAGL,MAAM,CAACC,WAAW,CAAC;EACvC,MAAMK,KAAK,GAAGD,WAAW,CAACE,QAAQ,EAAE;EAEpC,IAAID,KAAK,EAAE;IACT,MAAME,OAAO,GAAGL,GAAG,CAACM,KAAK,CAAC;MACxBC,OAAO,EAAEP,GAAG,CAACO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUL,KAAK,EAAE;KAC5D,CAAC;IACF,OAAOF,IAAI,CAACI,OAAO,CAAC;;EAGtB,OAAOJ,IAAI,CAACD,GAAG,CAAC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}