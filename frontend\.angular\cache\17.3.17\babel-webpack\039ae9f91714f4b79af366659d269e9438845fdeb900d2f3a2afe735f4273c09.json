{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/client.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction ClientFormComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientFormComponent_form_11_mat_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const industry_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", industry_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", industry_r3, \" \");\n  }\n}\nfunction ClientFormComponent_form_11_mat_option_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r4.label, \" \");\n  }\n}\nfunction ClientFormComponent_form_11_mat_spinner_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 28);\n  }\n}\nfunction ClientFormComponent_form_11_mat_icon_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"save\" : \"add\");\n  }\n}\nfunction ClientFormComponent_form_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function ClientFormComponent_form_11_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7)(3, \"h3\");\n    i0.ɵɵtext(4, \"Informations g\\u00E9n\\u00E9rales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 8)(6, \"mat-form-field\", 9)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Nom de l'entreprise *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 10);\n    i0.ɵɵelementStart(10, \"mat-error\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 8)(13, \"mat-form-field\", 11)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Personne de contact *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 12);\n    i0.ɵɵelementStart(17, \"mat-error\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-form-field\", 11)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"Secteur d'activit\\u00E9 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-select\", 13);\n    i0.ɵɵtemplate(23, ClientFormComponent_form_11_mat_option_23_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-error\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 8)(27, \"mat-form-field\", 11)(28, \"mat-label\");\n    i0.ɵɵtext(29, \"Email *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 15);\n    i0.ɵɵelementStart(31, \"mat-icon\", 16);\n    i0.ɵɵtext(32, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-error\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"mat-form-field\", 11)(36, \"mat-label\");\n    i0.ɵɵtext(37, \"T\\u00E9l\\u00E9phone *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 17);\n    i0.ɵɵelementStart(39, \"mat-icon\", 16);\n    i0.ɵɵtext(40, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-error\");\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 8)(44, \"mat-form-field\", 9)(45, \"mat-label\");\n    i0.ɵɵtext(46, \"Adresse *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"textarea\", 18);\n    i0.ɵɵelementStart(48, \"mat-error\");\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"div\", 8)(51, \"mat-form-field\", 11)(52, \"mat-label\");\n    i0.ɵɵtext(53, \"Site web\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"input\", 19);\n    i0.ɵɵelementStart(55, \"mat-icon\", 16);\n    i0.ɵɵtext(56, \"link\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"mat-form-field\", 11)(58, \"mat-label\");\n    i0.ɵɵtext(59, \"Statut *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"mat-select\", 20);\n    i0.ɵɵtemplate(61, ClientFormComponent_form_11_mat_option_61_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"mat-error\");\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(64, \"div\", 7)(65, \"h3\");\n    i0.ɵɵtext(66, \"Informations compl\\u00E9mentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"div\", 8)(68, \"mat-form-field\", 9)(69, \"mat-label\");\n    i0.ɵɵtext(70, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"textarea\", 21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(72, \"div\", 22)(73, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ClientFormComponent_form_11_Template_button_click_73_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(74, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"button\", 24);\n    i0.ɵɵtemplate(76, ClientFormComponent_form_11_mat_spinner_76_Template, 1, 0, \"mat-spinner\", 25)(77, ClientFormComponent_form_11_mat_icon_77_Template, 2, 1, \"mat-icon\", 26);\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.clientForm);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"name\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"contactPerson\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.industries);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"industry\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"email\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"phone\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"address\"));\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.statuses);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(\"status\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSaving);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSaving);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSaving);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSaving);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isSaving ? \"Enregistrement...\" : ctx_r1.isEditMode ? \"Modifier\" : \"Cr\\u00E9er\", \" \");\n  }\n}\nexport let ClientFormComponent = /*#__PURE__*/(() => {\n  class ClientFormComponent {\n    constructor(fb, clientService, router, route, snackBar) {\n      this.fb = fb;\n      this.clientService = clientService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.isEditMode = false;\n      this.isLoading = false;\n      this.isSaving = false;\n      this.industries = ['Technology', 'Finance', 'Healthcare', 'Education', 'E-commerce', 'Manufacturing', 'Consulting', 'Marketing', 'Real Estate', 'Retail', 'Transportation', 'Energy', 'Media', 'Government', 'Non-profit', 'Other'];\n      this.statuses = [{\n        value: 'ACTIVE',\n        label: 'Actif'\n      }, {\n        value: 'INACTIVE',\n        label: 'Inactif'\n      }, {\n        value: 'PROSPECT',\n        label: 'Prospect'\n      }];\n      this.destroy$ = new Subject();\n      this.clientForm = this.createForm();\n    }\n    ngOnInit() {\n      this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        if (params['id']) {\n          this.clientId = +params['id'];\n          this.isEditMode = true;\n          this.loadClient();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    createForm() {\n      return this.fb.group({\n        name: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        phone: ['', [Validators.required]],\n        address: ['', [Validators.required]],\n        contactPerson: ['', [Validators.required]],\n        industry: ['', [Validators.required]],\n        website: [''],\n        notes: [''],\n        status: ['ACTIVE', [Validators.required]]\n      });\n    }\n    loadClient() {\n      if (!this.clientId) return;\n      this.isLoading = true;\n      this.clientService.getClient(this.clientId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: client => {\n          if (client) {\n            this.clientForm.patchValue(client);\n          } else {\n            this.snackBar.open('Client non trouvé', 'Fermer', {\n              duration: 3000\n            });\n            this.router.navigate(['/clients']);\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading client:', error);\n          this.snackBar.open('Erreur lors du chargement du client', 'Fermer', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    onSubmit() {\n      if (this.clientForm.valid) {\n        this.isSaving = true;\n        const formValue = this.clientForm.value;\n        if (this.isEditMode && this.clientId) {\n          const updateData = {\n            id: this.clientId,\n            ...formValue\n          };\n          this.clientService.updateClient(updateData).pipe(takeUntil(this.destroy$)).subscribe({\n            next: () => {\n              this.snackBar.open('Client modifié avec succès', 'Fermer', {\n                duration: 3000\n              });\n              this.router.navigate(['/clients']);\n            },\n            error: error => {\n              console.error('Error updating client:', error);\n              this.snackBar.open('Erreur lors de la modification du client', 'Fermer', {\n                duration: 3000\n              });\n              this.isSaving = false;\n            }\n          });\n        } else {\n          const createData = formValue;\n          this.clientService.createClient(createData).pipe(takeUntil(this.destroy$)).subscribe({\n            next: () => {\n              this.snackBar.open('Client créé avec succès', 'Fermer', {\n                duration: 3000\n              });\n              this.router.navigate(['/clients']);\n            },\n            error: error => {\n              console.error('Error creating client:', error);\n              this.snackBar.open('Erreur lors de la création du client', 'Fermer', {\n                duration: 3000\n              });\n              this.isSaving = false;\n            }\n          });\n        }\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    onCancel() {\n      this.router.navigate(['/clients']);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.clientForm.controls).forEach(key => {\n        const control = this.clientForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFieldError(fieldName) {\n      const control = this.clientForm.get(fieldName);\n      if (control?.errors && control.touched) {\n        if (control.errors['required']) {\n          return 'Ce champ est requis';\n        }\n        if (control.errors['email']) {\n          return 'Veuillez saisir une adresse email valide';\n        }\n        if (control.errors['minlength']) {\n          return `Minimum ${control.errors['minlength'].requiredLength} caractères`;\n        }\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function ClientFormComponent_Factory(t) {\n        return new (t || ClientFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ClientService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ClientFormComponent,\n        selectors: [[\"app-client-form\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 12,\n        vars: 5,\n        consts: [[1, \"client-form-container\"], [1, \"form-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"loading-container\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-grid\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Ex: TechCorp Solutions\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"contactPerson\", \"placeholder\", \"Ex: Marie Dubois\"], [\"formControlName\", \"industry\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"placeholder\", \"+33 1 23 45 67 89\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"rows\", \"2\", \"placeholder\", \"123 Avenue des Champs-\\u00C9lys\\u00E9es, 75008 Paris\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"https://www.entreprise.com\"], [\"formControlName\", \"status\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"4\", \"placeholder\", \"Notes sur le client, historique, pr\\u00E9f\\u00E9rences...\"], [1, \"form-actions\"], [\"type\", \"button\", \"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", \"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"value\"], [\"diameter\", \"20\"]],\n        template: function ClientFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n            i0.ɵɵtext(8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card-content\");\n            i0.ɵɵtemplate(10, ClientFormComponent_div_10_Template, 4, 0, \"div\", 2)(11, ClientFormComponent_form_11_Template, 79, 15, \"form\", 3);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add\");\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier le Client\" : \"Nouveau Client\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations du client\" : \"Ajoutez un nouveau client \\u00E0 votre portefeuille\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, RouterModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatSelectModule, i9.MatSelect, i10.MatOption, MatButtonModule, i11.MatButton, MatIconModule, i12.MatIcon, MatProgressSpinnerModule, i13.MatProgressSpinner],\n        styles: [\".client-form-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:1.5rem}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:1.5rem;height:1.5rem}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{margin-top:8px;color:#0009}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding-top:24px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:32px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 20px;font-size:1.1rem;font-weight:500;color:#000000de;border-bottom:1px solid rgba(0,0,0,.12);padding-bottom:8px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]:last-child{margin-bottom:0}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{flex:1}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{flex:1;min-width:0}@media (max-width: 600px){.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{width:100%}}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:12px;margin-top:32px;padding-top:24px;border-top:1px solid rgba(0,0,0,.12)}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-right:8px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.2rem;width:1.2rem;height:1.2rem}@media (max-width: 600px){.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{flex-direction:column-reverse}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}.mat-mdc-form-field.full-width[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%], .mat-mdc-form-field.half-width[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{width:100%}.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%]   .mat-mdc-form-field-error-wrapper[_ngcontent-%COMP%]   .mat-mdc-form-field-error[_ngcontent-%COMP%]{font-size:.75rem;color:#f44336}textarea.mat-mdc-input-element[_ngcontent-%COMP%]{resize:vertical;min-height:60px}.mat-mdc-select[_ngcontent-%COMP%]   .mat-mdc-select-trigger[_ngcontent-%COMP%]   .mat-mdc-select-value[_ngcontent-%COMP%]{color:#000000de}.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]{background-color:var(--mdc-theme-primary, #1976d2);color:#fff}.mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%]:disabled{background-color:#0000001f;color:#00000042}.mat-mdc-button[_ngcontent-%COMP%]:not(.mat-mdc-raised-button){color:#0009}.mat-mdc-button[_ngcontent-%COMP%]:not(.mat-mdc-raised-button):hover{background-color:#0000000a}@media (max-width: 768px){.client-form-container[_ngcontent-%COMP%]{padding:16px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding:16px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]{gap:24px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{margin-bottom:12px}.client-form-container[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{margin-top:24px;padding-top:16px}}\"]\n      });\n    }\n  }\n  return ClientFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}