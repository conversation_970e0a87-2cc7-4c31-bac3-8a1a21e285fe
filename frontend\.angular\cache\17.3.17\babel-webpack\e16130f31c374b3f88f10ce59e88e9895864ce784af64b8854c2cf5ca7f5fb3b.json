{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, booleanAttribute, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, inject, ElementRef, EventEmitter, ANIMATION_MODULE_TYPE, numberAttribute, Output, ContentChildren, forwardRef, QueryList, Attribute, NgModule } from '@angular/core';\nimport { MatRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule } from '@angular/material/core';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { Subject, fromEvent, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, CdkMonitorFocus } from '@angular/cdk/a11y';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { takeUntil, take, startWith, switchMap, skip, filter, distinctUntilChanged } from 'rxjs/operators';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst _c0 = [\"*\"];\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c1 = [\"tabListContainer\"];\nconst _c2 = [\"tabList\"];\nconst _c3 = [\"tabListInner\"];\nconst _c4 = [\"nextPaginator\"];\nconst _c5 = [\"previousPaginator\"];\nconst _c6 = a0 => ({\n  animationDuration: a0\n});\nconst _c7 = (a0, a1) => ({\n  value: a0,\n  params: a1\n});\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c8 = [\"tabBodyWrapper\"];\nconst _c9 = [\"tabHeader\"];\nfunction MatTabGroup_For_3_Conditional_6_ng_template_0_Template(rf, ctx) {}\nfunction MatTabGroup_For_3_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_For_3_Conditional_6_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction MatTabGroup_For_3_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction MatTabGroup_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7, 2);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_For_3_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const tab_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const tabHeader_r7 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r5._handleClick(tab_r4, tabHeader_r7, i_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r2).$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._tabFocusChanged($event, i_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵtemplate(6, MatTabGroup_For_3_Conditional_6_Template, 1, 1, null, 12)(7, MatTabGroup_For_3_Conditional_7_Template, 1, 1);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const i_r5 = ctx.$index;\n    const tabNode_r8 = i0.ɵɵreference(1);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r4.labelClass);\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r5.selectedIndex === i_r5);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabLabelId(i_r5))(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r5.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r5._getTabIndex(i_r5))(\"aria-posinset\", i_r5 + 1)(\"aria-setsize\", ctx_r5._tabs.length)(\"aria-controls\", ctx_r5._getTabContentId(i_r5))(\"aria-selected\", ctx_r5.selectedIndex === i_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", tabNode_r8)(\"matRippleDisabled\", tab_r4.disabled || ctx_r5.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(6, tab_r4.templateLabel ? 6 : 7);\n  }\n}\nfunction MatTabGroup_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatTabGroup_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 13);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._setTabBodyWrapperHeight($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.$index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r10.bodyClass);\n    i0.ɵɵclassProp(\"mat-mdc-tab-body-active\", ctx_r5.selectedIndex === i_r11);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabContentId(i_r11))(\"content\", tab_r10.content)(\"position\", tab_r10.position)(\"origin\", tab_r10.origin)(\"animationDuration\", ctx_r5.animationDuration)(\"preserveContent\", ctx_r5.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r5.contentTabIndex != null && ctx_r5.selectedIndex === i_r11 ? ctx_r5.contentTabIndex : null)(\"aria-labelledby\", ctx_r5._getTabLabelId(i_r11))(\"aria-hidden\", ctx_r5.selectedIndex !== i_r11);\n  }\n}\nconst _c10 = [\"mat-tab-nav-bar\", \"\"];\nconst _c11 = [\"mat-tab-link\", \"\"];\nconst MAT_TAB_CONTENT = /*#__PURE__*/new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nlet MatTabContent = /*#__PURE__*/(() => {\n  class MatTabContent {\n    constructor(/** Content for the tab. */template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function MatTabContent_Factory(t) {\n        return new (t || MatTabContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatTabContent,\n        selectors: [[\"\", \"matTabContent\", \"\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_TAB_CONTENT,\n          useExisting: MatTabContent\n        }])]\n      });\n    }\n  }\n  return MatTabContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = /*#__PURE__*/new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = /*#__PURE__*/new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nlet MatTabLabel = /*#__PURE__*/(() => {\n  class MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n      super(templateRef, viewContainerRef);\n      this._closestTab = _closestTab;\n    }\n    static {\n      this.ɵfac = function MatTabLabel_Factory(t) {\n        return new (t || MatTabLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatTabLabel,\n        selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_TAB_LABEL,\n          useExisting: MatTabLabel\n        }]), i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatTabLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = /*#__PURE__*/new InjectionToken('MAT_TAB_GROUP');\nlet MatTab = /*#__PURE__*/(() => {\n  class MatTab {\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n      return this._templateLabel;\n    }\n    set templateLabel(value) {\n      this._setTemplateLabelInput(value);\n    }\n    /** @docs-private */\n    get content() {\n      return this._contentPortal;\n    }\n    constructor(_viewContainerRef, _closestTabGroup) {\n      this._viewContainerRef = _viewContainerRef;\n      this._closestTabGroup = _closestTabGroup;\n      /** whether the tab is disabled. */\n      this.disabled = false;\n      /**\n       * Template provided in the tab content that will be used if present, used to enable lazy-loading\n       */\n      this._explicitContent = undefined;\n      /** Plain text label for the tab, used when there is no template label. */\n      this.textLabel = '';\n      /** Portal that will be the hosted content of the tab */\n      this._contentPortal = null;\n      /** Emits whenever the internal state of the tab changes. */\n      this._stateChanges = new Subject();\n      /**\n       * The relatively indexed position where 0 represents the center, negative is left, and positive\n       * represents the right.\n       */\n      this.position = null;\n      /**\n       * The initial relatively index origin of the tab if it was created and selected after there\n       * was already a selected tab. Provides context of what position the tab should originate from.\n       */\n      this.origin = null;\n      /**\n       * Whether the tab is currently active.\n       */\n      this.isActive = false;\n    }\n    ngOnChanges(changes) {\n      if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n        this._stateChanges.next();\n      }\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n    }\n    ngOnInit() {\n      this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n      // Only update the label if the query managed to find one. This works around an issue where a\n      // user may have manually set `templateLabel` during creation mode, which would then get\n      // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n      // tab matches the current one so that we don't pick up labels from nested tabs.\n      if (value && value._closestTab === this) {\n        this._templateLabel = value;\n      }\n    }\n    static {\n      this.ɵfac = function MatTab_Factory(t) {\n        return new (t || MatTab)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB_GROUP, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTab,\n        selectors: [[\"mat-tab\"]],\n        contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n          }\n        },\n        viewQuery: function MatTab_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(TemplateRef, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n          }\n        },\n        hostAttrs: [\"hidden\", \"\"],\n        inputs: {\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n          textLabel: [i0.ɵɵInputFlags.None, \"label\", \"textLabel\"],\n          ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n          ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n          labelClass: \"labelClass\",\n          bodyClass: \"bodyClass\"\n        },\n        exportAs: [\"matTab\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_TAB,\n          useExisting: MatTab\n        }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 1,\n        vars: 0,\n        template: function MatTab_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return MatTab;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  constructor(_items) {\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    if (correspondingItem === currentItem) {\n      return;\n    }\n    currentItem?.deactivateInkBar();\n    if (correspondingItem) {\n      const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\nlet InkBarItem = /*#__PURE__*/(() => {\n  class InkBarItem {\n    constructor() {\n      this._elementRef = inject(ElementRef);\n      this._fitToContent = false;\n    }\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n      return this._fitToContent;\n    }\n    set fitInkBarToContent(newValue) {\n      if (this._fitToContent !== newValue) {\n        this._fitToContent = newValue;\n        if (this._inkBarElement) {\n          this._appendInkBarElement();\n        }\n      }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n      const element = this._elementRef.nativeElement;\n      // Early exit if no indicator is present to handle cases where an indicator\n      // may be activated without a prior indicator state\n      if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n        element.classList.add(ACTIVE_CLASS);\n        return;\n      }\n      // This animation uses the FLIP approach. You can read more about it at the link below:\n      // https://aerotwist.com/blog/flip-your-animations/\n      // Calculate the dimensions based on the dimensions of the previous indicator\n      const currentClientRect = element.getBoundingClientRect();\n      const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n      const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n      element.classList.add(NO_TRANSITION_CLASS);\n      this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n      // Force repaint before updating classes and transform to ensure the transform properly takes effect\n      element.getBoundingClientRect();\n      element.classList.remove(NO_TRANSITION_CLASS);\n      element.classList.add(ACTIVE_CLASS);\n      this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n      this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n      this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n      this._inkBarElement?.remove();\n      this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n      const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n      const inkBarElement = this._inkBarElement = documentNode.createElement('span');\n      const inkBarContentElement = this._inkBarContentElement = documentNode.createElement('span');\n      inkBarElement.className = 'mdc-tab-indicator';\n      inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n      inkBarElement.appendChild(this._inkBarContentElement);\n      this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n      if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Ink bar element has not been created and cannot be appended');\n      }\n      const parentElement = this._fitToContent ? this._elementRef.nativeElement.querySelector('.mdc-tab__content') : this._elementRef.nativeElement;\n      if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Missing element to host the ink bar');\n      }\n      parentElement.appendChild(this._inkBarElement);\n    }\n    static {\n      this.ɵfac = function InkBarItem_Factory(t) {\n        return new (t || InkBarItem)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: InkBarItem,\n        inputs: {\n          fitInkBarToContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute]\n        },\n        features: [i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return InkBarItem;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = /*#__PURE__*/new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nlet MatTabLabelWrapper = /*#__PURE__*/(() => {\n  class MatTabLabelWrapper extends InkBarItem {\n    constructor(elementRef) {\n      super();\n      this.elementRef = elementRef;\n      /** Whether the tab is disabled. */\n      this.disabled = false;\n    }\n    /** Sets focus on the wrapper element */\n    focus() {\n      this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n      return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n      return this.elementRef.nativeElement.offsetWidth;\n    }\n    static {\n      this.ɵfac = function MatTabLabelWrapper_Factory(t) {\n        return new (t || MatTabLabelWrapper)(i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatTabLabelWrapper,\n        selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n        hostVars: 3,\n        hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n            i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n          }\n        },\n        inputs: {\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatTabLabelWrapper;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nlet MatPaginatedTabHeader = /*#__PURE__*/(() => {\n  class MatPaginatedTabHeader {\n    /** The index of the active tab. */\n    get selectedIndex() {\n      return this._selectedIndex;\n    }\n    set selectedIndex(v) {\n      const value = isNaN(v) ? 0 : v;\n      if (this._selectedIndex != value) {\n        this._selectedIndexChanged = true;\n        this._selectedIndex = value;\n        if (this._keyManager) {\n          this._keyManager.updateActiveItem(value);\n        }\n      }\n    }\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._viewportRuler = _viewportRuler;\n      this._dir = _dir;\n      this._ngZone = _ngZone;\n      this._platform = _platform;\n      this._animationMode = _animationMode;\n      /** The distance in pixels that the tab labels should be translated to the left. */\n      this._scrollDistance = 0;\n      /** Whether the header should scroll to the selected index after the view has been checked. */\n      this._selectedIndexChanged = false;\n      /** Emits when the component is destroyed. */\n      this._destroyed = new Subject();\n      /** Whether the controls for pagination should be displayed */\n      this._showPaginationControls = false;\n      /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n      this._disableScrollAfter = true;\n      /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n      this._disableScrollBefore = true;\n      /** Stream that will stop the automated scrolling. */\n      this._stopScrolling = new Subject();\n      /**\n       * Whether pagination should be disabled. This can be used to avoid unnecessary\n       * layout recalculations if it's known that pagination won't be required.\n       */\n      this.disablePagination = false;\n      this._selectedIndex = 0;\n      /** Event emitted when the option is selected. */\n      this.selectFocusedIndex = new EventEmitter();\n      /** Event emitted when a label is focused. */\n      this.indexFocused = new EventEmitter();\n      // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n      _ngZone.runOutsideAngular(() => {\n        fromEvent(_elementRef.nativeElement, 'mouseleave').pipe(takeUntil(this._destroyed)).subscribe(() => {\n          this._stopInterval();\n        });\n      });\n    }\n    ngAfterViewInit() {\n      // We need to handle these events manually, because we want to bind passive event listeners.\n      fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._handlePaginatorPress('before');\n      });\n      fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._handlePaginatorPress('after');\n      });\n    }\n    ngAfterContentInit() {\n      const dirChange = this._dir ? this._dir.change : of('ltr');\n      const resize = this._viewportRuler.change(150);\n      const realign = () => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n      };\n      this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n      // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n      .skipPredicate(() => false);\n      this._keyManager.updateActiveItem(this._selectedIndex);\n      // Defer the first call in order to allow for slower browsers to lay out the elements.\n      // This helps in cases where the user lands directly on a page with paginated tabs.\n      // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n      // can hold up tests that are in a background tab.\n      this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n      // On dir change or window resize, realign the ink bar and update the orientation of\n      // the key manager if the direction has changed.\n      merge(dirChange, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n        // We need to defer this to give the browser some time to recalculate\n        // the element dimensions. The call has to be wrapped in `NgZone.run`,\n        // because the viewport change handler runs outside of Angular.\n        this._ngZone.run(() => {\n          Promise.resolve().then(() => {\n            // Clamp the scroll distance, because it can change with the number of tabs.\n            this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n            realign();\n          });\n        });\n        this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n      });\n      // If there is a change in the focus key manager we need to emit the `indexFocused`\n      // event in order to provide a public event that notifies about focus changes. Also we realign\n      // the tabs container by scrolling the new focused tab into the visible section.\n      this._keyManager.change.subscribe(newFocusIndex => {\n        this.indexFocused.emit(newFocusIndex);\n        this._setTabFocus(newFocusIndex);\n      });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n      if (typeof ResizeObserver !== 'function') {\n        return EMPTY;\n      }\n      return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n        const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n        tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n        return () => {\n          resizeObserver.disconnect();\n        };\n      }))),\n      // Skip the first emit since the resize observer emits when an item\n      // is observed for new items when the tab is already inserted\n      skip(1),\n      // Skip emissions where all the elements are invisible since we don't want\n      // the header to try and re-render with invalid measurements. See #25574.\n      filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n      // If the number of tab labels have changed, check if scrolling should be enabled\n      if (this._tabLabelCount != this._items.length) {\n        this.updatePagination();\n        this._tabLabelCount = this._items.length;\n        this._changeDetectorRef.markForCheck();\n      }\n      // If the selected index has changed, scroll to the label and check if the scrolling controls\n      // should be disabled.\n      if (this._selectedIndexChanged) {\n        this._scrollToLabel(this._selectedIndex);\n        this._checkScrollingControls();\n        this._alignInkBarToSelectedTab();\n        this._selectedIndexChanged = false;\n        this._changeDetectorRef.markForCheck();\n      }\n      // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n      // then translate the header to reflect this.\n      if (this._scrollDistanceChanged) {\n        this._updateTabScrollPosition();\n        this._scrollDistanceChanged = false;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n      // We don't handle any key bindings with a modifier key.\n      if (hasModifierKey(event)) {\n        return;\n      }\n      switch (event.keyCode) {\n        case ENTER:\n        case SPACE:\n          if (this.focusIndex !== this.selectedIndex) {\n            const item = this._items.get(this.focusIndex);\n            if (item && !item.disabled) {\n              this.selectFocusedIndex.emit(this.focusIndex);\n              this._itemSelected(event);\n            }\n          }\n          break;\n        default:\n          this._keyManager.onKeydown(event);\n      }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n      const textContent = this._elementRef.nativeElement.textContent;\n      // We need to diff the text content of the header, because the MutationObserver callback\n      // will fire even if the text content didn't change which is inefficient and is prone\n      // to infinite loops if a poorly constructed expression is passed in (see #14249).\n      if (textContent !== this._currentTextContent) {\n        this._currentTextContent = textContent || '';\n        // The content observer runs outside the `NgZone` by default, which\n        // means that we need to bring the callback back in ourselves.\n        this._ngZone.run(() => {\n          this.updatePagination();\n          this._alignInkBarToSelectedTab();\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n      this._checkPaginationEnabled();\n      this._checkScrollingControls();\n      this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n      return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n      if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n        return;\n      }\n      this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n      return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n      if (this._showPaginationControls) {\n        this._scrollToLabel(tabIndex);\n      }\n      if (this._items && this._items.length) {\n        this._items.toArray()[tabIndex].focus();\n        // Do not let the browser manage scrolling to focus the element, this will be handled\n        // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n        // should be the full width minus the offset width.\n        const containerEl = this._tabListContainer.nativeElement;\n        const dir = this._getLayoutDirection();\n        if (dir == 'ltr') {\n          containerEl.scrollLeft = 0;\n        } else {\n          containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n        }\n      }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n      if (this.disablePagination) {\n        return;\n      }\n      const scrollDistance = this.scrollDistance;\n      const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n      // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n      // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n      // and ripples will exceed the boundaries of the visible tab bar.\n      // See: https://github.com/angular/components/issues/10276\n      // We round the `transform` here, because transforms with sub-pixel precision cause some\n      // browsers to blur the content of the element.\n      this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n      // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n      // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n      // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n      // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n      if (this._platform.TRIDENT || this._platform.EDGE) {\n        this._tabListContainer.nativeElement.scrollLeft = 0;\n      }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n      return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n      this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n      const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n      // Move the scroll distance one-third the length of the tab list's viewport.\n      const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n      return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n      this._stopInterval();\n      this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n      if (this.disablePagination) {\n        return;\n      }\n      const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n      if (!selectedLabel) {\n        return;\n      }\n      // The view length is the visible width of the tab labels.\n      const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n      const {\n        offsetLeft,\n        offsetWidth\n      } = selectedLabel.elementRef.nativeElement;\n      let labelBeforePos, labelAfterPos;\n      if (this._getLayoutDirection() == 'ltr') {\n        labelBeforePos = offsetLeft;\n        labelAfterPos = labelBeforePos + offsetWidth;\n      } else {\n        labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n        labelBeforePos = labelAfterPos - offsetWidth;\n      }\n      const beforeVisiblePos = this.scrollDistance;\n      const afterVisiblePos = this.scrollDistance + viewLength;\n      if (labelBeforePos < beforeVisiblePos) {\n        // Scroll header to move label to the before direction\n        this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n      } else if (labelAfterPos > afterVisiblePos) {\n        // Scroll header to move label to the after direction\n        this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n      }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n      if (this.disablePagination) {\n        this._showPaginationControls = false;\n      } else {\n        const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n        if (!isEnabled) {\n          this.scrollDistance = 0;\n        }\n        if (isEnabled !== this._showPaginationControls) {\n          this._changeDetectorRef.markForCheck();\n        }\n        this._showPaginationControls = isEnabled;\n      }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n      if (this.disablePagination) {\n        this._disableScrollAfter = this._disableScrollBefore = true;\n      } else {\n        // Check if the pagination arrows should be activated.\n        this._disableScrollBefore = this.scrollDistance == 0;\n        this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n      const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n      const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n      return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n      const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n      const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n      if (selectedLabelWrapper) {\n        this._inkBar.alignToElement(selectedLabelWrapper);\n      } else {\n        this._inkBar.hide();\n      }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n      this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n      // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n      // null check the `button`, but we do it so we don't break tests that use fake events.\n      if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n        return;\n      }\n      // Avoid overlapping timers.\n      this._stopInterval();\n      // Start a timer after the delay and keep firing based on the interval.\n      timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n      // Keep the timer going until something tells it to stop or the component is destroyed.\n      .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n        const {\n          maxScrollDistance,\n          distance\n        } = this._scrollHeader(direction);\n        // Stop the timer if we've reached the start or the end.\n        if (distance === 0 || distance >= maxScrollDistance) {\n          this._stopInterval();\n        }\n      });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n      if (this.disablePagination) {\n        return {\n          maxScrollDistance: 0,\n          distance: 0\n        };\n      }\n      const maxScrollDistance = this._getMaxScrollDistance();\n      this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n      // Mark that the scroll distance has changed so that after the view is checked, the CSS\n      // transformation can move the header.\n      this._scrollDistanceChanged = true;\n      this._checkScrollingControls();\n      return {\n        maxScrollDistance,\n        distance: this._scrollDistance\n      };\n    }\n    static {\n      this.ɵfac = function MatPaginatedTabHeader_Factory(t) {\n        return new (t || MatPaginatedTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatPaginatedTabHeader,\n        inputs: {\n          disablePagination: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disablePagination\", \"disablePagination\", booleanAttribute],\n          selectedIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectedIndex\", \"selectedIndex\", numberAttribute]\n        },\n        outputs: {\n          selectFocusedIndex: \"selectFocusedIndex\",\n          indexFocused: \"indexFocused\"\n        },\n        features: [i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return MatPaginatedTabHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nlet MatTabHeader = /*#__PURE__*/(() => {\n  class MatTabHeader extends MatPaginatedTabHeader {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n      super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n      /** Whether the ripple effect is disabled or not. */\n      this.disableRipple = false;\n    }\n    ngAfterContentInit() {\n      this._inkBar = new MatInkBar(this._items);\n      super.ngAfterContentInit();\n    }\n    _itemSelected(event) {\n      event.preventDefault();\n    }\n    static {\n      this.ɵfac = function MatTabHeader_Factory(t) {\n        return new (t || MatTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTabHeader,\n        selectors: [[\"mat-tab-header\"]],\n        contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n          }\n        },\n        viewQuery: function MatTabHeader_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c1, 7);\n            i0.ɵɵviewQuery(_c2, 7);\n            i0.ɵɵviewQuery(_c3, 7);\n            i0.ɵɵviewQuery(_c4, 5);\n            i0.ɵɵviewQuery(_c5, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-tab-header\"],\n        hostVars: 4,\n        hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n          }\n        },\n        inputs: {\n          disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 13,\n        vars: 10,\n        consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\", \"disabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-labels\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\", \"disabled\"]],\n        template: function MatTabHeader_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"button\", 5, 0);\n            i0.ɵɵlistener(\"click\", function MatTabHeader_Template_button_click_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n            })(\"mousedown\", function MatTabHeader_Template_button_mousedown_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n            })(\"touchend\", function MatTabHeader_Template_button_touchend_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._stopInterval());\n            });\n            i0.ɵɵelement(2, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 7, 1);\n            i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handleKeydown($event));\n            });\n            i0.ɵɵelementStart(5, \"div\", 8, 2);\n            i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onContentChanges());\n            });\n            i0.ɵɵelementStart(7, \"div\", 9, 3);\n            i0.ɵɵprojection(9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"button\", 10, 4);\n            i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_button_mousedown_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n            })(\"click\", function MatTabHeader_Template_button_click_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n            })(\"touchend\", function MatTabHeader_Template_button_touchend_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._stopInterval());\n            });\n            i0.ɵɵelement(12, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n            i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n            i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n          }\n        },\n        dependencies: [MatRipple, CdkObserveContent],\n        styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return MatTabHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = /*#__PURE__*/new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: /*#__PURE__*/trigger('translateTab', [\n  /*#__PURE__*/\n  // Transitions to `none` instead of 0, because some browsers might blur the content.\n  state('center, void, left-origin-center, right-origin-center', /*#__PURE__*/style({\n    transform: 'none'\n  })),\n  /*#__PURE__*/\n  // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  // in order to ensure that the element has a height before its state changes. This is\n  // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  // not have a static height and is not rendered. See related issue: #9465\n  state('left', /*#__PURE__*/style({\n    transform: 'translate3d(-100%, 0, 0)',\n    minHeight: '1px',\n    // Normally this is redundant since we detach the content from the DOM, but if the user\n    // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    visibility: 'hidden'\n  })), /*#__PURE__*/state('right', /*#__PURE__*/style({\n    transform: 'translate3d(100%, 0, 0)',\n    minHeight: '1px',\n    visibility: 'hidden'\n  })), /*#__PURE__*/transition('* => left, * => right, left => center, right => center', /*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')), /*#__PURE__*/transition('void => left-origin-center', [/*#__PURE__*/style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  }), /*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')]), /*#__PURE__*/transition('void => right-origin-center', [/*#__PURE__*/style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  }), /*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')])])\n};\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nlet MatTabBodyPortal = /*#__PURE__*/(() => {\n  class MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n      super(componentFactoryResolver, viewContainerRef, _document);\n      this._host = _host;\n      /** Subscription to events for when the tab body begins centering. */\n      this._centeringSub = Subscription.EMPTY;\n      /** Subscription to events for when the tab body finishes leaving from center position. */\n      this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n      super.ngOnInit();\n      this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition(this._host._position))).subscribe(isCentering => {\n        if (isCentering && !this.hasAttached()) {\n          this.attach(this._host._content);\n        }\n      });\n      this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n        if (!this._host.preserveContent) {\n          this.detach();\n        }\n      });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._centeringSub.unsubscribe();\n      this._leavingSub.unsubscribe();\n    }\n    static {\n      this.ɵfac = function MatTabBodyPortal_Factory(t) {\n        return new (t || MatTabBodyPortal)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(forwardRef(() => MatTabBody)), i0.ɵɵdirectiveInject(DOCUMENT));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatTabBodyPortal,\n        selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatTabBodyPortal;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nlet MatTabBody = /*#__PURE__*/(() => {\n  class MatTabBody {\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n      this._positionIndex = position;\n      this._computePositionAnimationState();\n    }\n    constructor(_elementRef, _dir, changeDetectorRef) {\n      this._elementRef = _elementRef;\n      this._dir = _dir;\n      /** Subscription to the directionality change observable. */\n      this._dirChangeSubscription = Subscription.EMPTY;\n      /** Emits when an animation on the tab is complete. */\n      this._translateTabComplete = new Subject();\n      /** Event emitted when the tab begins to animate towards the center as the active tab. */\n      this._onCentering = new EventEmitter();\n      /** Event emitted before the centering of the tab begins. */\n      this._beforeCentering = new EventEmitter();\n      /** Event emitted before the centering of the tab begins. */\n      this._afterLeavingCenter = new EventEmitter();\n      /** Event emitted when the tab completes its animation towards the center. */\n      this._onCentered = new EventEmitter(true);\n      // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n      // anyway to prevent the animations module from throwing an error if the body is used on its own.\n      /** Duration for the tab's animation. */\n      this.animationDuration = '500ms';\n      /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n      this.preserveContent = false;\n      if (_dir) {\n        this._dirChangeSubscription = _dir.change.subscribe(dir => {\n          this._computePositionAnimationState(dir);\n          changeDetectorRef.markForCheck();\n        });\n      }\n      // Ensure that we get unique animation events, because the `.done` callback can get\n      // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n      this._translateTabComplete.pipe(distinctUntilChanged((x, y) => {\n        return x.fromState === y.fromState && x.toState === y.toState;\n      })).subscribe(event => {\n        // If the transition to the center is complete, emit an event.\n        if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n          this._onCentered.emit();\n        }\n        if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n          this._afterLeavingCenter.emit();\n        }\n      });\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n    ngOnInit() {\n      if (this._position == 'center' && this.origin != null) {\n        this._position = this._computePositionFromOrigin(this.origin);\n      }\n    }\n    ngOnDestroy() {\n      this._dirChangeSubscription.unsubscribe();\n      this._translateTabComplete.complete();\n    }\n    _onTranslateTabStarted(event) {\n      const isCentering = this._isCenterPosition(event.toState);\n      this._beforeCentering.emit(isCentering);\n      if (isCentering) {\n        this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n      }\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition(position) {\n      return position == 'center' || position == 'left-origin-center' || position == 'right-origin-center';\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n      if (this._positionIndex < 0) {\n        this._position = dir == 'ltr' ? 'left' : 'right';\n      } else if (this._positionIndex > 0) {\n        this._position = dir == 'ltr' ? 'right' : 'left';\n      } else {\n        this._position = 'center';\n      }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n    _computePositionFromOrigin(origin) {\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr' && origin <= 0 || dir == 'rtl' && origin > 0) {\n        return 'left-origin-center';\n      }\n      return 'right-origin-center';\n    }\n    static {\n      this.ɵfac = function MatTabBody_Factory(t) {\n        return new (t || MatTabBody)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTabBody,\n        selectors: [[\"mat-tab-body\"]],\n        viewQuery: function MatTabBody_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-tab-body\"],\n        inputs: {\n          _content: [i0.ɵɵInputFlags.None, \"content\", \"_content\"],\n          origin: \"origin\",\n          animationDuration: \"animationDuration\",\n          preserveContent: \"preserveContent\",\n          position: \"position\"\n        },\n        outputs: {\n          _onCentering: \"_onCentering\",\n          _beforeCentering: \"_beforeCentering\",\n          _afterLeavingCenter: \"_afterLeavingCenter\",\n          _onCentered: \"_onCentered\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 6,\n        consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"matTabBodyHost\", \"\"]],\n        template: function MatTabBody_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1, 0);\n            i0.ɵɵlistener(\"@translateTab.start\", function MatTabBody_Template_div_animation_translateTab_start_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onTranslateTabStarted($event));\n            })(\"@translateTab.done\", function MatTabBody_Template_div_animation_translateTab_done_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._translateTabComplete.next($event));\n            });\n            i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"@translateTab\", i0.ɵɵpureFunction2(3, _c7, ctx._position, i0.ɵɵpureFunction1(1, _c6, ctx.animationDuration)));\n          }\n        },\n        dependencies: [MatTabBodyPortal, CdkScrollable],\n        styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matTabsAnimations.translateTab]\n        }\n      });\n    }\n  }\n  return MatTabBody;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n/** Boolean constant that determines whether the tab group supports the `backgroundColor` input */\nconst ENABLE_BACKGROUND_INPUT = true;\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nlet MatTabGroup = /*#__PURE__*/(() => {\n  class MatTabGroup {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n      return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(value) {\n      this._fitInkBarToContent = value;\n      this._changeDetectorRef.markForCheck();\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n      return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n      this._indexToSelect = isNaN(value) ? null : value;\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n      return this._animationDuration;\n    }\n    set animationDuration(value) {\n      const stringValue = value + '';\n      this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n      return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n      this._contentTabIndex = isNaN(value) ? null : value;\n    }\n    /**\n     * Background color of the tab group.\n     * @deprecated The background color should be customized through Sass theming APIs.\n     * @breaking-change 20.0.0 Remove this input\n     */\n    get backgroundColor() {\n      return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n      if (!ENABLE_BACKGROUND_INPUT) {\n        throw new Error(`mat-tab-group background color must be set through the Sass theming API`);\n      }\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n      if (value) {\n        classList.add('mat-tabs-with-background', `mat-background-${value}`);\n      }\n      this._backgroundColor = value;\n    }\n    constructor(_elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._animationMode = _animationMode;\n      /** All of the tabs that belong to the group. */\n      this._tabs = new QueryList();\n      /** The tab index that should be selected after the content has been checked. */\n      this._indexToSelect = 0;\n      /** Index of the tab that was focused last. */\n      this._lastFocusedTabIndex = null;\n      /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n      this._tabBodyWrapperHeight = 0;\n      /** Subscription to tabs being added/removed. */\n      this._tabsSubscription = Subscription.EMPTY;\n      /** Subscription to changes in the tab labels. */\n      this._tabLabelSubscription = Subscription.EMPTY;\n      this._fitInkBarToContent = false;\n      /** Whether tabs should be stretched to fill the header. */\n      this.stretchTabs = true;\n      /** Whether the tab group should grow to the size of the active tab. */\n      this.dynamicHeight = false;\n      this._selectedIndex = null;\n      /** Position of the tab header. */\n      this.headerPosition = 'above';\n      /**\n       * Whether pagination should be disabled. This can be used to avoid unnecessary\n       * layout recalculations if it's known that pagination won't be required.\n       */\n      this.disablePagination = false;\n      /** Whether ripples in the tab group are disabled. */\n      this.disableRipple = false;\n      /**\n       * By default tabs remove their content from the DOM while it's off-screen.\n       * Setting this to `true` will keep it in the DOM which will prevent elements\n       * like iframes and videos from reloading next time it comes back into the view.\n       */\n      this.preserveContent = false;\n      /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n      this.selectedIndexChange = new EventEmitter();\n      /** Event emitted when focus has changed within a tab group. */\n      this.focusChange = new EventEmitter();\n      /** Event emitted when the body animation has completed */\n      this.animationDone = new EventEmitter();\n      /** Event emitted when the tab selection has changed. */\n      this.selectedTabChange = new EventEmitter(true);\n      /** Whether the tab group is rendered on the server. */\n      this._isServer = !inject(Platform).isBrowser;\n      this._groupId = nextId++;\n      this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n      this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n      this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n      if (defaultConfig?.contentTabIndex != null) {\n        this.contentTabIndex = defaultConfig.contentTabIndex;\n      }\n      this.preserveContent = !!defaultConfig?.preserveContent;\n      this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n      this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n      // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n      // the amount of tabs changes before the actual change detection runs.\n      const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // If there is a change in selected index, emit a change event. Should not trigger if\n      // the selected index has not yet been initialized.\n      if (this._selectedIndex != indexToSelect) {\n        const isFirstRun = this._selectedIndex == null;\n        if (!isFirstRun) {\n          this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          // Preserve the height so page doesn't scroll up during tab change.\n          // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n          const wrapper = this._tabBodyWrapper.nativeElement;\n          wrapper.style.minHeight = wrapper.clientHeight + 'px';\n        }\n        // Changing these values after change detection has run\n        // since the checked content may contain references to them.\n        Promise.resolve().then(() => {\n          this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n          if (!isFirstRun) {\n            this.selectedIndexChange.emit(indexToSelect);\n            // Clear the min-height, this was needed during tab change to avoid\n            // unnecessary scrolling.\n            this._tabBodyWrapper.nativeElement.style.minHeight = '';\n          }\n        });\n      }\n      // Setup the position for each tab and optionally setup an origin on the next selected tab.\n      this._tabs.forEach((tab, index) => {\n        tab.position = index - indexToSelect;\n        // If there is already a selected tab, then set up an origin for the next selected tab\n        // if it doesn't have one already.\n        if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n          tab.origin = indexToSelect - this._selectedIndex;\n        }\n      });\n      if (this._selectedIndex !== indexToSelect) {\n        this._selectedIndex = indexToSelect;\n        this._lastFocusedTabIndex = null;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    ngAfterContentInit() {\n      this._subscribeToAllTabChanges();\n      this._subscribeToTabLabels();\n      // Subscribe to changes in the amount of tabs, in order to be\n      // able to re-render the content as new tabs are added or removed.\n      this._tabsSubscription = this._tabs.changes.subscribe(() => {\n        const indexToSelect = this._clampTabIndex(this._indexToSelect);\n        // Maintain the previously-selected tab if a new tab is added or removed and there is no\n        // explicit change that selects a different tab.\n        if (indexToSelect === this._selectedIndex) {\n          const tabs = this._tabs.toArray();\n          let selectedTab;\n          for (let i = 0; i < tabs.length; i++) {\n            if (tabs[i].isActive) {\n              // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n              // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n              // adding a tab within the `selectedIndexChange` event.\n              this._indexToSelect = this._selectedIndex = i;\n              this._lastFocusedTabIndex = null;\n              selectedTab = tabs[i];\n              break;\n            }\n          }\n          // If we haven't found an active tab and a tab exists at the selected index, it means\n          // that the active tab was swapped out. Since this won't be picked up by the rendering\n          // loop in `ngAfterContentChecked`, we need to sync it up manually.\n          if (!selectedTab && tabs[indexToSelect]) {\n            Promise.resolve().then(() => {\n              tabs[indexToSelect].isActive = true;\n              this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n            });\n          }\n        }\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n      // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n      // some that are inside of nested tab groups. We filter them out manually by checking that\n      // the closest group to the tab is the current one.\n      this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n        this._tabs.reset(tabs.filter(tab => {\n          return tab._closestTabGroup === this || !tab._closestTabGroup;\n        }));\n        this._tabs.notifyOnChanges();\n      });\n    }\n    ngOnDestroy() {\n      this._tabs.destroy();\n      this._tabsSubscription.unsubscribe();\n      this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n      if (this._tabHeader) {\n        this._tabHeader._alignInkBarToSelectedTab();\n      }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n      if (this._tabHeader) {\n        this._tabHeader.updatePagination();\n      }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n      const header = this._tabHeader;\n      if (header) {\n        header.focusIndex = index;\n      }\n    }\n    _focusChanged(index) {\n      this._lastFocusedTabIndex = index;\n      this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n      const event = new MatTabChangeEvent();\n      event.index = index;\n      if (this._tabs && this._tabs.length) {\n        event.tab = this._tabs.toArray()[index];\n      }\n      return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n      if (this._tabLabelSubscription) {\n        this._tabLabelSubscription.unsubscribe();\n      }\n      this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n      // Note the `|| 0`, which ensures that values like NaN can't get through\n      // and which would otherwise throw the component into an infinite loop\n      // (since Math.max(NaN, 0) === NaN).\n      return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(i) {\n      return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(i) {\n      return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n      if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n        return;\n      }\n      const wrapper = this._tabBodyWrapper.nativeElement;\n      wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n      // This conditional forces the browser to paint the height so that\n      // the animation to the new height can have an origin.\n      if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n        wrapper.style.height = tabHeight + 'px';\n      }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n      const wrapper = this._tabBodyWrapper.nativeElement;\n      this._tabBodyWrapperHeight = wrapper.clientHeight;\n      wrapper.style.height = '';\n      this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n      tabHeader.focusIndex = index;\n      if (!tab.disabled) {\n        this.selectedIndex = index;\n      }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n      const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n      return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n      // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n      // can cause the tab to be moved out from under the pointer, interrupting the\n      // click sequence (see #21898). We don't need to scroll the tab into view for\n      // such cases anyway, because it will be done when the tab becomes selected.\n      if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n        this._tabHeader.focusIndex = index;\n      }\n    }\n    static {\n      this.ɵfac = function MatTabGroup_Factory(t) {\n        return new (t || MatTabGroup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTabGroup,\n        selectors: [[\"mat-tab-group\"]],\n        contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n          }\n        },\n        viewQuery: function MatTabGroup_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c8, 5);\n            i0.ɵɵviewQuery(_c9, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-tab-group\"],\n        hostVars: 10,\n        hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n            i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n            i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n          }\n        },\n        inputs: {\n          color: \"color\",\n          fitInkBarToContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n          stretchTabs: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n          dynamicHeight: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"dynamicHeight\", \"dynamicHeight\", booleanAttribute],\n          selectedIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n          headerPosition: \"headerPosition\",\n          animationDuration: \"animationDuration\",\n          contentTabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"contentTabIndex\", \"contentTabIndex\", numberAttribute],\n          disablePagination: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disablePagination\", \"disablePagination\", booleanAttribute],\n          disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n          preserveContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"preserveContent\", \"preserveContent\", booleanAttribute],\n          backgroundColor: \"backgroundColor\"\n        },\n        outputs: {\n          selectedIndexChange: \"selectedIndexChange\",\n          focusChange: \"focusChange\",\n          animationDone: \"animationDone\",\n          selectedTabChange: \"selectedTabChange\"\n        },\n        exportAs: [\"matTabGroup\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_TAB_GROUP,\n          useExisting: MatTabGroup\n        }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 9,\n        vars: 6,\n        consts: [[\"tabHeader\", \"\"], [\"tabBodyWrapper\", \"\"], [\"tabNode\", \"\"], [3, \"indexFocused\", \"selectFocusedIndex\", \"selectedIndex\", \"disableRipple\", \"disablePagination\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-mdc-focus-indicator\", 3, \"id\", \"mdc-tab--active\", \"class\", \"disabled\", \"fitInkBarToContent\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"role\", \"tabpanel\", 3, \"id\", \"mat-mdc-tab-body-active\", \"class\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-mdc-focus-indicator\", 3, \"click\", \"cdkFocusChange\", \"id\", \"disabled\", \"fitInkBarToContent\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"_onCentered\", \"_onCentering\", \"id\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\"]],\n        template: function MatTabGroup_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"mat-tab-header\", 3, 0);\n            i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._focusChanged($event));\n            })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectedIndex = $event);\n            });\n            i0.ɵɵrepeaterCreate(2, MatTabGroup_For_3_Template, 8, 17, \"div\", 4, i0.ɵɵrepeaterTrackByIdentity);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, MatTabGroup_Conditional_4_Template, 1, 0);\n            i0.ɵɵelementStart(5, \"div\", 5, 1);\n            i0.ɵɵrepeaterCreate(7, MatTabGroup_For_8_Template, 1, 13, \"mat-tab-body\", 6, i0.ɵɵrepeaterTrackByIdentity);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination);\n            i0.ɵɵadvance(2);\n            i0.ɵɵrepeater(ctx._tabs);\n            i0.ɵɵadvance(2);\n            i0.ɵɵconditional(4, ctx._isServer ? 4 : -1);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵrepeater(ctx._tabs);\n          }\n        },\n        dependencies: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n        styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return MatTabGroup;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {}\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nlet MatTabNav = /*#__PURE__*/(() => {\n  class MatTabNav extends MatPaginatedTabHeader {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n      return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(value) {\n      this._fitInkBarToContent.next(value);\n      this._changeDetectorRef.markForCheck();\n    }\n    get animationDuration() {\n      return this._animationDuration;\n    }\n    set animationDuration(value) {\n      const stringValue = value + '';\n      this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    /** Background color of the tab nav. */\n    get backgroundColor() {\n      return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n      if (value) {\n        classList.add('mat-tabs-with-background', `mat-background-${value}`);\n      }\n      this._backgroundColor = value;\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n      super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n      this._fitInkBarToContent = new BehaviorSubject(false);\n      /** Whether tabs should be stretched to fill the header. */\n      this.stretchTabs = true;\n      /** Whether the ripple effect is disabled or not. */\n      this.disableRipple = false;\n      /** Theme color of the nav bar. */\n      this.color = 'primary';\n      this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n      this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n      this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    _itemSelected() {\n      // noop\n    }\n    ngAfterContentInit() {\n      this._inkBar = new MatInkBar(this._items);\n      // We need this to run before the `changes` subscription in parent to ensure that the\n      // selectedIndex is up-to-date by the time the super class starts looking for it.\n      this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        this.updateActiveLink();\n      });\n      super.ngAfterContentInit();\n    }\n    ngAfterViewInit() {\n      if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n      }\n      super.ngAfterViewInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n      if (!this._items) {\n        return;\n      }\n      const items = this._items.toArray();\n      for (let i = 0; i < items.length; i++) {\n        if (items[i].active) {\n          this.selectedIndex = i;\n          this._changeDetectorRef.markForCheck();\n          if (this.tabPanel) {\n            this.tabPanel._activeTabId = items[i].id;\n          }\n          return;\n        }\n      }\n      // The ink bar should hide itself if no items are active.\n      this.selectedIndex = -1;\n      this._inkBar.hide();\n    }\n    _getRole() {\n      return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n    static {\n      this.ɵfac = function MatTabNav_Factory(t) {\n        return new (t || MatTabNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTabNav,\n        selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n        contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n          }\n        },\n        viewQuery: function MatTabNav_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c1, 7);\n            i0.ɵɵviewQuery(_c2, 7);\n            i0.ɵɵviewQuery(_c3, 7);\n            i0.ɵɵviewQuery(_c4, 5);\n            i0.ɵɵviewQuery(_c5, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n        hostVars: 17,\n        hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"role\", ctx._getRole());\n            i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n            i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n          }\n        },\n        inputs: {\n          fitInkBarToContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n          stretchTabs: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n          animationDuration: \"animationDuration\",\n          backgroundColor: \"backgroundColor\",\n          disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n          color: \"color\",\n          tabPanel: \"tabPanel\"\n        },\n        exportAs: [\"matTabNavBar\", \"matTabNav\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        attrs: _c10,\n        ngContentSelectors: _c0,\n        decls: 13,\n        vars: 8,\n        consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\", \"disabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-links\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\", \"disabled\"]],\n        template: function MatTabNav_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"button\", 5, 0);\n            i0.ɵɵlistener(\"click\", function MatTabNav_Template_button_click_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n            })(\"mousedown\", function MatTabNav_Template_button_mousedown_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n            })(\"touchend\", function MatTabNav_Template_button_touchend_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._stopInterval());\n            });\n            i0.ɵɵelement(2, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 7, 1);\n            i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handleKeydown($event));\n            });\n            i0.ɵɵelementStart(5, \"div\", 8, 2);\n            i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onContentChanges());\n            });\n            i0.ɵɵelementStart(7, \"div\", 9, 3);\n            i0.ɵɵprojection(9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"button\", 10, 4);\n            i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_button_mousedown_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n            })(\"click\", function MatTabNav_Template_button_click_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n            })(\"touchend\", function MatTabNav_Template_button_touchend_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._stopInterval());\n            });\n            i0.ɵɵelement(12, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n            i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n            i0.ɵɵadvance(10);\n            i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n            i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n          }\n        },\n        dependencies: [MatRipple, CdkObserveContent],\n        styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return MatTabNav;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nlet MatTabLink = /*#__PURE__*/(() => {\n  class MatTabLink extends InkBarItem {\n    /** Whether the link is active. */\n    get active() {\n      return this._isActive;\n    }\n    set active(value) {\n      if (value !== this._isActive) {\n        this._isActive = value;\n        this._tabNavBar.updateActiveLink();\n      }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n      return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n    }\n    constructor(_tabNavBar, /** @docs-private */elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n      super();\n      this._tabNavBar = _tabNavBar;\n      this.elementRef = elementRef;\n      this._focusMonitor = _focusMonitor;\n      this._destroyed = new Subject();\n      /** Whether the tab link is active or not. */\n      this._isActive = false;\n      /** Whether the tab link is disabled. */\n      this.disabled = false;\n      /** Whether ripples are disabled on the tab link. */\n      this.disableRipple = false;\n      this.tabIndex = 0;\n      /** Unique id for the tab. */\n      this.id = `mat-tab-link-${nextUniqueId++}`;\n      this.rippleConfig = globalRippleOptions || {};\n      this.tabIndex = parseInt(tabIndex) || 0;\n      if (animationMode === 'NoopAnimations') {\n        this.rippleConfig.animation = {\n          enterDuration: 0,\n          exitDuration: 0\n        };\n      }\n      _tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n        this.fitInkBarToContent = fitInkBarToContent;\n      });\n    }\n    /** Focuses the tab link. */\n    focus() {\n      this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n      this._destroyed.next();\n      this._destroyed.complete();\n      super.ngOnDestroy();\n      this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n      // Since we allow navigation through tabbing in the nav bar, we\n      // have to update the focused index whenever the link receives focus.\n      this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n      if (event.keyCode === SPACE || event.keyCode === ENTER) {\n        if (this.disabled) {\n          event.preventDefault();\n        } else if (this._tabNavBar.tabPanel) {\n          // Only prevent the default action on space since it can scroll the page.\n          // Don't prevent enter since it can break link navigation.\n          if (event.keyCode === SPACE) {\n            event.preventDefault();\n          }\n          this.elementRef.nativeElement.click();\n        }\n      }\n    }\n    _getAriaControls() {\n      return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n      if (this._tabNavBar.tabPanel) {\n        return this.active ? 'true' : 'false';\n      } else {\n        return this.elementRef.nativeElement.getAttribute('aria-selected');\n      }\n    }\n    _getAriaCurrent() {\n      return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n      return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    _getTabIndex() {\n      if (this._tabNavBar.tabPanel) {\n        return this._isActive && !this.disabled ? 0 : -1;\n      } else {\n        return this.disabled ? -1 : this.tabIndex;\n      }\n    }\n    static {\n      this.ɵfac = function MatTabLink_Factory(t) {\n        return new (t || MatTabLink)(i0.ɵɵdirectiveInject(MatTabNav), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTabLink,\n        selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n        hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-mdc-focus-indicator\"],\n        hostVars: 11,\n        hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n              return ctx._handleFocus();\n            })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n              return ctx._handleKeydown($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._getTabIndex())(\"role\", ctx._getRole());\n            i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n          }\n        },\n        inputs: {\n          active: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"active\", \"active\", booleanAttribute],\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n          disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n          tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n          id: \"id\"\n        },\n        exportAs: [\"matTabLink\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        attrs: _c11,\n        ngContentSelectors: _c0,\n        decls: 5,\n        vars: 2,\n        consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n        template: function MatTabLink_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n            i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n            i0.ɵɵprojection(4);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n          }\n        },\n        dependencies: [MatRipple],\n        styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatTabLink;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nlet MatTabNavPanel = /*#__PURE__*/(() => {\n  class MatTabNavPanel {\n    constructor() {\n      /** Unique id for the tab panel. */\n      this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n    static {\n      this.ɵfac = function MatTabNavPanel_Factory(t) {\n        return new (t || MatTabNavPanel)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatTabNavPanel,\n        selectors: [[\"mat-tab-nav-panel\"]],\n        hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n        hostVars: 2,\n        hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n          }\n        },\n        inputs: {\n          id: \"id\"\n        },\n        exportAs: [\"matTabNavPanel\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 1,\n        vars: 0,\n        template: function MatTabNavPanel_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵprojection(0);\n          }\n        },\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatTabNavPanel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatTabsModule = /*#__PURE__*/(() => {\n  class MatTabsModule {\n    static {\n      this.ɵfac = function MatTabsModule_Factory(t) {\n        return new (t || MatTabsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatTabsModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatTabsModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n//# sourceMappingURL=tabs.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}