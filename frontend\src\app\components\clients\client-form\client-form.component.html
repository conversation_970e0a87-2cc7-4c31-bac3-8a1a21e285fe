<div class="client-form-container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
        {{ isEditMode ? 'Modifier le Client' : 'Nouveau Client' }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ isEditMode ? 'Modifiez les informations du client' : 'Ajoutez un nouveau client à votre portefeuille' }}
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Chargement des données...</p>
      </div>

      <form [formGroup]="clientForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-grid">
          <!-- Basic Information Section -->
          <div class="form-section">
            <h3>Informations générales</h3>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nom de l'entreprise *</mat-label>
                <input matInput formControlName="name" placeholder="Ex: TechCorp Solutions">
                <mat-error>{{ getFieldError('name') }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Personne de contact *</mat-label>
                <input matInput formControlName="contactPerson" placeholder="Ex: Marie Dubois">
                <mat-error>{{ getFieldError('contactPerson') }}</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Secteur d'activité *</mat-label>
                <mat-select formControlName="industry">
                  <mat-option *ngFor="let industry of industries" [value]="industry">
                    {{ industry }}
                  </mat-option>
                </mat-select>
                <mat-error>{{ getFieldError('industry') }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Email *</mat-label>
                <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                <mat-icon matSuffix>email</mat-icon>
                <mat-error>{{ getFieldError('email') }}</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Téléphone *</mat-label>
                <input matInput formControlName="phone" placeholder="+33 1 23 45 67 89">
                <mat-icon matSuffix>phone</mat-icon>
                <mat-error>{{ getFieldError('phone') }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Adresse *</mat-label>
                <textarea matInput formControlName="address" rows="2" 
                         placeholder="123 Avenue des Champs-Élysées, 75008 Paris"></textarea>
                <mat-error>{{ getFieldError('address') }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Site web</mat-label>
                <input matInput formControlName="website" placeholder="https://www.entreprise.com">
                <mat-icon matSuffix>link</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Statut *</mat-label>
                <mat-select formControlName="status">
                  <mat-option *ngFor="let status of statuses" [value]="status.value">
                    {{ status.label }}
                  </mat-option>
                </mat-select>
                <mat-error>{{ getFieldError('status') }}</mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Additional Information Section -->
          <div class="form-section">
            <h3>Informations complémentaires</h3>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Notes</mat-label>
                <textarea matInput formControlName="notes" rows="4" 
                         placeholder="Notes sur le client, historique, préférences..."></textarea>
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-actions">
          <button type="button" mat-button (click)="onCancel()" [disabled]="isSaving">
            Annuler
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="isSaving">
            <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
            <mat-icon *ngIf="!isSaving">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
            {{ isSaving ? 'Enregistrement...' : (isEditMode ? 'Modifier' : 'Créer') }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
